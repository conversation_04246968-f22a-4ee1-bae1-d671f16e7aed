module.exports =
/******/ (function(modules) { // webpackBootstrap
/******/ 	// The module cache
/******/ 	var installedModules = {};

/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {

/******/ 		// Check if module is in cache
/******/ 		if(installedModules[moduleId])
/******/ 			return installedModules[moduleId].exports;

/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = installedModules[moduleId] = {
/******/ 			exports: {},
/******/ 			id: moduleId,
/******/ 			loaded: false
/******/ 		};

/******/ 		// Execute the module function
/******/ 		modules[moduleId].call(module.exports, module, module.exports, __webpack_require__);

/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;

/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}


/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = modules;

/******/ 	// expose the module cache
/******/ 	__webpack_require__.c = installedModules;

/******/ 	// __webpack_public_path__
/******/ 	__webpack_require__.p = "";

/******/ 	// Load entry module and return exports
/******/ 	return __webpack_require__(0);
/******/ })
/************************************************************************/
/******/ ([
/* 0 */
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	Object.defineProperty(exports, "__esModule", {
	  value: true
	});

	var _injector = __webpack_require__(1);

	Object.defineProperty(exports, 'Injector', {
	  enumerable: true,
	  get: function get() {
	    return _injector.Injector;
	  }
	});

	var _annotations = __webpack_require__(2);

	Object.defineProperty(exports, 'annotate', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.annotate;
	  }
	});
	Object.defineProperty(exports, 'Inject', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.Inject;
	  }
	});
	Object.defineProperty(exports, 'InjectLazy', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.InjectLazy;
	  }
	});
	Object.defineProperty(exports, 'InjectPromise', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.InjectPromise;
	  }
	});
	Object.defineProperty(exports, 'Provide', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.Provide;
	  }
	});
	Object.defineProperty(exports, 'ProvidePromise', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.ProvidePromise;
	  }
	});
	Object.defineProperty(exports, 'SuperConstructor', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.SuperConstructor;
	  }
	});
	Object.defineProperty(exports, 'TransientScope', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.TransientScope;
	  }
	});
	Object.defineProperty(exports, 'ClassProvider', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.ClassProvider;
	  }
	});
	Object.defineProperty(exports, 'FactoryProvider', {
	  enumerable: true,
	  get: function get() {
	    return _annotations.FactoryProvider;
	  }
	});

/***/ },
/* 1 */
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	Object.defineProperty(exports, "__esModule", {
	  value: true
	});
	exports.Injector = undefined;

	var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

	var _annotations = __webpack_require__(2);

	var _util = __webpack_require__(3);

	var _profiler = __webpack_require__(4);

	var _providers = __webpack_require__(6);

	function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

	function constructResolvingMessage(resolving, token) {
	  // If a token is passed in, add it into the resolving array.
	  // We need to check arguments.length because it can be null/undefined.
	  if (arguments.length > 1) {
	    resolving.push(token);
	  }

	  if (resolving.length > 1) {
	    return ' (' + resolving.map(_util.toString).join(' -> ') + ')';
	  }

	  return '';
	}

	// Injector encapsulate a life scope.
	// There is exactly one instance for given token in given injector.
	//
	// All the state is immutable, the only state changes is the cache. There is however no way to produce different instance under given token. In that sense it is immutable.
	//
	// Injector is responsible for:
	// - resolving tokens into
	//   - provider
	//   - value (cache/calling provider)
	// - dealing with isPromise
	// - dealing with isLazy
	// - loading different "providers" and modules

	var Injector = function () {
	  function Injector() {
	    var modules = arguments.length <= 0 || arguments[0] === undefined ? [] : arguments[0];
	    var parentInjector = arguments.length <= 1 || arguments[1] === undefined ? null : arguments[1];
	    var providers = arguments.length <= 2 || arguments[2] === undefined ? new Map() : arguments[2];
	    var scopes = arguments.length <= 3 || arguments[3] === undefined ? [] : arguments[3];

	    _classCallCheck(this, Injector);

	    this._cache = new Map();
	    this._providers = providers;
	    this._parent = parentInjector;
	    this._scopes = scopes;

	    this._loadModules(modules);

	    (0, _profiler.profileInjector)(this, Injector);
	  }

	  // Collect all registered providers that has given annotation.
	  // Including providers defined in parent injectors.


	  _createClass(Injector, [{
	    key: '_collectProvidersWithAnnotation',
	    value: function _collectProvidersWithAnnotation(annotationClass, collectedProviders) {
	      this._providers.forEach(function (provider, token) {
	        if (!collectedProviders.has(token) && (0, _annotations.hasAnnotation)(provider.provider, annotationClass)) {
	          collectedProviders.set(token, provider);
	        }
	      });

	      if (this._parent) {
	        this._parent._collectProvidersWithAnnotation(annotationClass, collectedProviders);
	      }
	    }

	    // Load modules/function/classes.
	    // This mutates `this._providers`, but it is only called during the constructor.

	  }, {
	    key: '_loadModules',
	    value: function _loadModules(modules) {
	      var _iteratorNormalCompletion = true;
	      var _didIteratorError = false;
	      var _iteratorError = undefined;

	      try {
	        for (var _iterator = modules[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {
	          var module = _step.value;

	          // A single provider (class or function).
	          if ((0, _util.isFunction)(module)) {
	            this._loadFnOrClass(module);
	            continue;
	          }

	          throw new Error('Invalid module!');
	        }
	      } catch (err) {
	        _didIteratorError = true;
	        _iteratorError = err;
	      } finally {
	        try {
	          if (!_iteratorNormalCompletion && _iterator.return) {
	            _iterator.return();
	          }
	        } finally {
	          if (_didIteratorError) {
	            throw _iteratorError;
	          }
	        }
	      }
	    }

	    // Load a function or class.
	    // This mutates `this._providers`, but it is only called during the constructor.

	  }, {
	    key: '_loadFnOrClass',
	    value: function _loadFnOrClass(fnOrClass) {
	      // TODO(vojta): should we expose provider.token?
	      var annotations = (0, _annotations.readAnnotations)(fnOrClass);
	      var token = annotations.provide.token || fnOrClass;
	      var provider = (0, _providers.createProviderFromFnOrClass)(fnOrClass, annotations);

	      this._providers.set(token, provider);
	    }

	    // Returns true if there is any provider registered for given token.
	    // Including parent injectors.

	  }, {
	    key: '_hasProviderFor',
	    value: function _hasProviderFor(token) {
	      if (this._providers.has(token)) {
	        return true;
	      }

	      if (this._parent) {
	        return this._parent._hasProviderFor(token);
	      }

	      return false;
	    }

	    // Find the correct injector where the default provider should be instantiated and cached.

	  }, {
	    key: '_instantiateDefaultProvider',
	    value: function _instantiateDefaultProvider(provider, token, resolving, wantPromise, wantLazy) {
	      // In root injector, instantiate here.
	      if (!this._parent) {
	        this._providers.set(token, provider);
	        return this.get(token, resolving, wantPromise, wantLazy);
	      }

	      // Check if this injector forces new instance of this provider.
	      var _iteratorNormalCompletion2 = true;
	      var _didIteratorError2 = false;
	      var _iteratorError2 = undefined;

	      try {
	        for (var _iterator2 = this._scopes[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {
	          var ScopeClass = _step2.value;

	          if ((0, _annotations.hasAnnotation)(provider.provider, ScopeClass)) {
	            this._providers.set(token, provider);
	            return this.get(token, resolving, wantPromise, wantLazy);
	          }
	        }

	        // Otherwise ask parent injector.
	      } catch (err) {
	        _didIteratorError2 = true;
	        _iteratorError2 = err;
	      } finally {
	        try {
	          if (!_iteratorNormalCompletion2 && _iterator2.return) {
	            _iterator2.return();
	          }
	        } finally {
	          if (_didIteratorError2) {
	            throw _iteratorError2;
	          }
	        }
	      }

	      return this._parent._instantiateDefaultProvider(provider, token, resolving, wantPromise, wantLazy);
	    }

	    // Return an instance for given token.

	  }, {
	    key: 'get',
	    value: function get(token) {
	      var resolving = arguments.length <= 1 || arguments[1] === undefined ? [] : arguments[1];

	      var _this = this;

	      var wantPromise = arguments.length <= 2 || arguments[2] === undefined ? false : arguments[2];
	      var wantLazy = arguments.length <= 3 || arguments[3] === undefined ? false : arguments[3];

	      var resolvingMsg = '';
	      var provider;
	      var instance;
	      var injector = this;

	      if (token === null || token === undefined) {
	        resolvingMsg = constructResolvingMessage(resolving, token);
	        throw new Error('Invalid token "' + token + '" requested!' + resolvingMsg);
	      }

	      // Special case, return itself.
	      if (token === Injector) {
	        if (wantPromise) {
	          return Promise.resolve(this);
	        }

	        return this;
	      }

	      // TODO(vojta): optimize - no child injector for locals?
	      if (wantLazy) {
	        return function createLazyInstance() {
	          var lazyInjector = injector;

	          if (arguments.length) {
	            var locals = [];
	            var args = arguments;

	            for (var i = 0; i < args.length; i += 2) {
	              locals.push(function (ii) {
	                var fn = function createLocalInstance() {
	                  return args[ii + 1];
	                };

	                (0, _annotations.annotate)(fn, new _annotations.Provide(args[ii]));

	                return fn;
	              }(i));
	            }

	            lazyInjector = injector.createChild(locals);
	          }

	          return lazyInjector.get(token, resolving, wantPromise, false);
	        };
	      }

	      // Check if there is a cached instance already.
	      if (this._cache.has(token)) {
	        instance = this._cache.get(token);
	        provider = this._providers.get(token);

	        if (provider.isPromise && !wantPromise) {
	          resolvingMsg = constructResolvingMessage(resolving, token);
	          throw new Error('Cannot instantiate ' + (0, _util.toString)(token) + ' synchronously. It is provided as a promise!' + resolvingMsg);
	        }

	        if (!provider.isPromise && wantPromise) {
	          return Promise.resolve(instance);
	        }

	        return instance;
	      }

	      provider = this._providers.get(token);

	      // No provider defined (overridden), use the default provider (token).
	      if (!provider && (0, _util.isFunction)(token) && !this._hasProviderFor(token)) {
	        provider = (0, _providers.createProviderFromFnOrClass)(token, (0, _annotations.readAnnotations)(token));
	        return this._instantiateDefaultProvider(provider, token, resolving, wantPromise, wantLazy);
	      }

	      if (!provider) {
	        if (!this._parent) {
	          resolvingMsg = constructResolvingMessage(resolving, token);
	          throw new Error('No provider for ' + (0, _util.toString)(token) + '!' + resolvingMsg);
	        }

	        return this._parent.get(token, resolving, wantPromise, wantLazy);
	      }

	      if (resolving.indexOf(token) !== -1) {
	        resolvingMsg = constructResolvingMessage(resolving, token);
	        throw new Error('Cannot instantiate cyclic dependency!' + resolvingMsg);
	      }

	      resolving.push(token);

	      // TODO(vojta): handle these cases:
	      // 1/
	      // - requested as promise (delayed)
	      // - requested again as promise (before the previous gets resolved) -> cache the promise
	      // 2/
	      // - requested as promise (delayed)
	      // - requested again sync (before the previous gets resolved)
	      // -> error, but let it go inside to throw where exactly is the async provider
	      var delayingInstantiation = wantPromise && provider.params.some(function (param) {
	        return !param.isPromise;
	      });
	      var args = provider.params.map(function (param) {

	        if (delayingInstantiation) {
	          return _this.get(param.token, resolving, true, param.isLazy);
	        }

	        return _this.get(param.token, resolving, param.isPromise, param.isLazy);
	      });

	      // Delaying the instantiation - return a promise.
	      if (delayingInstantiation) {
	        var delayedResolving = resolving.slice(); // clone

	        resolving.pop();

	        // Once all dependencies (promises) are resolved, instantiate.
	        return Promise.all(args).then(function (args) {
	          try {
	            instance = provider.create(args);
	          } catch (e) {
	            resolvingMsg = constructResolvingMessage(delayedResolving);
	            var originalMsg = 'ORIGINAL ERROR: ' + e.message;
	            e.message = 'Error during instantiation of ' + (0, _util.toString)(token) + '!' + resolvingMsg + '\n' + originalMsg;
	            throw e;
	          }

	          if (!(0, _annotations.hasAnnotation)(provider.provider, _annotations.TransientScope)) {
	            injector._cache.set(token, instance);
	          }

	          // TODO(vojta): if a provider returns a promise (but is not declared as @ProvidePromise),
	          // here the value will get unwrapped (because it is returned from a promise callback) and
	          // the actual value will be injected. This is probably not desired behavior. Maybe we could
	          // get rid off the @ProvidePromise and just check the returned value, whether it is
	          // a promise or not.
	          return instance;
	        });
	      }

	      try {
	        instance = provider.create(args);
	      } catch (e) {
	        resolvingMsg = constructResolvingMessage(resolving);
	        var originalMsg = 'ORIGINAL ERROR: ' + e.message;
	        e.message = 'Error during instantiation of ' + (0, _util.toString)(token) + '!' + resolvingMsg + '\n' + originalMsg;
	        throw e;
	      }

	      if (!(0, _annotations.hasAnnotation)(provider.provider, _annotations.TransientScope)) {
	        this._cache.set(token, instance);
	      }

	      if (!wantPromise && provider.isPromise) {
	        resolvingMsg = constructResolvingMessage(resolving);

	        throw new Error('Cannot instantiate ' + (0, _util.toString)(token) + ' synchronously. It is provided as a promise!' + resolvingMsg);
	      }

	      if (wantPromise && !provider.isPromise) {
	        instance = Promise.resolve(instance);
	      }

	      resolving.pop();

	      return instance;
	    }
	  }, {
	    key: 'getPromise',
	    value: function getPromise(token) {
	      return this.get(token, [], true);
	    }

	    // Create a child injector, which encapsulate shorter life scope.
	    // It is possible to add additional providers and also force new instances of existing providers.

	  }, {
	    key: 'createChild',
	    value: function createChild() {
	      var modules = arguments.length <= 0 || arguments[0] === undefined ? [] : arguments[0];
	      var forceNewInstancesOf = arguments.length <= 1 || arguments[1] === undefined ? [] : arguments[1];

	      var forcedProviders = new Map();

	      // Always force new instance of TransientScope.
	      forceNewInstancesOf.push(_annotations.TransientScope);

	      var _iteratorNormalCompletion3 = true;
	      var _didIteratorError3 = false;
	      var _iteratorError3 = undefined;

	      try {
	        for (var _iterator3 = forceNewInstancesOf[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {
	          var annotation = _step3.value;

	          this._collectProvidersWithAnnotation(annotation, forcedProviders);
	        }
	      } catch (err) {
	        _didIteratorError3 = true;
	        _iteratorError3 = err;
	      } finally {
	        try {
	          if (!_iteratorNormalCompletion3 && _iterator3.return) {
	            _iterator3.return();
	          }
	        } finally {
	          if (_didIteratorError3) {
	            throw _iteratorError3;
	          }
	        }
	      }

	      return new Injector(modules, this, forcedProviders, forceNewInstancesOf);
	    }
	  }]);

	  return Injector;
	}();

	exports.Injector = Injector;

/***/ },
/* 2 */
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	Object.defineProperty(exports, "__esModule", {
	  value: true
	});
	exports.FactoryProvider = exports.ClassProvider = exports.ProvidePromise = exports.Provide = exports.InjectLazy = exports.InjectPromise = exports.Inject = exports.TransientScope = exports.SuperConstructor = exports.readAnnotations = exports.hasAnnotation = exports.annotate = undefined;

	var _util = __webpack_require__(3);

	function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

	function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }

	function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

	// This module contains:
	// - built-in annotation classes
	// - helpers to read/write annotations

	// ANNOTATIONS

	// A built-in token.
	// Used to ask for pre-injected parent constructor.
	// A class constructor can ask for this.

	var SuperConstructor = function SuperConstructor() {
	  _classCallCheck(this, SuperConstructor);
	};

	// A built-in scope.
	// Never cache.


	var TransientScope = function TransientScope() {
	  _classCallCheck(this, TransientScope);
	};

	var Inject = function Inject() {
	  _classCallCheck(this, Inject);

	  for (var _len = arguments.length, tokens = Array(_len), _key = 0; _key < _len; _key++) {
	    tokens[_key] = arguments[_key];
	  }

	  this.tokens = tokens;
	  this.isPromise = false;
	  this.isLazy = false;
	};

	var InjectPromise = function (_Inject) {
	  _inherits(InjectPromise, _Inject);

	  function InjectPromise() {
	    _classCallCheck(this, InjectPromise);

	    var _this = _possibleConstructorReturn(this, Object.getPrototypeOf(InjectPromise).call(this));

	    for (var _len2 = arguments.length, tokens = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
	      tokens[_key2] = arguments[_key2];
	    }

	    _this.tokens = tokens;
	    _this.isPromise = true;
	    _this.isLazy = false;
	    return _this;
	  }

	  return InjectPromise;
	}(Inject);

	var InjectLazy = function (_Inject2) {
	  _inherits(InjectLazy, _Inject2);

	  function InjectLazy() {
	    _classCallCheck(this, InjectLazy);

	    var _this2 = _possibleConstructorReturn(this, Object.getPrototypeOf(InjectLazy).call(this));

	    for (var _len3 = arguments.length, tokens = Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
	      tokens[_key3] = arguments[_key3];
	    }

	    _this2.tokens = tokens;
	    _this2.isPromise = false;
	    _this2.isLazy = true;
	    return _this2;
	  }

	  return InjectLazy;
	}(Inject);

	var Provide = function Provide(token) {
	  _classCallCheck(this, Provide);

	  this.token = token;
	  this.isPromise = false;
	};

	var ProvidePromise = function (_Provide) {
	  _inherits(ProvidePromise, _Provide);

	  function ProvidePromise(token) {
	    _classCallCheck(this, ProvidePromise);

	    var _this3 = _possibleConstructorReturn(this, Object.getPrototypeOf(ProvidePromise).call(this));

	    _this3.token = token;
	    _this3.isPromise = true;
	    return _this3;
	  }

	  return ProvidePromise;
	}(Provide);

	var ClassProvider = function ClassProvider() {
	  _classCallCheck(this, ClassProvider);
	};

	var FactoryProvider = function FactoryProvider() {
	  _classCallCheck(this, FactoryProvider);
	};

	// HELPERS

	// Append annotation on a function or class.
	// This can be helpful when not using ES6+.


	function annotate(fn, annotation) {
	  fn.annotations = fn.annotations || [];
	  fn.annotations.push(annotation);
	}

	// Read annotations on a function or class and return whether given annotation is present.
	function hasAnnotation(fn, annotationClass) {
	  if (!fn.annotations || fn.annotations.length === 0) {
	    return false;
	  }

	  var _iteratorNormalCompletion = true;
	  var _didIteratorError = false;
	  var _iteratorError = undefined;

	  try {
	    for (var _iterator = fn.annotations[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {
	      var annotation = _step.value;

	      if (annotation instanceof annotationClass) {
	        return true;
	      }
	    }
	  } catch (err) {
	    _didIteratorError = true;
	    _iteratorError = err;
	  } finally {
	    try {
	      if (!_iteratorNormalCompletion && _iterator.return) {
	        _iterator.return();
	      }
	    } finally {
	      if (_didIteratorError) {
	        throw _iteratorError;
	      }
	    }
	  }

	  return false;
	}

	// Read annotations on a function or class and collect "interesting" metadata:
	function readAnnotations(fn) {
	  var collectedAnnotations = {
	    // Description of the provided value.
	    provide: {
	      token: null,
	      isPromise: false
	    },

	    // List of parameter descriptions.
	    // A parameter description is an object with properties:
	    // - token (anything)
	    // - isPromise (boolean)
	    // - isLazy (boolean)
	    params: []
	  };

	  if (fn.annotations && fn.annotations.length) {
	    var _iteratorNormalCompletion2 = true;
	    var _didIteratorError2 = false;
	    var _iteratorError2 = undefined;

	    try {
	      for (var _iterator2 = fn.annotations[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {
	        var annotation = _step2.value;

	        if (annotation instanceof Inject) {
	          annotation.tokens.forEach(function (token) {
	            collectedAnnotations.params.push({
	              token: token,
	              isPromise: annotation.isPromise,
	              isLazy: annotation.isLazy
	            });
	          });
	        }

	        if (annotation instanceof Provide) {
	          collectedAnnotations.provide.token = annotation.token;
	          collectedAnnotations.provide.isPromise = annotation.isPromise;
	        }
	      }
	    } catch (err) {
	      _didIteratorError2 = true;
	      _iteratorError2 = err;
	    } finally {
	      try {
	        if (!_iteratorNormalCompletion2 && _iterator2.return) {
	          _iterator2.return();
	        }
	      } finally {
	        if (_didIteratorError2) {
	          throw _iteratorError2;
	        }
	      }
	    }
	  }

	  // Read annotations for individual parameters.
	  if (fn.parameters) {
	    fn.parameters.forEach(function (param, idx) {
	      var _iteratorNormalCompletion3 = true;
	      var _didIteratorError3 = false;
	      var _iteratorError3 = undefined;

	      try {
	        for (var _iterator3 = param[Symbol.iterator](), _step3; !(_iteratorNormalCompletion3 = (_step3 = _iterator3.next()).done); _iteratorNormalCompletion3 = true) {
	          var paramAnnotation = _step3.value;

	          // Type annotation.
	          if ((0, _util.isFunction)(paramAnnotation) && !collectedAnnotations.params[idx]) {
	            collectedAnnotations.params[idx] = {
	              token: paramAnnotation,
	              isPromise: false,
	              isLazy: false
	            };
	          } else if (paramAnnotation instanceof Inject) {
	            collectedAnnotations.params[idx] = {
	              token: paramAnnotation.tokens[0],
	              isPromise: paramAnnotation.isPromise,
	              isLazy: paramAnnotation.isLazy
	            };
	          }
	        }
	      } catch (err) {
	        _didIteratorError3 = true;
	        _iteratorError3 = err;
	      } finally {
	        try {
	          if (!_iteratorNormalCompletion3 && _iterator3.return) {
	            _iterator3.return();
	          }
	        } finally {
	          if (_didIteratorError3) {
	            throw _iteratorError3;
	          }
	        }
	      }
	    });
	  }

	  return collectedAnnotations;
	}

	exports.annotate = annotate;
	exports.hasAnnotation = hasAnnotation;
	exports.readAnnotations = readAnnotations;
	exports.SuperConstructor = SuperConstructor;
	exports.TransientScope = TransientScope;
	exports.Inject = Inject;
	exports.InjectPromise = InjectPromise;
	exports.InjectLazy = InjectLazy;
	exports.Provide = Provide;
	exports.ProvidePromise = ProvidePromise;
	exports.ClassProvider = ClassProvider;
	exports.FactoryProvider = FactoryProvider;

/***/ },
/* 3 */
/***/ function(module, exports) {

	/* WEBPACK VAR INJECTION */(function(global) {'use strict';

	Object.defineProperty(exports, "__esModule", {
	  value: true
	});

	var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol ? "symbol" : typeof obj; };

	// A bunch of helper functions.

	function isUpperCase(char) {
	  return char.toUpperCase() === char;
	}

	function isFunction(value) {
	  return typeof value === 'function';
	}

	function isObject(value) {
	  return (typeof value === 'undefined' ? 'undefined' : _typeof(value)) === 'object';
	}

	function toString(token) {
	  if (typeof token === 'string') {
	    return token;
	  }

	  if (token === undefined || token === null) {
	    return '' + token;
	  }

	  if (token.name) {
	    return token.name;
	  }

	  return token.toString();
	}

	var ownKeys = global.Reflect && Reflect.ownKeys ? Reflect.ownKeys : function ownKeys(O) {
	  var keys = Object.getOwnPropertyNames(O);
	  if (Object.getOwnPropertySymbols) return keys.concat(Object.getOwnPropertySymbols(O));
	  return keys;
	};

	exports.isUpperCase = isUpperCase;
	exports.isFunction = isFunction;
	exports.isObject = isObject;
	exports.toString = toString;
	exports.ownKeys = ownKeys;
	/* WEBPACK VAR INJECTION */}.call(exports, (function() { return this; }())))

/***/ },
/* 4 */
/***/ function(module, exports, __webpack_require__) {

	/* WEBPACK VAR INJECTION */(function(process, global) {'use strict';

	Object.defineProperty(exports, "__esModule", {
	  value: true
	});

	var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === "function" && obj.constructor === Symbol ? "symbol" : typeof obj; };

	exports.profileInjector = profileInjector;

	var _util = __webpack_require__(3);

	var IS_DEBUG = false;
	var _global = null;

	if ((typeof process === 'undefined' ? 'undefined' : _typeof(process)) === 'object' && process.env) {
	  // Node.js
	  IS_DEBUG = !!process.env['DEBUG'];
	  _global = global;
	} else if ((typeof location === 'undefined' ? 'undefined' : _typeof(location)) === 'object' && location.search) {
	  // Browser
	  IS_DEBUG = /di_debug/.test(location.search);
	  _global = window;
	}

	var globalCounter = 0;
	function getUniqueId() {
	  return ++globalCounter;
	}

	function serializeToken(token, tokens) {
	  if (!tokens.has(token)) {
	    tokens.set(token, getUniqueId().toString());
	  }

	  return tokens.get(token);
	}

	function serializeProvider(provider, key, tokens) {
	  return {
	    id: serializeToken(key, tokens),
	    name: (0, _util.toString)(key),
	    isPromise: provider.isPromise,
	    dependencies: provider.params.map(function (param) {
	      return {
	        token: serializeToken(param.token, tokens),
	        isPromise: param.isPromise,
	        isLazy: param.isLazy
	      };
	    })
	  };
	}

	function serializeInjector(injector, tokens, Injector) {
	  var serializedInjector = {
	    id: serializeToken(injector, tokens),
	    parent_id: injector._parent ? serializeToken(injector._parent, tokens) : null,
	    providers: {}
	  };

	  var injectorClassId = serializeToken(Injector, tokens);
	  serializedInjector.providers[injectorClassId] = {
	    id: injectorClassId,
	    name: (0, _util.toString)(Injector),
	    isPromise: false,
	    dependencies: []
	  };

	  injector._providers.forEach(function (provider, key) {
	    var serializedProvider = serializeProvider(provider, key, tokens);
	    serializedInjector.providers[serializedProvider.id] = serializedProvider;
	  });

	  return serializedInjector;
	}

	function profileInjector(injector, Injector) {
	  if (!IS_DEBUG) {
	    return;
	  }

	  if (!_global.__di_dump__) {
	    _global.__di_dump__ = {
	      injectors: [],
	      tokens: new Map()
	    };
	  }

	  _global.__di_dump__.injectors.push(serializeInjector(injector, _global.__di_dump__.tokens, Injector));
	}
	/* WEBPACK VAR INJECTION */}.call(exports, __webpack_require__(5), (function() { return this; }())))

/***/ },
/* 5 */
/***/ function(module, exports) {

	// shim for using process in browser

	var process = module.exports = {};
	var queue = [];
	var draining = false;
	var currentQueue;
	var queueIndex = -1;

	function cleanUpNextTick() {
	    draining = false;
	    if (currentQueue.length) {
	        queue = currentQueue.concat(queue);
	    } else {
	        queueIndex = -1;
	    }
	    if (queue.length) {
	        drainQueue();
	    }
	}

	function drainQueue() {
	    if (draining) {
	        return;
	    }
	    var timeout = setTimeout(cleanUpNextTick);
	    draining = true;

	    var len = queue.length;
	    while(len) {
	        currentQueue = queue;
	        queue = [];
	        while (++queueIndex < len) {
	            if (currentQueue) {
	                currentQueue[queueIndex].run();
	            }
	        }
	        queueIndex = -1;
	        len = queue.length;
	    }
	    currentQueue = null;
	    draining = false;
	    clearTimeout(timeout);
	}

	process.nextTick = function (fun) {
	    var args = new Array(arguments.length - 1);
	    if (arguments.length > 1) {
	        for (var i = 1; i < arguments.length; i++) {
	            args[i - 1] = arguments[i];
	        }
	    }
	    queue.push(new Item(fun, args));
	    if (queue.length === 1 && !draining) {
	        setTimeout(drainQueue, 0);
	    }
	};

	// v8 likes predictible objects
	function Item(fun, array) {
	    this.fun = fun;
	    this.array = array;
	}
	Item.prototype.run = function () {
	    this.fun.apply(null, this.array);
	};
	process.title = 'browser';
	process.browser = true;
	process.env = {};
	process.argv = [];
	process.version = ''; // empty string to avoid regexp issues
	process.versions = {};

	function noop() {}

	process.on = noop;
	process.addListener = noop;
	process.once = noop;
	process.off = noop;
	process.removeListener = noop;
	process.removeAllListeners = noop;
	process.emit = noop;

	process.binding = function (name) {
	    throw new Error('process.binding is not supported');
	};

	process.cwd = function () { return '/' };
	process.chdir = function (dir) {
	    throw new Error('process.chdir is not supported');
	};
	process.umask = function() { return 0; };


/***/ },
/* 6 */
/***/ function(module, exports, __webpack_require__) {

	'use strict';

	Object.defineProperty(exports, "__esModule", {
	  value: true
	});

	var _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();

	exports.createProviderFromFnOrClass = createProviderFromFnOrClass;

	var _annotations = __webpack_require__(2);

	var _util = __webpack_require__(3);

	function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

	function isClass(clsOrFunction) {

	  if ((0, _annotations.hasAnnotation)(clsOrFunction, _annotations.ClassProvider)) {
	    return true;
	  } else if ((0, _annotations.hasAnnotation)(clsOrFunction, _annotations.FactoryProvider)) {
	    return false;
	  } else if (clsOrFunction.name) {
	    return (0, _util.isUpperCase)(clsOrFunction.name.charAt(0));
	  } else {
	    return (0, _util.ownKeys)(clsOrFunction.prototype).length > 0;
	  }
	}

	// Provider is responsible for creating instances.
	//
	// responsibilities:
	// - create instances
	//
	// communication:
	// - exposes `create()` which creates an instance of something
	// - exposes `params` (information about which arguments it requires to be passed into `create()`)
	//
	// Injector reads `provider.params` first, create these dependencies (however it wants),
	// then calls `provider.create(args)`, passing in these arguments.

	var EmptyFunction = Object.getPrototypeOf(Function);

	// ClassProvider knows how to instantiate classes.
	//
	// If a class inherits (has parent constructors), this provider normalizes all the dependencies
	// into a single flat array first, so that the injector does not need to worry about inheritance.
	//
	// - all the state is immutable (constructed)
	//
	// TODO(vojta): super constructor - should be only allowed during the constructor call?

	var ClassProvider = function () {
	  function ClassProvider(clazz, params, isPromise) {
	    _classCallCheck(this, ClassProvider);

	    // TODO(vojta): can we hide this.provider? (only used for hasAnnotation(provider.provider))
	    this.provider = clazz;
	    this.isPromise = isPromise;

	    this.params = [];
	    this._constructors = [];

	    this._flattenParams(clazz, params);
	    this._constructors.unshift([clazz, 0, this.params.length - 1]);
	  }

	  // Normalize params for all the constructors (in the case of inheritance),
	  // into a single flat array of DependencyDescriptors.
	  // So that the injector does not have to worry about inheritance.
	  //
	  // This function mutates `this.params` and `this._constructors`,
	  // but it is only called during the constructor.
	  // TODO(vojta): remove the annotations argument?


	  _createClass(ClassProvider, [{
	    key: '_flattenParams',
	    value: function _flattenParams(constructor, params) {
	      var SuperConstructor;
	      var constructorInfo;

	      var _iteratorNormalCompletion = true;
	      var _didIteratorError = false;
	      var _iteratorError = undefined;

	      try {
	        for (var _iterator = params[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {
	          var param = _step.value;

	          if (param.token === _annotations.SuperConstructor) {
	            SuperConstructor = Object.getPrototypeOf(constructor);

	            if (SuperConstructor === EmptyFunction) {
	              throw new Error((0, _util.toString)(constructor) + ' does not have a parent constructor. Only classes with a parent can ask for SuperConstructor!');
	            }

	            constructorInfo = [SuperConstructor, this.params.length];
	            this._constructors.push(constructorInfo);
	            this._flattenParams(SuperConstructor, (0, _annotations.readAnnotations)(SuperConstructor).params);
	            constructorInfo.push(this.params.length - 1);
	          } else {
	            this.params.push(param);
	          }
	        }
	      } catch (err) {
	        _didIteratorError = true;
	        _iteratorError = err;
	      } finally {
	        try {
	          if (!_iteratorNormalCompletion && _iterator.return) {
	            _iterator.return();
	          }
	        } finally {
	          if (_didIteratorError) {
	            throw _iteratorError;
	          }
	        }
	      }
	    }

	    // Basically the reverse process to `this._flattenParams`:
	    // We get arguments for all the constructors as a single flat array.
	    // This method generates pre-bound "superConstructor" wrapper with correctly passing arguments.

	  }, {
	    key: '_createConstructor',
	    value: function _createConstructor(currentConstructorIdx, context, allArguments) {
	      var constructorInfo = this._constructors[currentConstructorIdx];
	      var nextConstructorInfo = this._constructors[currentConstructorIdx + 1];
	      var argsForCurrentConstructor;

	      if (nextConstructorInfo) {
	        argsForCurrentConstructor = allArguments.slice(constructorInfo[1], nextConstructorInfo[1]).concat([this._createConstructor(currentConstructorIdx + 1, context, allArguments)]).concat(allArguments.slice(nextConstructorInfo[2] + 1, constructorInfo[2] + 1));
	      } else {
	        argsForCurrentConstructor = allArguments.slice(constructorInfo[1], constructorInfo[2] + 1);
	      }

	      return function InjectedAndBoundSuperConstructor() {
	        // TODO(vojta): throw if arguments given
	        return constructorInfo[0].apply(context, argsForCurrentConstructor);
	      };
	    }

	    // It is called by injector to create an instance.

	  }, {
	    key: 'create',
	    value: function create(args) {
	      var context = Object.create(this.provider.prototype);
	      var constructor = this._createConstructor(0, context, args);
	      var returnedValue = constructor();

	      if ((0, _util.isFunction)(returnedValue) || (0, _util.isObject)(returnedValue)) {
	        return returnedValue;
	      }

	      return context;
	    }
	  }]);

	  return ClassProvider;
	}();

	// FactoryProvider knows how to create instance from a factory function.
	// - all the state is immutable


	var FactoryProvider = function () {
	  function FactoryProvider(factoryFunction, params, isPromise) {
	    _classCallCheck(this, FactoryProvider);

	    this.provider = factoryFunction;
	    this.params = params;
	    this.isPromise = isPromise;

	    var _iteratorNormalCompletion2 = true;
	    var _didIteratorError2 = false;
	    var _iteratorError2 = undefined;

	    try {
	      for (var _iterator2 = params[Symbol.iterator](), _step2; !(_iteratorNormalCompletion2 = (_step2 = _iterator2.next()).done); _iteratorNormalCompletion2 = true) {
	        var param = _step2.value;

	        if (param.token === _annotations.SuperConstructor) {
	          throw new Error((0, _util.toString)(factoryFunction) + ' is not a class. Only classes with a parent can ask for SuperConstructor!');
	        }
	      }
	    } catch (err) {
	      _didIteratorError2 = true;
	      _iteratorError2 = err;
	    } finally {
	      try {
	        if (!_iteratorNormalCompletion2 && _iterator2.return) {
	          _iterator2.return();
	        }
	      } finally {
	        if (_didIteratorError2) {
	          throw _iteratorError2;
	        }
	      }
	    }
	  }

	  _createClass(FactoryProvider, [{
	    key: 'create',
	    value: function create(args) {
	      return this.provider.apply(undefined, args);
	    }
	  }]);

	  return FactoryProvider;
	}();

	function createProviderFromFnOrClass(fnOrClass, annotations) {
	  if (isClass(fnOrClass)) {
	    return new ClassProvider(fnOrClass, annotations.params, annotations.provide.isPromise);
	  }

	  return new FactoryProvider(fnOrClass, annotations.params, annotations.provide.isPromise);
	}

/***/ }
/******/ ]);
