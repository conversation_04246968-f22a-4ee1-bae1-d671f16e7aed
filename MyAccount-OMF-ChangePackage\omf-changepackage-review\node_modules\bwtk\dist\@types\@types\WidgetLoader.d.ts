import * as React from "react";
import { ViewWidget } from "./ViewWidget";
export interface ViewWidgetLoaderProps {
    widget: string;
    onLoad?(widget: ViewWidget): void;
    onLoadError?(error: any): void;
    [others: string]: any;
}
export declare class WidgetLoader extends React.Component<ViewWidgetLoaderProps, any> {
    private destroyed;
    private delayInit;
    private _widget;
    private loaded;
    private delayInitCallback;
    private widgetRoot;
    constructor(props: ViewWidgetLoaderProps);
    private get widget();
    private updateWidgetProps;
    componentDidMount(): void;
    private onWidgetLoaded;
    componentWillUnmount(): void;
    destroy(): void;
    tryDelayedRender(retryTimeout?: number, retryTimes?: number, cb?: Function): void;
    render(): React.ReactNode;
}
