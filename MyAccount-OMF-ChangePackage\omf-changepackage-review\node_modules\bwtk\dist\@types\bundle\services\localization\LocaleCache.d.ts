import { MessagesDictionary, Id, LocaleValue } from "../../../@types";
export declare class LocaleCache {
    private static cache;
    private static callbacks;
    private static mappings;
    static initLocaleData(widgetName: string): void;
    static setLocaleData(widgetName: string, locale: MessagesDictionary): void;
    static tryUseLocaleData(widget: Id | string): void;
    static useLocaleData(widget: Id | string): void;
    static addCallback(widget: Id | string, cb: IOnReadyCallback): void;
    static removeCallback(widget: Id | string): void;
    static runOnReadyCallbacks(widgetName: string): void;
    private static callbackExists;
    static getLocaleData(widget: Id | string): ILocaleCacheValue | null;
    static isLocaleDataReady(widget: Id, locale: LocaleValue): boolean;
    static isFinished(widget: Id, locale: LocaleValue): boolean;
    static isPending(widget: Id | string): boolean;
    static setPermanentlyFailed(widget: Id | string): void;
    static loadingFailed(widget: Id | string): boolean;
    static remove(widget: Id | string, deleteCacheData?: boolean): void;
    private static removeCacheData;
    static addMapping(config: {
        [widgetName: string]: string | string[];
    }): void;
    static getMappings(widget: Id | string): string[];
    private static normalizeId;
    static isMapped(widget: Id | string): boolean;
}
export interface ILocaleCache {
    [widgetName: string]: ILocaleCacheValue;
}
export interface ILocaleCacheValue {
    locale: MessagesDictionary;
    state: LocaleCacheState;
    usedBy: string[];
}
export interface IOnReadyCallback {
    (): boolean;
}
export interface IOnReadyCallbacks {
    [widgetName: string]: IOnReadyCallbackEntry[];
}
export interface IOnReadyCallbackEntry {
    [id: string]: IOnReadyCallback;
}
export interface IMappings {
    [widgetName: string]: string[];
}
export interface INormalizedId {
    id: string;
    widget: string;
}
export declare enum LocaleCacheState {
    PENGING = 1,
    READY = 2,
    FAILED = 3
}
