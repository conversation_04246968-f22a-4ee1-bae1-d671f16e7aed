const CURRENT_SCRIPT_SRC = (document.currentScript && document.currentScript.getAttribute('src')) || '';

class BwtkLoader {
  fullDomain = '';
  query = '';

  constructor() {
    // Any JS files that contain ES module exports but have a .js extension
    // Add more filenames here as needed
    this.moduleJsFiles = [
      "redux-actions.js"
      // "some-other-esm-file.js",
      // "example.module.js",
    ];

    if (CURRENT_SCRIPT_SRC) {
      try {
        const url = new URL(CURRENT_SCRIPT_SRC);
    
        this.fullDomain = `${url.protocol}//${url.hostname}`;
        this.query = url.search;
      } catch (e) {
        console.log("Page not using cached URLs:", e);
      }
    }

    this.defaultScripts = {
      polyfill: `${this.fullDomain}/styles/WidgetAssets/bwtk/v2-7/polyfill/polyfill.min.js${this.query}`,
      redux: "",
      react: "",
      reactDom: "",
      rxjs: "",
      reduxActions: "",
      reactRedux: "",
      reduxObservable: "",
      inject: `${this.fullDomain}/styles/WidgetAssets/bwtk/inject.min.js${this.query}`,
      propTypes: `${this.fullDomain}/styles/WidgetAssets/libs/v3/prop-types/prop-types.min.js${this.query}`,
      reactIntl: `${this.fullDomain}/styles/WidgetAssets/libs/v6/react-intl/react-intl.iife.js${this.query}`,
      bwtk: `${this.fullDomain}/styles/WidgetAssets/bwtk/v6/bwtk.min.js${this.query}`
    };
  }

  /**
   * Helper method to determine script type based on file name or extension.
   *  - If the file is known to be a module (e.g., in `this.moduleJsFiles`), load as "module".
   *  - If it ends with `.mjs`, load as "module".
   *  - Otherwise, "text/javascript" by default.
   */
  getScriptType(src) {
    // 1. Check if the file is in our known list of ESM .js files
    if (this.moduleJsFiles.some((fileName) => src.includes(fileName))) {
      return "module";
    }

    // 2. Check if it ends with ".mjs"
    if (src.endsWith(".mjs")) {
      return "module";
    }

    // 3. Default: classic script
    return "text/javascript";
  }

  start({ mode = "production", override = {}, bundlePath = "" }, onReadyCB = () => {}) {
    const isDevMode = mode === "development";
    const fullDomain = this.fullDomain;
    const query = this.query;

    // Update default scripts based on mode
    this.defaultScripts.redux = isDevMode
      ? `${fullDomain}/styles/WidgetAssets/libs/v6/redux/umd/redux.umd.js${query}`
      : `${fullDomain}/styles/WidgetAssets/libs/v6/redux/umd/redux.umd.js${query}`;
    this.defaultScripts.react = isDevMode
      ? `${fullDomain}/styles/WidgetAssets/libs/v6/react/umd/react.development.js${query}`
      : `${fullDomain}/styles/WidgetAssets/libs/v6/react/umd/react.production.min.js${query}`;
    this.defaultScripts.reactDom = isDevMode
      ? `${fullDomain}/styles/WidgetAssets/libs/v6/react-dom/umd/react-dom.development.js${query}`
      : `${fullDomain}/styles/WidgetAssets/libs/v6/react-dom/umd/react-dom.production.min.js${query}`;
    this.defaultScripts.rxjs = isDevMode
      ? `${fullDomain}/styles/WidgetAssets/libs/v6/rxjs/bundles/rxjs.umd.js${query}`
      : `${fullDomain}/styles/WidgetAssets/libs/v6/rxjs/bundles/rxjs.umd.min.js${query}`;
    this.defaultScripts.reduxActions = isDevMode
      ? `${fullDomain}/styles/WidgetAssets/libs/v6/redux-actions/redux-actions.umd.js${query}`
      : `${fullDomain}/styles/WidgetAssets/libs/v6/redux-actions/redux-actions.umd.js${query}`;
    this.defaultScripts.reactRedux = isDevMode
      ? `${fullDomain}/styles/WidgetAssets/libs/v6/react-redux/umd/react-redux.umd.js${query}`
      : `${fullDomain}/styles/WidgetAssets/libs/v6/react-redux/umd/react-redux.umd.js${query}`;
    this.defaultScripts.reduxObservable = isDevMode
      ? `${fullDomain}/styles/WidgetAssets/libs/v6/redux-observable/umd/redux-observable.umd.js${query}`
      : `${fullDomain}/styles/WidgetAssets/libs/v6/redux-observable/umd/redux-observable.umd.js${query}`;
    this.defaultScripts.reactIntl = isDevMode
      ? `${fullDomain}/styles/WidgetAssets/libs/v6/react-intl/react-intl.iife.js${query}`
      : `${fullDomain}/styles/WidgetAssets/libs/v6/react-intl/react-intl.iife.js${query}`;
    this.defaultScripts.bwtk = isDevMode ? `${fullDomain}/styles/WidgetAssets/bwtk/v6/bwtk-6.1.0.js${query}` : `${fullDomain}/styles/WidgetAssets/bwtk/v6/bwtk-6.1.0.min.js${query}`;

    // Merge/override default scripts
    const scriptsToLoad = Object.entries({
      ...this.defaultScripts,
      ...override.scripts
    }).map(([key, src]) => src);

    // Add additional scripts from override
    const additionalScripts = override.additionalScripts || [];
    scriptsToLoad.push(...additionalScripts);

    // Finally, add the main bundle at the end
    scriptsToLoad.push(`${fullDomain}${bundlePath}${query}`);

    // Group some scripts as "independent"
    const independentScripts = [
      this.defaultScripts.inject,
      this.defaultScripts.react,
      this.defaultScripts.redux,
      this.defaultScripts.rxjs
    ];

    // The rest will be loaded sequentially
    const dependentScripts = scriptsToLoad.filter((script) => !independentScripts.includes(script));

    // Load independent scripts in parallel, then dependent scripts sequentially
    this.loadScriptsInParallel(independentScripts)
      .then(() => this.loadScriptsSequentially(dependentScripts))
      .then(() => this.onReady(onReadyCB))
      .catch((err) => console.error("Error loading scripts:", err));
  }

  loadScriptsInParallel(scripts) {
    // Load all scripts in parallel and return a Promise that resolves when all are loaded
    return Promise.all(
      scripts.map(
        (src) =>
          new Promise((resolve, reject) => {
            const script = document.createElement("script");
            script.src = src;
            script.type = this.getScriptType(src);
            script.onload = resolve;
            script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
            document.head.appendChild(script);
          })
      )
    );
  }

  loadScriptsSequentially(scripts) {
    // Load scripts one by one in sequence
    return scripts.reduce(
      (promiseChain, src) =>
        promiseChain.then(
          () =>
            new Promise((resolve, reject) => {
              const script = document.createElement("script");
              script.src = src;
              script.type = this.getScriptType(src);
              script.onload = resolve;
              script.onerror = () => reject(new Error(`Failed to load script: ${src}`));
              document.head.appendChild(script);
            })
        ),
      Promise.resolve()
    );
  }

  onReady(onReadyCB) {
    // If your final bundle attaches itself to window.bundle, unify the name
    if (typeof window.Bundle === "undefined") {
      window.Bundle = window.bundle;
    }

    // Call Bundle.initialize if it exists
    if (typeof window.Bundle !== "undefined" && typeof window.Bundle.initialize === "function") {
      const Bundle = window.Bundle;
      onReadyCB(Bundle);
    } else {
      console.error("Bundle or Bundle.initialize is not defined");
    }

    console.log("All scripts loaded");
  }
}

// Usage example:
// const loader = new BwtkLoader();
// loader.start({ mode: "development", override: {}, bundlePath: "path/to/bundle.js" });
