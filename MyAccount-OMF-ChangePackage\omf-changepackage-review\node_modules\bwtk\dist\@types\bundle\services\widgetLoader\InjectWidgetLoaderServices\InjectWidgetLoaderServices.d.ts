import { WidgetLoaderServices, ConfigServices, Logger } from "../../../../@types";
import { WLServices as StaticWidgetLoaderServices } from "../StaticWidgetLoaderServices";
export declare class WLServices extends StaticWidgetLoaderServices implements WidgetLoaderServices {
    private static createLogger;
    private useRequireJs;
    constructor(config: ConfigServices, logger: Logger);
    private _inject;
    private get inject();
    private requireContext;
    init(): void;
    reset(): void;
    load(id: string): Promise<any>;
}
