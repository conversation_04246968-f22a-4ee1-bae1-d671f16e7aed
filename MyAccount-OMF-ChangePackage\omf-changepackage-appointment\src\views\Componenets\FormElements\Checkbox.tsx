import { FormattedMessage } from "react-intl";
import { useFormContext } from "react-hook-form";
import { FormattedHTMLMessage } from "omf-changepackage-components";

interface ComponentProps {
  label: string;
  value: string;
  handleChange: Function;
  subLabel?: string;
  checked?: boolean;
}

export const Checkbox = (props: ComponentProps) => {
  const privateProps = { ...defaultProps, ...props};
  const { label, value, handleChange, subLabel, checked } = privateProps;
  const { register }: any = useFormContext();

  return (
    <div className="flexBlock flexCol-xs margin-15-bottom">
      <label htmlFor="additionalPhoneNumber" className="installation-form-label">
        <span className="txtBold block"><FormattedMessage id={label} /></span>
        {subLabel ? <span className="txtItalic block txtNormal"><FormattedHTMLMessage id={subLabel} /></span> : null}
      </label>
      <div className="flexCol margin-5-top">
        <label className="graphical_ctrl graphical_ctrl_checkbox txtNormal">
          <FormattedHTMLMessage id={value + "_LABEL"} />
          <input type="checkbox" ref={register} id={label} name={label} defaultChecked={checked} onChange={(e) => handleChange(e)} />
          <span className="ctrl_element chk_radius"></span>
        </label>
      </div>
    </div>
  );
};

const defaultProps = {
  checked: false
};
