/* eslint-disable */
"use strict";
(function(env) {
    var api = {},
        onReadyCallback = function() {}, // should be overwritten by user
        polyfillPath = "",
        scripts = [];
    
    function getScripts() {
        var tags = document.getElementsByTagName("link"),
            attr = "data-bwtk-load";
        for (var i = 0; i < tags.length; i++) {
            var tag = tags[i];
            if (tag.hasAttribute(attr)) {
                var url = tag.getAttribute("href");
                if (/polyfill\./i.test(url)) {
                    polyfillPath = url;
                    continue;
                }
                scripts.push(url);
            }
        }
    }

    function fetchScript(url, callback) {
        var script = document.createElement("script")

        if (script.readyState){  // IE
            script.onreadystatechange = function(){
                if (/(loaded|complete)/.test(script.readyState)){
                    script.onreadystatechange = null;
                    callback();
                }
            };
        } else {  // other browsers
            script.onload = callback;
        }
        
        script.src = url;
        document.getElementsByTagName("head")[0].appendChild(script);        
    }

    function loadScripts(index) {  
        if (index >= scripts.length) {
            onReadyCallback();
            return;
        }
        fetchScript(scripts[index], function() {
            loadScripts(index + 1);
        });      
    }

    function init() {
        loadScripts(0);
    }    

    api.setCallback = function(callback) {
        onReadyCallback = callback;
    };

    api.load = function(callback) {
        onReadyCallback = callback ? callback : onReadyCallback;

        getScripts();

        if (typeof env.BwtkPolyfill === "undefined" && polyfillPath !== "") {
            fetchScript(polyfillPath, function(){
                env.BwtkPolyfill.ready(init);
            });
        } else {
            env.BwtkPolyfill.ready(init);
        }
        return api;
    };

    api.start = api.load;

    env.BwtkLoader = api;
})(window);