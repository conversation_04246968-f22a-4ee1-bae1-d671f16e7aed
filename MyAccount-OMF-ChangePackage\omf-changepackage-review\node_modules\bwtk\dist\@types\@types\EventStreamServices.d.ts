export declare abstract class EventStreamServices {
    abstract init(): void;
    abstract subscribe(eventName: string, callback: IEventStreamCallback): IEventStreamVoid;
    abstract subscribe(config: IEventStreamParams): IEventStreamVoid;
    abstract subscribe(callback: IEventStreamGlobalCallback): IEventStreamVoid;
    abstract subscribeError(callback: IEventStreamCallback): IEventStreamVoid;
    abstract subscribeAll(callback: IEventStreamCallback): () => void;
    abstract send(type: string, payload?: any): void;
    abstract sendError(errorType: string, errorPayload?: any, widgetName?: string): void;
    abstract sendError(error: Error, widgetName?: string): void;
    abstract destroy(): void;
    abstract get errors(): IEventStreamErrorsShortcut;
}
export declare abstract class EventStream {
    abstract init(): void;
}
export interface IEventStreamParams {
    [type: string]: IEventStreamCallback;
}
export type IEventStreamCallback = (payload?: any) => void;
export type IEventStreamGlobalCallback = (event: IEventStreamValue) => void;
export interface IEventStreamValue {
    type: string;
    payload?: any;
    error?: boolean;
    meta?: {};
}
export interface IEventStreamErrorValue {
    type: string;
    payload: any;
}
export type IEventStreamVoid = () => void;
export interface IEventStreamErrorsShortcut {
    subscribe: (callback: IEventStreamCallback) => IEventStreamVoid;
    send: (errorOrErrorType: string | Error, errorPayload?: any, widgetName?: string) => void;
}
export declare enum ErrorStreamErrors {
    GENERIC = "ERROR_GENERIC",
    STREAM = "ERROR_EVENT_STREAM",
    AJAX = "ERROR_AJAX",
    REDUX = "ERROR_REDUX",
    LOCALIZATION = "ERROR_LOCALIZATION",
    REACT_BOUNDARY = "ERROR_REACT_BOUNDARY"
}
