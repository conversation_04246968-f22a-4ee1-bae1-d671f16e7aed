import { WidgetLoaderServices, ConfigServices, Logger } from "../../../@types";
export declare class WLServices implements WidgetLoaderServices {
    protected config: ConfigServices;
    protected logger: Logger;
    protected registry: {
        [key: string]: IModuleDefinition;
    };
    constructor(config: ConfigServices, logger: Logger);
    init(): void;
    reset(): void;
    load(id: string): Promise<any>;
    private registerStaticModules;
    registerStaticModule(moduleId: string, moduleDefinition: any): void;
}
export interface IModuleDefinition {
    namespace?: string;
    factory?: IFactory;
    url?: string;
    loaded?: any;
}
export interface IFactory {
    <T>(): T | Promise<T>;
}
