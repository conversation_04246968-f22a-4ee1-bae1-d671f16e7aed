import { DiServices } from "./DiServices";
import { WidgetLoaderServices } from "./WidgetLoaderServices";
import { StoreServices } from "./StoreServices";
import { EventStreamServices } from "./EventStreamServices";
import { ViewWidget } from "./ViewWidget";
export declare abstract class ServiceLocator implements IServiceLocator {
    protected get di(): DiServices;
    protected get loader(): WidgetLoaderServices;
    protected get store(): StoreServices;
    protected get stream(): EventStreamServices;
    abstract addServiceInterceptor(id: string, interceptor: Function): Function;
    abstract getService(id: string): any;
    getWidget(ids: string | string[], cb: (...instance: ViewWidget[]) => void, errorCb?: (err: any) => void): void;
    private createWidgetInstances;
    private doCreateWidgetInstances;
    private loadWidgets;
    private getLoadWidgetPromises;
    static get instance(): IServiceLocator;
    static setInstanceProvider(instanceProvider: () => IServiceLocator): void;
    private static emitOnReady;
    private static events;
    static onReady(handler: Function): void;
    private static get instanceProvider();
    private static set instanceProvider(value);
    private static _instanceProvider;
}
export interface IServiceLocator {
    addServiceInterceptor(id: string, interceptorOrMembers: Function | string[], interceptor?: Function): Function;
    getService(id: string): any;
    getWidget(ids: string | string[], cb: (...instance: ViewWidget[]) => void, errorCb?: (err: any) => void): void;
}
export declare class CallbackExecutionError extends Error {
    innerException: Error;
    name: string;
    message: string;
    constructor(innerException: Error);
}
