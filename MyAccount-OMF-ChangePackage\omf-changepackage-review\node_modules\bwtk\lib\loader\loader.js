/* eslint-disable */
"use strict";
var BwtkLoader = function() {
  if(typeof window.bwtkRequireJS === "undefined") {
    throw new Error ("BwtkLoader needs RequireJS, which was not found.");
  }

  var self = this,
    config = { // settings configurable via BwtkLoader API
      baseUrl: "../node_modules/", 
      registryPath: "", 
      devMode: false, 
      paths: {}, 
      aliases: {}, 
      additionalDependencies: [],
      context: ""
    },
    bwtkDependencies = [
      "redux", 
      "react",
      "react-dom",
      "rxjs",
      "redux-actions",
      "react-redux",
      "react-intl",
      "bwtk",
      "redux-observable"
    ],
    require = window.bwtkRequireJS.require,
    loading = false;

  self.reset = function() {
    for (var k in require.s.contexts) {
      var c = require.s.contexts[k];
      for (var d in c.defined) {
        c.require.undef(d);
      }
      delete require.s.contexts[k];
      delete window.bwtkRequireJS[k];
    }
    return self;
  };

  // Should be overwritten
  self.onReady = function() {};

  self.setPath = function(module_id, module_path) {
    var path = module_path.replace(/\.js$/i, "");
    config.paths[module_id] = path;
    if (!(module_id in bwtkDependencies)) {
      config.additionalDependencies.push(module_id);
    }
    return self;
  };

  self.setPaths = function(modules) {
    Object.keys(modules).forEach(function(k) {
      self.setPath(k, modules[k]);
    }); 
    return self;
  };

  self.setBaseUrl = function(url) {
    config.baseUrl = url;
    return self;
  };

  self.addWidget = function(widget, path){
    var _path = typeof path === "undefined" ? config.registryPath : path;
    _path = _path.replace("{WIDGET}", widget);
    
    self.setPath(widget, _path);
    config.additionalDependencies.push(widget);
    return self;
  };

  self.setBundle = function(path){
    this.setPath("bundle", path);
    return self;
  };

  self.start = function(callback) {
    loading = true;
    if (typeof callback === "function") {
      self.onReady = callback;
    }
    if(typeof window.BwtkPolyfill !== "undefined") {
      window.BwtkPolyfill.ready(init);
    } else {
      init();
    }
  };

  self.devMode = function() {
    config.devMode = true;
    return self;
  };

  self.loading = function() {
    return loading;
  }

  function init(){
    var rjsConfig = buildConfig();
    var context = rjsConfig.context;
    var req = require.config(rjsConfig);
    // expose define now  (after config() call) to ensure it runs in the right context
    window.define = window.bwtkRequireJS.define; // must be set globally 
    defineModules();
    if(context) {
      window.bwtkRequireJS[context] = {require: req}; // this makes the correct version of require() accessible inside of bwtk
    }

    // ++ build a list of dependencies
    var deps = ["bwtk"];
    if (rjsConfig.paths.bundle) {
      deps.push("bundle");
    }
    config.additionalDependencies.forEach(function(dep){
      deps.push(dep);
    });
    deps = self.uniqueArray(deps);
    // -- build a list of dependencies

    // initiate loading
    req(deps, function() {
      self.onReady(req, context ? context: undefined);
      loading = false;
    });
  };

  self.setRegistry = function(format) {
    config.registryPath = format;
    return self;
  };

  self.addAlias = function(oldName, newName) {
    config.aliases[newName] = oldName;
    return self;
  };

  self.multiVersion = function() {
    config.context = "_" + Math.random().toString(36).substr(2, 9);
    return self;
  };

  function buildConfig() {
    var devMode = config.devMode;
    // RequireJS configuration object with default settings
    var rConfig = {
      enforceDefine: true,
      deps: ["bwtk"],
      baseUrl: config.baseUrl,
      paths: {
        "redux":            "redux/dist/redux" + (devMode ? "" : ".min"),
        "react":            "react/umd/react." + (devMode ? "development" : "production.min"),
        "react-dom":        "react-dom/umd/react-dom." + (devMode ? "development" : "production.min"),
        "rxjs":             "rxjs/bundles/Rx" + (devMode ? "" : ".min"),
        "redux-actions":    "redux-actions/dist/redux-actions" + (devMode ? "" : ".min"),
        "react-redux":      "react-redux/dist/react-redux" + (devMode ? "" : ".min"),
        "react-intl":       "react-intl/dist/react-intl" + (devMode ? "" : ".min"),
        "bwtk":             "bwtk/dist/bwtk" + (devMode ? "" : ".min"),
        "redux-observable": "redux-observable/dist/redux-observable" + (devMode ? "" : ".min")
      },
      onNodeCreated: function(node) {
        node.setAttribute("crossorigin", "anonymous");
      }
    }; 

    if(config.context) {
      rConfig.context = config.context;
    }

    Object.keys(config.paths).forEach(function(k){
      rConfig.paths[k] = config.paths[k];
    });

    if(!window.Intl) {
      if(!rConfig.paths.intl) {
        rConfig.paths["intl"] = "intl/dist/Intl.min";
        rConfig.shim = {bwtk: {deps: ["intl"] }};
      }
    } else if(rConfig.paths.intl) {
      delete rConfig.paths.intl;     
    }

    if (Object.keys(config.aliases).length) {
      rConfig.map = {"*": config.aliases};
    }

    return rConfig;
  }

  function defineModules() {
    // define() is set in the init() call and runs in its own context

    // ++ Remap redux-observable dependencies since it's not handled internally
    define("rxjs/Observable", ["rxjs"], function(Rx) {
      return Rx;
    });
    define("rxjs/Subject", ["rxjs"], function(Rx) {
      return Rx;
    });  
    define("rxjs/operator/filter", ["rxjs"], function(Rx) {
      return Rx.Observable.prototype;
    });
    define("rxjs/operator/map", ["rxjs"], function(Rx) {
      return Rx.Observable.prototype;
    });
    define("rxjs/operator/switch", ["rxjs"], function(Rx) {
      return Rx.Observable.prototype;
    });  
    define("rxjs/operator/switchMap", ["rxjs"], function(Rx) {
      return Rx.Observable.prototype;
    });
    define("rxjs/observable/from", ["rxjs"], function(Rx) {
      return Rx.Observable;
    });
    define("rxjs/observable/merge", ["rxjs"], function(Rx) {
      return Rx.Observable;
    });
    define("rxjs/observable/of", ["rxjs"], function(Rx) {
      return Rx.Observable;
    });
    // -- Remap redux-observable dependencies since it's not handled internally
  }

  // ++ Helpers
  self.uniqueArray = function(arrArg) {
    return arrArg.filter(function(elem, pos,arr) {
      return arr.indexOf(elem) == pos;
    });
  };
  // -- Helpers

  return self;
};
