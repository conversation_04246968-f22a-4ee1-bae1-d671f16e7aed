import { StoreServices, ConfigServices } from "../../../@types";
export declare class DefaultConfigServices implements ConfigServices {
    private get id();
    private readonly store;
    constructor(storeServices: StoreServices);
    init(): void;
    private validateConfig;
    private splitKey;
    private emitChangesAsync;
    private emitChanges;
    createKey(globalKey: string, target?: string): string;
    setDefaultConfig(key: string, value: any): void;
    setConfig(key: string, value: any): void;
    private dispatch;
    getConfig<TValue>(key: string, defaultValue?: any): TValue;
    private getConfigState;
    private getDefaultConfigState;
    onChange(key: string, cb: (oldValue: string, newValue: string) => void): {
        dispose: () => void;
    };
    private emitter;
}
