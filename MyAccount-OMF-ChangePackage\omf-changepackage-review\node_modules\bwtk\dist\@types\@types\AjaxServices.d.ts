export declare abstract class AjaxServices implements Client {
    abstract createClient(options?: AjaxOptions): Client;
    abstract get<T>(path: string, data?: any): Promise<T>;
    abstract post<T>(path: string, data: any): Promise<T>;
    abstract head<T>(path: string): Promise<T>;
    abstract put<T>(path: string, data: any): Promise<T>;
    abstract patch<T>(path: string, data: any): Promise<T>;
    abstract del(path: string): Promise<any>;
}
export interface Client {
    get<T>(path: string, data?: any, options?: AjaxOptions): Promise<T>;
    post<T>(path: string, data: any, options?: AjaxOptions): Promise<T>;
    head<T>(path: string, options?: AjaxOptions): Promise<T>;
    put<T>(path: string, data: any, options?: AjaxOptions): Promise<T>;
    patch<T>(path: string, data: any, options?: AjaxOptions): Promise<T>;
    del(path: string, options?: AjaxOptions): Promise<any>;
}
export interface CustomAjaxOptions {
    url?: string;
    dataType?: string;
    timeout?: number;
    [key: string]: any;
}
export interface AjaxOptions extends RequestInit, CustomAjaxOptions {
    headers?: any;
    cache?: any;
    [key: string]: any;
}
export interface AjaxResponse {
    headers: {
        [key: string]: any;
    };
    redirected: boolean;
    status: number;
    statusText: string;
    type: string;
    url: string;
    dataType: string;
    data: any;
}
