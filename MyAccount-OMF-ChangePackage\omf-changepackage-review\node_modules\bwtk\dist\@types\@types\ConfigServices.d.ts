export declare abstract class ConfigServices {
    abstract init(): void;
    abstract createKey(globalKey: string, target?: string): string;
    abstract setDefaultConfig(key: string, value: any): void;
    abstract setConfig(key: string, value: any): void;
    abstract getConfig<TValue>(key: string, defaultValue?: any): TValue;
    abstract onChange(key: string, cb: (oldValue: string, newValue: string) => void): Disposable;
}
export declare class Actions {
    static get SET_DEFAULT_CONFIG(): string;
    static get SET_CONFIG(): string;
}
export interface Disposable {
    dispose(): void;
}
