import { ModuleLoader } from "./ModuleLoader";
import { IModuleDefinition } from "../StaticWidgetLoaderServices";
export declare class ModuleResolver {
    private moduleId;
    private registry;
    private loader;
    constructor(moduleId: string, registry: {
        [key: string]: IModuleDefinition;
    }, loader: ModuleLoader);
    resolve(): Promise<any>;
    private get factory();
    private get isGlobalScript();
    private get globalScript();
    private get moduleIdAsGlobalVar();
}
