/*! omf-changepackage-appointment (widget) 0.1.0 | bwtk 6.1.0 | 2025-08-25T15:23:40.528Z */
!function(e,t){var n,r;if("object"==typeof exports&&"object"==typeof module)module.exports=t(require("react-redux"),require("omf-changepackage-components"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("rxjs/operators"),require("react"),require("react-intl"));else if("function"==typeof define&&define.amd)define(["react-redux","omf-changepackage-components","bwtk","redux","redux-actions","redux-observable","rxjs","rxjs/operators","react","react-intl"],t);else for(r in n="object"==typeof exports?t(require("react-redux"),require("omf-changepackage-components"),require("bwtk"),require("redux"),require("redux-actions"),require("redux-observable"),require("rxjs"),require("rxjs/operators"),require("react"),require("react-intl")):t(e.ReactRedux,e.OMFChangepackageComponents,e.bwtk,e.Redux,e.ReduxActions,e.ReduxObservable,e.rxjs,e.Rx,e.React,e.ReactIntl))("object"==typeof exports?exports:e)[r]=n[r]}(self,function(e,t,n,r,a,s,i,o,l,c){return function(){"use strict";function d(e){var t,n=En[e];return void 0!==n?n.exports:(t=En[e]={exports:{}},An[e](t,t.exports,d),t.exports)}function u(e,t){function n(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");O(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function f(e,t,n,r){var a,s,i=arguments.length,o=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(s=e.length-1;s>=0;s--)(a=e[s])&&(o=(i<3?a(o):i>3?a(t,n,o):a(t,n))||o);return i>3&&o&&Object.defineProperty(t,n,o),o}function p(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function m(e,t){var n,r,a,s,i="function"==typeof Symbol&&e[Symbol.iterator];if(!i)return e;n=i.call(e),a=[];try{for(;(void 0===t||t-- >0)&&!(r=n.next()).done;)a.push(r.value)}catch(o){s={error:o}}finally{try{r&&!r.done&&(i=n.return)&&i.call(n)}finally{if(s)throw s.error}}return a}function h(e,t,n){if(n||2===arguments.length)for(var r,a=0,s=t.length;a<s;a++)!r&&a in t||(r||(r=Array.prototype.slice.call(t,0,a)),r[a]=t[a]);return e.concat(r||Array.prototype.slice.call(t))}function g(e){let t;const n=Array.isArray(e),r="undefined"!=typeof FileList&&e instanceof FileList;if(e instanceof Date)t=new Date(e);else{if(Ve&&(e instanceof Blob||r)||!n&&!Le(e))return e;if(t=n?[]:Object.create(Object.getPrototypeOf(e)),n||Me(e))for(const n in e)e.hasOwnProperty(n)&&(t[n]=g(e[n]));else t=e}return t}function y(e,t,n=new WeakSet){if(Ye(e)||Ye(t))return e===t;if(Ce(e)&&Ce(t))return e.getTime()===t.getTime();const r=Object.keys(e),a=Object.keys(t);if(r.length!==a.length)return!1;if(n.has(e)||n.has(t))return!0;n.add(e),n.add(t);for(const s of r){const r=e[s];if(!a.includes(s))return!1;if("ref"!==s){const e=t[s];if(Ce(r)&&Ce(e)||Le(r)&&Le(e)||Array.isArray(r)&&Array.isArray(e)?!y(r,e,n):r!==e)return!1}}return!0}function x(e,t){const n=Array.isArray(t)?t:Be(t)?[t]:Ue(t),r=1===n.length?e:function(e,t){const n=t.slice(0,-1).length;let r=0;for(;r<n;)e=qe(e)?r++:e[t[r++]];return e}(e,n),a=n.length-1,s=n[a];return r&&delete r[s],0!==a&&(Le(r)&&et(r)||Array.isArray(r)&&function(e){for(const t in e)if(e.hasOwnProperty(t)&&!qe(e[t]))return!1;return!0}(r))&&x(e,n.slice(0,-1)),e}function b(e,t={}){const n=Array.isArray(e);if(Le(e)||n)for(const r in e)Array.isArray(e[r])||Le(e[r])&&!lt(e[r])?(t[r]=Array.isArray(e[r])?[]:{},b(e[r],t[r])):we(e[r])||(t[r]=!0);return t}function v(e,t,n){const r=Array.isArray(e);if(Le(e)||r)for(const a in e)Array.isArray(e[a])||Le(e[a])&&!lt(e[a])?qe(t)||Ye(n[a])?n[a]=Array.isArray(e[a])?b(e[a],[]):{...b(e[a])}:v(e[a],we(t)?{}:t[a],n[a]):n[a]=!y(e[a],t[a]);return n}function A(e){const t=e.ref;return tt(t)?t.files:st(t)?ft(e.refs).value:at(t)?[...t.selectedOptions].map(({value:e})=>e):Fe(t)?dt(e.refs).value:ut(qe(t.value)?e.ref.value:t.value,e)}function E(e,t,n){const r=We(e,n);if(r||Be(n))return{error:r,name:n};const a=n.split(".");for(;a.length;){const r=a.join("."),s=We(t,r),i=We(e,r);if(s&&!Array.isArray(s)&&n!==r)return{name:n};if(i&&i.type)return{name:r,error:i};if(i&&i.root&&i.root.type)return{name:`${r}.root`,error:i.root};a.pop()}return{name:n}}function N(e,t,n="validate"){if(_t(e)||Array.isArray(e)&&e.every(_t)||ze(e)&&!e)return{type:n,message:_t(e)?e:"",ref:t}}function S(e){var t,n;try{return t=e.split("T"),(n=new Date(t[0])).setMinutes(new Date(e).getMinutes()+new Date(e).getTimezoneOffset()),n.setHours(0),n.setMinutes(0),n}catch(r){return e}}function _(e,t){return Object.keys(e).map(function(t){return e[t]}).map(function(e){return t(e)})}function T(e){var t=j(e);return 10===(t=t.substr(0,10)).length?t.slice(0,3)+"-"+t.slice(3,6)+"-"+t.slice(6):t}function j(e){return e.replace(/\D/g,"")}var D,I,O,F,C,w,L,R,k,P,M,V,B,q,H,U,W,z,G,X,$,J,Y,Z,Q,K,ee,te,ne,re,ae,se,ie,oe,le,ce,de,ue,fe,pe,me,he,ge,ye,xe,be,ve,Ae,Ee,Ne,Se,_e,Te,je,De,Ie,Oe,Fe,Ce,we,Le,Re,ke,Pe,Me,Ve,Be,qe,He,Ue,We,ze,Ge,Xe,$e,Je,Ye,Ze,Qe,Ke,et,tt,nt,rt,at,st,it,ot,lt,ct,dt,ut,ft,pt,mt,ht,gt,yt,xt,bt,vt,At,Et,Nt,St,_t,Tt,jt,Dt,It,Ot,Ft,Ct,wt,Lt,Rt,kt,Pt,Mt,Vt,Bt,qt,Ht,Ut,Wt,zt,Gt,Xt,$t,Jt,Yt,Zt,Qt,Kt,en,tn,nn,rn,an,sn,on,ln,cn,dn,un,fn,pn,mn,hn,gn,yn,xn,bn,vn,An={102:function(e){e.exports=n},418:function(e){e.exports=i},419:function(e){e.exports=c},442:function(e){e.exports=l},446:function(e){e.exports=t},493:function(e,t,n){e.exports=n(557)},541:function(e){e.exports=a},557:function(e,t){function n(e,t,n){var a,s=null;if(void 0!==n&&(s=""+n),void 0!==t.key&&(s=""+t.key),"key"in t)for(a in n={},t)"key"!==a&&(n[a]=t[a]);else n=t;return t=n.ref,{$$typeof:r,type:e,key:s,ref:void 0!==t?t:null,props:n}}
/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
var r=Symbol.for("react.transitional.element"),a=Symbol.for("react.fragment");t.Fragment=a,t.jsx=n,t.jsxs=n},750:function(e){e.exports=r},769:function(e){e.exports=s},843:function(e){e.exports=o},999:function(t){t.exports=e}},En={};d.d=function(e,t){for(var n in t)d.o(t,n)&&!d.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},d.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},d.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},D={},d.r(D),d.d(D,{default:function(){return vn}}),I={},d.r(I),d.d(I,{contactInformation:function(){return W},getAppointment:function(){return q},getOderDetails:function(){return B},initSlickSlider:function(){return Y},setAdditionalDetails:function(){return X},setAppointment:function(){return H},setAvailableDates:function(){return U},setDuration:function(){return z},setForErrors:function(){return J},setInstallationAddress:function(){return G},setIsInstallationRequired:function(){return $}}),O=function(e,t){return O=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},O(e,t)},F=function(){return F=Object.assign||function(e){var t,n,r,a;for(n=1,r=arguments.length;n<r;n++)for(a in t=arguments[n])Object.prototype.hasOwnProperty.call(t,a)&&(e[a]=t[a]);return e},F.apply(this,arguments)},Object.create,Object.create,C=function(e){return C=Object.getOwnPropertyNames||function(e){var t,n=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(n[n.length]=t);return n},C(e)},"function"==typeof SuppressedError&&SuppressedError,w=d(493),L=d(999),R=d(446),k=d(102),P=d(750),M=d(541),V=d(769),B=(0,M.createAction)("GET_ORDER_DETAILS"),q=(0,M.createAction)("GET_APPOINTMENT"),H=(0,M.createAction)("SET_APPOINTMENT"),U=(0,M.createAction)("SET_AVAIALBLE_DATES"),W=(0,M.createAction)("SET_CONTACT_INFO"),z=(0,M.createAction)("SET_DURATION"),G=(0,M.createAction)("SET_INSTALLATION_ADDRESS"),X=(0,M.createAction)("SET_ADDITIONAL_DETAILS"),$=(0,M.createAction)("SET_INSTALLATION_REQUIRED"),J=(0,M.createAction)("SET_FORM_ERRORS"),Y=(0,M.createAction)("INIT_SLICK_SLIDER"),Z=d(418),Q=k.CommonFeatures.BaseConfig,K=k.CommonFeatures.configProperty,ee=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return u(t,e),f([K({}),p("design:type",Object)],t.prototype,"headers",void 0),f([K({}),p("design:type",Object)],t.prototype,"environmentVariables",void 0),f([K({}),p("design:type",Object)],t.prototype,"mockdata",void 0),f([K({base:"http://127.0.0.1:8881",orderDetailsAPI:"/",appointmentAPI:"/",orderSubmitAPI:"/"}),p("design:type",Object)],t.prototype,"api",void 0),f([k.Injectable],t)}(Q),te=function(e){function t(t,n){return e.call(this,t,n)||this}return u(t,e),f([k.Injectable,p("design:paramtypes",[k.AjaxServices,ee])],t)}(R.BaseClient),ne={availableDates:null,duration:"",installationAddress:{address1:"",address2:"",city:"",province:"",postalCode:"",apartmentType:"",apartmentNumber:""},contactInformation:{preferredContactMethod:"",primaryPhone:{phoneNumber:"",phoneExtension:""},mobileNumber:null,additionalPhone:{phoneNumber:"",phoneExtension:""},textMessage:"",email:""},additionalDetails:{apartment:"",entryCode:"",specialInstructions:"",superintendantName:"",superintendantPhone:"",informedSuperintendant:null},isInstallationRequired:null},re=function(){function e(){}return e.create=function(e,t,n){var r,a;return t.installationAddress.address1=n.installationAddress&&n.installationAddress.address1?n.installationAddress.address1:"",t.installationAddress.address2=n.installationAddress&&n.installationAddress.address2?n.installationAddress.address2:"",t.installationAddress.city=n.installationAddress&&n.installationAddress.city?n.installationAddress.city:"",t.installationAddress.postalCode=n.installationAddress&&n.installationAddress.postalCode?n.installationAddress.postalCode:"",t.installationAddress.province=n.installationAddress&&n.installationAddress.province?n.installationAddress.province:"",t.installationAddress.apartmentType=n.installationAddress&&n.installationAddress.province?n.installationAddress.apartmentType:"",t.installationAddress.apartmentNumber=n.installationAddress&&n.installationAddress.province?n.installationAddress.apartmentNumber:"",t.isInstallationRequired=n.isInstallationRequired,t.duration=n.duration,t.contactInformation.primaryPhone.phoneNumber=e.Phone_LABEL?e.Phone_LABEL:"",t.contactInformation.primaryPhone.phoneExtension=e.Phone_EXT?e.Phone_EXT:"",t.contactInformation.additionalPhone.phoneNumber=e.ADDITIONAL_PHONE_NUMBER,t.contactInformation.additionalPhone.phoneExtension=e.ADDITIONAL_PHONE_EXT,t.contactInformation.preferredContactMethod=e.PREFERED_METHOD_OF_CONTACT,t.contactInformation.email=e.Email_LABEL?e.Email_LABEL:"",t.contactInformation.textMessage=e.TextMessage_LABEL?e.TextMessage_LABEL:"",t.availableDates=(r=n.availableDates,a=JSON.parse(e.dateAndTime),null==r||r.forEach(function(e){e.timeSlots.forEach(function(e){return e.isSelected=!1})}),null==r||r.forEach(function(e){return e.timeSlots.forEach(function(t){return t.isSelected=!(e.date!==a.date||!a.timeSlots.map(function(e){return e.intervalType===t.intervalType}))})}),r),t.additionalDetails.apartment=e.APPARTMENT,t.additionalDetails.entryCode=e.ENTRY_CODE,t.additionalDetails.informedSuperintendant=e.INFORMED_SUPERINTENDANT,t.additionalDetails.specialInstructions=e.SPECIAL_INSTRUCTIONS,t.additionalDetails.superintendantName=e.SUPERINTENDANT_NAME,t.additionalDetails.superintendantPhone=e.SUPERINTENDANT_PHONE,t},e}(),ae=d(843),se=R.Actions.errorOccured,ie=R.Actions.setWidgetStatus,oe=R.Actions.setProductConfigurationTotal,le=R.Actions.broadcastUpdate,ce=R.Actions.historyGo,de=R.Actions.clearCachedState,ue=R.Actions.omniPageLoaded,fe=function(){function e(e,t){this.client=e,this.config=t,this.widgetState=R.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return(0,V.combineEpics)(this.appointmentEpic,this.submitAppointmentEpic)},Object.defineProperty(e.prototype,"appointmentEpic",{get:function(){var e=this;return function(t){return t.pipe((0,V.ofType)(q.toString()),(0,ae.filter)(function(){return e.widgetState!==R.EWidgetStatus.UPDATING}),(0,ae.mergeMap)(function(){var t=ie(e.widgetState=R.EWidgetStatus.UPDATING);return e.client.get(e.config.api.appointmentAPI).pipe((0,ae.mergeMap)(function(n){var r=n.data,a=[U(r.appointment.availableDates),z(r.appointment.duration),G(r.appointment.installationAddress),W(r.appointment.contactInformation),X(r.appointment.additionalDetails),$(r.appointment.isInstallationRequired),le(oe((0,R.ValueOf)(r,"productConfigurationTotal"))),ie(e.widgetState=R.EWidgetStatus.RENDERED),ue()];return h([t],m(a),!1)}))}),(0,ae.catchError)(function(e){return(0,Z.of)(se(new R.Models.ErrorHandler("getAppointment",e)))}))}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"submitAppointmentEpic",{get:function(){var e=this;return function(t,n){return t.pipe((0,V.ofType)(H.toString()),(0,ae.filter)(function(){return e.widgetState!==R.EWidgetStatus.UPDATING}),(0,ae.mergeMap)(function(t){var r=t.payload,a=ie(e.widgetState=R.EWidgetStatus.UPDATING);return e.client.put(e.config.api.appointmentAPI,re.create(r,ne,n.getState())).pipe((0,ae.mergeMap)(function(){var e=[le(ce(R.EWidgetRoute.REVIEW)),de([R.EWidgetName.REVIEW])];return h([a],m(e),!1)}))}),(0,ae.catchError)(function(e){return(0,Z.of)(se(new R.Models.ErrorHandler("getAppointment",e)))}))}},enumerable:!1,configurable:!0}),f([k.Injectable,p("design:paramtypes",[te,ee])],e)}(),pe=R.Actions.omniPageLoaded,me=function(){function e(){this.widgetState=R.EWidgetStatus.INIT}return e.prototype.combineEpics=function(){return(0,V.combineEpics)(this.pageLoadedEpic)},Object.defineProperty(e.prototype,"pageLoadedEpic",{get:function(){return function(e,t){return e.pipe((0,V.ofType)(pe.toString()),(0,Z.mergeMap)(function(){var e,t,n=R.Omniture.useOmniture();switch(R.Utils.getFlowType()){case R.EFlowType.INTERNET:t="Internet",e="Change package";break;case R.EFlowType.TV:case R.EFlowType.ADDTV:break;case R.EFlowType.BUNDLE:t="Bundle",e="Add Tv"}return n.trackPage({id:"AppointmentPage",s_oSS1:"~",s_oSS2:t||"~",s_oSS3:e||"Change package",s_oPGN:"Installation",s_oPLE:{type:R.Omniture.EMessageType.Warning,content:{ref:"IstallationMessageBanner"}}}),(0,Z.of)([])}),(0,Z.catchError)(function(e){return(0,Z.of)([])}))}},enumerable:!1,configurable:!0}),f([k.Injectable],e)}(),he=R.Actions.setWidgetStatus,ge=R.Actions.broadcastUpdate,ye=R.Actions.setAppointmentVisited,xe=function(){function e(e,t){this.omnitureEpics=e,this.appointmentEpics=t}return e.prototype.combineEpics=function(){return(0,V.combineEpics)(this.onWidgetStatusEpic)},Object.defineProperty(e.prototype,"onWidgetStatusEpic",{get:function(){return function(e){return e.pipe((0,V.ofType)(he.toString()),(0,Z.filter)(function(e){return e.payload===R.EWidgetStatus.INIT}),(0,Z.mergeMap)(function(){return(0,Z.of)(ge(ye()),q())}))}},enumerable:!1,configurable:!0}),f([k.Injectable,p("design:paramtypes",[me,fe])],e)}(),be=k.CommonFeatures.BaseLocalization,ve=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}var n;return u(t,e),n=t,t.getLocalizedString=function(e){n.Instance=n.Instance||k.ServiceLocator.instance.getService(k.CommonServices.Localization);var t=n.Instance;return t?t.getLocalizedString("omf-changepackage-appointment",e,t.locale):e},t.Instance=null,n=f([k.Injectable],t)}(be),Ae=k.CommonFeatures.BaseStore,Ee=(0,k.CommonFeatures.actionsToComputedPropertyName)(I),Ne=Ee.setAvailableDates,Se=Ee.setDuration,_e=Ee.setInstallationAddress,Te=Ee.contactInformation,je=Ee.setAdditionalDetails,De=Ee.setIsInstallationRequired,Ie=function(e){function t(t,n,r,a){var s=e.call(this,n)||this;return s.client=t,s.epics=r,s.localization=a,s}return u(t,e),Object.defineProperty(t.prototype,"reducer",{get:function(){var e,t,n,r,a,s;return(0,P.combineReducers)(F(F(F(F({},R.Reducers.WidgetBaseLifecycle(this.localization)),R.Reducers.WidgetLightboxes()),R.Reducers.WidgetRestrictions()),{availableDates:(0,M.handleActions)((e={},e[Ne]=function(e,t){return t.payload||e},e),null),duration:(0,M.handleActions)((t={},t[Se]=function(e,t){return t.payload||e},t),null),installationAddress:(0,M.handleActions)((n={},n[_e]=function(e,t){return t.payload||e},n),null),contactInformation:(0,M.handleActions)((r={},r[Te]=function(e,t){return t.payload||e},r),null),additionalDetails:(0,M.handleActions)((a={},a[je]=function(e,t){return t.payload||e},a),null),isInstallationRequired:(0,M.handleActions)((s={},s[De]=function(e,t){return t.payload||e},s),!1)}))},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"middlewares",{get:function(){return(0,V.combineEpics)(this.epics.omnitureEpics.combineEpics(),this.epics.appointmentEpics.combineEpics(),this.epics.combineEpics(),(new R.ModalEpics).combineEpics(),new R.RestricitonsEpics(this.client,"APPOINTMENT_RESTRICTION_MODAL").combineEpics(),(new R.LifecycleEpics).combineEpics())},enumerable:!1,configurable:!0}),f([k.Injectable,p("design:paramtypes",[te,k.Store,xe,ve])],t)}(Ae),Oe=d(442),Fe=e=>"checkbox"===e.type,Ce=e=>e instanceof Date,we=e=>null==e;const Nn=e=>"object"==typeof e;Le=e=>!we(e)&&!Array.isArray(e)&&Nn(e)&&!Ce(e),Re=e=>Le(e)&&e.target?Fe(e.target)?e.target.checked:e.target.value:e,ke=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,Pe=(e,t)=>e.has(ke(t)),Me=e=>{const t=e.constructor&&e.constructor.prototype;return Le(t)&&t.hasOwnProperty("isPrototypeOf")},Ve="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document,Be=e=>/^\w*$/.test(e),qe=e=>void 0===e,He=e=>Array.isArray(e)?e.filter(Boolean):[],Ue=e=>He(e.replace(/["|']|\]/g,"").split(/\.|\[/)),We=(e,t,n)=>{if(!t||!Le(e))return n;const r=(Be(t)?[t]:Ue(t)).reduce((e,t)=>we(e)?e:e[t],e);return qe(r)||r===e?qe(e[t])?n:e[t]:r},ze=e=>"boolean"==typeof e,Ge=(e,t,n)=>{let r=-1;const a=Be(t)?[t]:Ue(t),s=a.length,i=s-1;for(;++r<s;){const t=a[r];let s=n;if(r!==i){const n=e[t];s=Le(n)||Array.isArray(n)?n:isNaN(+a[r+1])?{}:[]}if("__proto__"===t||"constructor"===t||"prototype"===t)return;e[t]=s,e=e[t]}};const Sn="blur",_n="focusout",Tn="onChange",jn="onSubmit",Dn="all",In="pattern",On="required",Fn=Oe.createContext(null);Fn.displayName="HookFormContext";const Cn=()=>Oe.useContext(Fn),wn=e=>{const{children:t,...n}=e;return Oe.createElement(Fn.Provider,{value:n},t)};Xe=(e,t,n,r=!0)=>{const a={defaultValues:t._defaultValues};for(const s in e)Object.defineProperty(a,s,{get:()=>{const a=s;return t._proxyFormState[a]!==Dn&&(t._proxyFormState[a]=!r||Dn),n&&(n[a]=!0),e[a]}});return a};const Ln="undefined"!=typeof window?Oe.useLayoutEffect:Oe.useEffect;$e=e=>"string"==typeof e,Je=(e,t,n,r,a)=>$e(e)?(r&&t.watch.add(e),We(n,e,a)):Array.isArray(e)?e.map(e=>(r&&t.watch.add(e),We(n,e))):(r&&(t.watchAll=!0),n),Ye=e=>we(e)||!Nn(e),Ze=(e,t,n,r,a)=>t?{...n[e],types:{...n[e]&&n[e].types?n[e].types:{},[r]:a||!0}}:{},Qe=e=>Array.isArray(e)?e:[e],Ke=()=>{let e=[];return{get observers(){return e},next:t=>{for(const n of e)n.next&&n.next(t)},subscribe:t=>(e.push(t),{unsubscribe:()=>{e=e.filter(e=>e!==t)}}),unsubscribe:()=>{e=[]}}},et=e=>Le(e)&&!Object.keys(e).length,tt=e=>"file"===e.type,nt=e=>"function"==typeof e,rt=e=>{if(!Ve)return!1;const t=e?e.ownerDocument:0;return e instanceof(t&&t.defaultView?t.defaultView.HTMLElement:HTMLElement)},at=e=>"select-multiple"===e.type,st=e=>"radio"===e.type,it=e=>st(e)||Fe(e),ot=e=>rt(e)&&e.isConnected,lt=e=>{for(const t in e)if(nt(e[t]))return!0;return!1},ct=(e,t)=>v(e,t,b(t));const Rn={value:!1,isValid:!1},kn={value:!0,isValid:!0};dt=e=>{if(Array.isArray(e)){if(e.length>1){const t=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:t,isValid:!!t.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!qe(e[0].attributes.value)?qe(e[0].value)||""===e[0].value?kn:{value:e[0].value,isValid:!0}:kn:Rn}return Rn},ut=(e,{valueAsNumber:t,valueAsDate:n,setValueAs:r})=>qe(e)?e:t?""===e?NaN:e?+e:e:n&&$e(e)?new Date(e):r?r(e):e;const Pn={isValid:!1,value:null};ft=e=>Array.isArray(e)?e.reduce((e,t)=>t&&t.checked&&!t.disabled?{isValid:!0,value:t.value}:e,Pn):Pn,pt=(e,t,n,r)=>{const a={};for(const s of e){const e=We(t,s);e&&Ge(a,s,e._f)}return{criteriaMode:n,names:[...e],fields:a,shouldUseNativeValidation:r}},mt=e=>e instanceof RegExp,ht=e=>qe(e)?e:mt(e)?e.source:Le(e)?mt(e.value)?e.value.source:e.value:e,gt=e=>({isOnSubmit:!e||e===jn,isOnBlur:"onBlur"===e,isOnChange:e===Tn,isOnAll:e===Dn,isOnTouch:"onTouched"===e});const Mn="AsyncFunction";yt=e=>!!e&&!!e.validate&&!!(nt(e.validate)&&e.validate.constructor.name===Mn||Le(e.validate)&&Object.values(e.validate).find(e=>e.constructor.name===Mn)),xt=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate),bt=(e,t,n)=>!n&&(t.watchAll||t.watch.has(e)||[...t.watch].some(t=>e.startsWith(t)&&/^\.\w+/.test(e.slice(t.length))));const Vn=(e,t,n,r)=>{for(const a of n||Object.keys(e)){const n=We(e,a);if(n){const{_f:e,...s}=n;if(e){if(e.refs&&e.refs[0]&&t(e.refs[0],a)&&!r)return!0;if(e.ref&&t(e.ref,e.name)&&!r)return!0;if(Vn(s,t))break}else if(Le(s)&&Vn(s,t))break}}};vt=(e,t,n,r)=>{n(e);const{name:a,...s}=e;return et(s)||Object.keys(s).length>=Object.keys(t).length||Object.keys(s).find(e=>t[e]===(!r||Dn))},At=(e,t,n)=>!e||!t||e===t||Qe(e).some(e=>e&&(n?e===t:e.startsWith(t)||t.startsWith(e))),Et=(e,t,n,r,a)=>!a.isOnAll&&(!n&&a.isOnTouch?!(t||e):(n?r.isOnBlur:a.isOnBlur)?!e:!(n?r.isOnChange:a.isOnChange)||e),Nt=(e,t)=>!He(We(e,t)).length&&x(e,t),St=(e,t,n)=>{const r=Qe(We(e,n));return Ge(r,"root",t[n]),Ge(e,n,r),e},_t=e=>$e(e),Tt=e=>Le(e)&&!mt(e)?e:{value:e,message:""},jt=async(e,t,n,r,a,s)=>{const{ref:i,refs:o,required:l,maxLength:c,minLength:d,min:u,max:f,pattern:p,validate:m,name:h,valueAsNumber:g,mount:y}=e._f,x=We(n,h);if(!y||t.has(h))return{};const b=o?o[0]:i,v=e=>{a&&b.reportValidity&&(b.setCustomValidity(ze(e)?"":e||""),b.reportValidity())},A={},E=st(i),S=Fe(i),_=E||S,T=(g||tt(i))&&qe(i.value)&&qe(x)||rt(i)&&""===i.value||""===x||Array.isArray(x)&&!x.length,j=Ze.bind(null,h,r,A),D=(e,t,n,r="maxLength",a="minLength")=>{const s=e?t:n;A[h]={type:e?r:a,message:s,ref:i,...j(e?r:a,s)}};if(s?!Array.isArray(x)||!x.length:l&&(!_&&(T||we(x))||ze(x)&&!x||S&&!dt(o).isValid||E&&!ft(o).isValid)){const{value:e,message:t}=_t(l)?{value:!!l,message:l}:Tt(l);if(e&&(A[h]={type:On,message:t,ref:b,...j(On,t)},!r))return v(t),A}if(!(T||we(u)&&we(f))){let e,t;const n=Tt(f),a=Tt(u);if(we(x)||isNaN(x)){const r=i.valueAsDate||new Date(x),s=e=>new Date((new Date).toDateString()+" "+e),o="time"==i.type,l="week"==i.type;$e(n.value)&&x&&(e=o?s(x)>s(n.value):l?x>n.value:r>new Date(n.value)),$e(a.value)&&x&&(t=o?s(x)<s(a.value):l?x<a.value:r<new Date(a.value))}else{const r=i.valueAsNumber||(x?+x:x);we(n.value)||(e=r>n.value),we(a.value)||(t=r<a.value)}if((e||t)&&(D(!!e,n.message,a.message,"max","min"),!r))return v(A[h].message),A}if((c||d)&&!T&&($e(x)||s&&Array.isArray(x))){const e=Tt(c),t=Tt(d),n=!we(e.value)&&x.length>+e.value,a=!we(t.value)&&x.length<+t.value;if((n||a)&&(D(n,e.message,t.message),!r))return v(A[h].message),A}if(p&&!T&&$e(x)){const{value:e,message:t}=Tt(p);if(mt(e)&&!x.match(e)&&(A[h]={type:In,message:t,ref:i,...j(In,t)},!r))return v(t),A}if(m)if(nt(m)){const e=N(await m(x,n),b);if(e&&(A[h]={...e,...j("validate",e.message)},!r))return v(e.message),A}else if(Le(m)){let e={};for(const t in m){if(!et(e)&&!r)break;const a=N(await m[t](x,n),b,t);a&&(e={...a,...j(t,a.message)},v(a.message),r&&(A[h]=e))}if(!et(e)&&(A[h]={ref:b,...e},!r))return A}return v(!0),A};const Bn={mode:jn,reValidateMode:Tn,shouldFocusError:!0};return function(e){e.PHONE="Phone",e.TEXT_MESSAGE="TextMessage",e.EMAIL="Email"}(Dt||(Dt={})),function(e){e.AM="AM",e.PM="PM",e.Evening="Evening",e.AllDay="AllDay",e.Item0810="Item0810",e.Item1012="Item1012",e.Item1315="Item1315",e.Item1517="Item1517",e.Item1719="Item1719",e.Item1921="Item1921"}(It||(It={})),function(e){e.EMAIL="Email",e.TEXT_MESSAGE="TextMessage",e.PHONE="Phone"}(Ot||(Ot={})),Ft=/^[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+((\.)+[a-z0-9`!#\$%&\*\+\/=\?\^\'\-_]+)*@([a-z0-9]+([\-][a-z0-9])*)+([\.]([a-z0-9]+([\-][a-z0-9])*)+)+$/i,Ct=/^[0-9]\d{2}-\d{3}-\d{4}$/i,wt=function(e){var t,n=[];return R.Utils.getFlowType()===R.EFlowType.INTERNET&&(t=523),R.Utils.getFlowType()===R.EFlowType.BUNDLE&&(t=508),Object.keys(e).map(function(t){var r=e[t];n.push({id:r.ref.name,error:r.type})}),R.Omniture.useOmniture().trackError(n.map(function(e){return{code:e.id,type:R.Omniture.EErrorType.Validation,layer:R.Omniture.EApplicationLayer.Frontend,description:e.error}}),t),n},Lt=function(e,t){var n,r;switch(e){case Dt.EMAIL:return null==t?void 0:t.email;case Dt.PHONE:return(null===(n=null==t?void 0:t.primaryPhone)||void 0===n?void 0:n.phoneNumber)&&T(null===(r=null==t?void 0:t.primaryPhone)||void 0===r?void 0:r.phoneNumber);case Dt.TEXT_MESSAGE:return(null==t?void 0:t.textMessage)&&T(null==t?void 0:t.textMessage);default:return""}},Rt=d(419),kt=function(e){var t=F(F({},Pt),e),n=t.label,r=t.value,a=t.handleChange,s=t.subLabel,i=t.checked,o=Cn().register;return(0,w.jsxs)("div",{className:"flexBlock flexCol-xs margin-15-bottom",children:[(0,w.jsxs)("label",{htmlFor:"additionalPhoneNumber",className:"installation-form-label",children:[(0,w.jsx)("span",{className:"txtBold block",children:(0,w.jsx)(Rt.FormattedMessage,{id:n})}),s?(0,w.jsx)("span",{className:"txtItalic block txtNormal",children:(0,w.jsx)(R.FormattedHTMLMessage,{id:s})}):null]}),(0,w.jsx)("div",{className:"flexCol margin-5-top",children:(0,w.jsxs)("label",{className:"graphical_ctrl graphical_ctrl_checkbox txtNormal",children:[(0,w.jsx)(R.FormattedHTMLMessage,{id:r+"_LABEL"}),(0,w.jsx)("input",{type:"checkbox",ref:o,id:n,name:n,defaultChecked:i,onChange:function(e){return a(e)}}),(0,w.jsx)("span",{className:"ctrl_element chk_radius"})]})})]})},Pt={checked:!1},Mt={checked:!1,requiredInput:!1},Vt=function(e){var t=F(F({},Mt),e),n=t.label,r=t.value,a=t.handleChange,s=t.checked,i=t.requiredInput,o=Cn().register;return n?(0,w.jsxs)("label",{className:"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom",children:[(0,w.jsx)("label",{className:"txtBold block",htmlFor:"option_".concat(r),children:(0,w.jsx)(Rt.FormattedMessage,{id:r})}),(0,w.jsx)("input",{type:"radio",id:"option_".concat(r),ref:o({required:i}),name:n,value:r,checked:s,onChange:function(e){return a(e)}}),(0,w.jsx)("span",{className:"ctrl_element"}),"OTHER"===r&&!0===s?(0,w.jsx)("span",{className:"topArrow text-left otherOption","aria-hidden":"true"}):null]}):null},Bt=function(e){var t=F(F({},qt),e),n=t.label,r=t.required,a=t.value,s=t.subLabel,i=t.handleChange,o=t.requiredInput,l=t.maxLength,c=Cn().register,d=m(Oe.useState((l||0)-(a||"").length),2),u=d[0],f=d[1];return(0,w.jsxs)("div",{className:"flexBlock flexCol-xs margin-15-bottom",children:[(0,w.jsxs)("label",{htmlFor:n,className:"installation-form-label",children:[(0,w.jsx)("span",{className:"txtBold",children:(0,w.jsx)(Rt.FormattedMessage,{id:n})}),r?(0,w.jsx)("span",{className:"txtNormal",children:"(optional)"}):"",s?(0,w.jsx)("span",{className:"txtItalic block txtNormal",children:(0,w.jsx)(R.FormattedHTMLMessage,{id:s})}):null]}),(0,w.jsxs)("div",{className:"flexCol",children:[(0,w.jsx)("textarea",{ref:c({required:o}),id:n,name:n,defaultValue:a,maxLength:l,className:"brf3-textarea form-control",onChange:function(e){f((l||0)-(e.currentTarget.value||"").length),i(e)}}),(0,w.jsx)("p",{children:(0,w.jsx)(Rt.FormattedMessage,{id:n+"_DESCRIPTION",values:{max:l,count:u}})})]})]})},qt={required:!1,requiredInput:!1,value:"",subLabel:""},Ht=function(e){var t=F(F({},Ut),e),n=t.label,r=t.subLabel,a=t.handleChange,s=t.containerClass,i=t.extention,o=t.optionalExtenstion,l=t.requiredInput,c=t.requiredPattern,d=t.value,u=t.subValue,f=Cn(),p=f.register,m=f.errors;return(0,w.jsxs)("div",{className:"flexBlock flexCol-xs margin-15-bottom flexWrap ".concat(s),children:[(0,w.jsxs)("label",{htmlFor:"additionalPhoneNumber",className:"installation-form-label ".concat(l?"form-required":""," ").concat(m&&m[n]?"error":""),children:[(0,w.jsx)("span",{className:"txtBold block",children:(0,w.jsx)(Rt.FormattedMessage,{id:n})}),r?(0,w.jsx)("span",{className:"txtItalic block txtNormal","aria-label":function(e){switch(e){case"TELEPHONE_FORMAT":case"PREFERED_PHONE_FORMAT":case"PREFERED_TEXT_MESSAGE_FORMAT":case"Phone_FORMAT":case"TextMessage_FORMAT":return ve.getLocalizedString("TELEPHONE_FORMAT_ARIA");default:return ve.getLocalizedString(e)}}(r),children:(0,w.jsx)(R.FormattedHTMLMessage,{id:r})}):null]}),(0,w.jsxs)("div",{className:"flexCol relative ".concat(m&&m[n]?"has-error":""),children:[(0,w.jsx)("span",{className:"topArrow text-left hide","aria-hidden":"true"}),(0,w.jsx)("input",{type:"text",ref:p({required:l,pattern:c}),className:"brf3-virgin-form-input form-control",id:n,name:n,title:n,defaultValue:d,onBlur:a,onChange:function(e){return a(e)}}),m&&m[n]?(0,w.jsxs)("span",{className:"error margin-5-top",children:[(0,w.jsxs)("span",{className:"virgin-icon icon-warning margin-15-right","aria-hidden":!0,children:[(0,w.jsx)("span",{className:"volt-icon path1"}),(0,w.jsx)("span",{className:"volt-icon path2"})]}),(0,w.jsx)("span",{className:"txtSize12",children:(0,w.jsx)(R.FormattedHTMLMessage,{id:"pattern"!==m[n].type?"INLINE_ERROR_required":"INLINE_ERROR_".concat(n,"_pattern")})})]}):null]}),i?(0,w.jsx)("div",{className:"flexCol brf3-virgin-form-subInput fill-sm",children:(0,w.jsxs)("div",{className:"flexBlock flexCol-xs",children:[(0,w.jsxs)("label",{htmlFor:"extension",className:"installation-form-label",children:[(0,w.jsx)("span",{className:"txtBold block",children:(0,w.jsx)(Rt.FormattedMessage,{id:i})}),o?(0,w.jsx)("span",{className:"txtItalic block txtNormal",children:(0,w.jsx)(Rt.FormattedMessage,{id:"OPTIONAL_LABEL"})}):null]}),(0,w.jsx)("div",{className:"flexCol",children:(0,w.jsx)("input",{type:"text",ref:p,className:"brf3-virgin-form-input form-control",id:i,name:i,title:i,maxLength:10,defaultValue:u,onBlur:a,onChange:function(e){return a(e)}})})]})}):null]})},Ut={requiredInput:!1,requiredPattern:/.*/i,containerClass:"",value:"",subValue:""},Wt=function(e){var t=e.legend,n=e.required,r=e.accessibleLegend,a=e.legendAdditionalClass;return t?(0,w.jsx)("legend",{className:"installation-form-label ".concat(n?"form-required":""," ").concat(r?"sr-only":""," ").concat(a),children:(0,w.jsx)(Rt.FormattedMessage,{id:t})}):null},zt=function(e){var t=e.className,n=e.children,r=e.legend,a=e.accessibleLegend,s=e.legendAdditionalClass,i=e.required,o=e.additionalClass;return(0,w.jsx)("fieldset",{className:"margin-15-bottom ".concat(t),children:a?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(Wt,{legend:r,required:i,accessibleLegend:a,legendAdditionalClass:s}),n]}):(0,w.jsxs)("div",{className:"flexBlock flexCol-xs ".concat(o),children:[(0,w.jsx)(Wt,{legend:r,required:i,accessibleLegend:a,legendAdditionalClass:s}),n]})})},function(e){e.ERROR="icon-warning",e.NOTE="icon-info",e.VALIDATION="icon-Big_check_confirm",e.INFO="icon-BIG_WARNING"}(Gt||(Gt={})),Xt=function(e){var t=F(F({},$t),e),n=t.iconType,r=t.heading,a=t.message,s=t.messages,i=t.iconSizeCSS;return(0,w.jsx)(R.Components.Container,{children:(0,w.jsxs)(R.Components.Panel,{className:"flexBlock pad-20-left pad-20-right pad-25-top pad-25-bottom margin-15-bottom txtBlack",children:[(0,w.jsxs)("span",{className:"virgin-icon ".concat(n," ").concat(i," txtSize36"),"aria-hidden":!0,children:[(0,w.jsx)("span",{className:"virgin-icon path1"}),(0,w.jsx)("span",{className:"virgin-icon path2"})]}),(0,w.jsxs)("div",{id:"IstallationMessageBanner",className:"flexCol pad-15-left content-width valign-top pad-0-xs",children:[(0,w.jsx)("h4",{className:"virginUltraReg txtSize18 txtDarkGrey1 no-margin-top txtUppercase",children:(0,w.jsx)(R.FormattedHTMLMessage,{id:r})}),a?(0,w.jsx)("p",{className:"txtSize14 txtGray4A sans-serif no-margin",children:(0,w.jsx)(R.FormattedHTMLMessage,{id:a})}):null,s?(0,w.jsx)("ul",{children:s&&s.map(function(e){return(0,w.jsxs)("li",{className:"error",children:[(0,w.jsx)("a",{id:"message_".concat(e.id),href:"#".concat(e.id),className:"txtRed txtBold txtUnderline",title:e.id,children:(0,w.jsx)(Rt.FormattedMessage,{id:e.id})}),(0,w.jsxs)("span",{className:"txtDarkGrey",children:[" - ","required"===e.error?(0,w.jsx)(Rt.FormattedMessage,{id:"INLINE_ERROR_required"}):(0,w.jsx)(Rt.FormattedMessage,{id:"INLINE_ERROR_"+e.id+"_"+e.error})]})]})})}):null]})]})})},$t={iconType:Gt.INFO,iconSizeCSS:"txtSize36"},function(e){e.H1="h1",e.H2="h2",e.H3="h3",e.H4="h4",e.H5="h5",e.H6="h6"}(Jt||(Jt={})),Yt=function(e){var t=F(F({},Zt),e),n=t.tag,r=t.additionalClass,a=t.content,s=t.description,i=n||"h2";return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(i,{className:"virginUltra txtBlack txtCapital noMargin ".concat(r),children:(0,w.jsx)(Rt.FormattedMessage,{id:a})}),s?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("span",{className:"spacer10 col-xs-12 clear"}),(0,w.jsx)("p",{className:"noMargin",children:(0,w.jsx)(R.FormattedHTMLMessage,{id:s})})]}):null]})},Zt={additionalClass:"",description:""},Qt=function(e){function t(t){var n=e.call(this,t)||this;return n.headingProps={tag:Jt.H2,classNames:"txtSize28 txtSize24-xs",content:"INSTALLATION_PAGE_HEADING"},n}return u(t,e),t.prototype.render=function(){return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(R.Components.Container,{children:(0,w.jsxs)(R.Components.BRF3Container,{children:[(0,w.jsx)("span",{className:"spacer5 flex col-12"}),(0,w.jsx)(Yt,F({},this.headingProps)),(0,w.jsx)("span",{className:"spacer25 flex col-12 clear"})]})}),(0,w.jsx)(Xt,{iconType:Gt.INFO,heading:"INSTALLATION_HEADING",message:"INSTALLATION_MESSAGE"}),Object.keys(this.props.errors).length?(0,w.jsx)(Xt,{iconType:Gt.ERROR,heading:"ERRORS_HEADING",messages:wt(this.props.errors)}):null]})},t}(Oe.PureComponent),Kt=function(){var e,t,n,r=(0,L.useSelector)(function(e){return null==e?void 0:e.contactInformation}),a=(0,L.useSelector)(function(e){return null==e?void 0:e.additionalDetails}),s=m(Oe.useState(Dt.PHONE),2),i=s[0],o=s[1],l=Cn().setValue,c=function(e){var t=e.target,n=t.value,r=t.name;switch(n){case Dt.PHONE:case Dt.EMAIL:case Dt.TEXT_MESSAGE:o(n)}switch(r){case Dt.PHONE+"_LABEL":case Dt.TEXT_MESSAGE+"_LABEL":case"ADDITIONAL_PHONE_NUMBER":l(r,T(n),{shouldValidate:!0});break;case Dt.PHONE+"_EXT":case"ADDITIONAL_PHONE_EXT":l(r,j(n),{shouldValidate:!0});break;case"SUPERINTENDANT_PHONE":l(r,T(n),{shouldValidate:!0});break;case Dt.EMAIL+"_LABEL":l(r,n,{shouldValidate:!0})}};return Oe.useEffect(function(){o((null==r?void 0:r.preferredContactMethod)?r.preferredContactMethod:Dt.PHONE)},[r]),n={tag:Jt.H2,additionalClass:"txtSize22 txtSize24-xs",content:"CONTACT_INFORMATION"},(0,w.jsxs)("div",{className:"margin-30-bottom",id:"section2",children:[(0,w.jsx)(Yt,F({},n)),(0,w.jsx)("span",{className:"spacer10 visible-xs"}),(0,w.jsxs)("div",{className:"pad-25-top no-pad-xs",children:[(0,w.jsxs)(zt,{legend:"PREFERED_METHOD_OF_CONTACT",required:!0,additionalClass:"flexWrap",accessibleLegend:!1,children:[(0,w.jsxs)("div",{className:"flexCol lineHeight18",children:[(0,w.jsx)("div",{className:"spacer15 visible-xs"}),_(Dt,function(e){return(0,w.jsx)(Vt,{label:"PREFERED_METHOD_OF_CONTACT",value:e,handleChange:c,checked:e===i})})]}),_(Dt,function(e){var t;return(0,w.jsx)(Ht,{requiredInput:i===e,label:e+"_LABEL",containerClass:"sub-option flex-wrap ".concat(e===i?"show":"hide"),subLabel:e+"_FORMAT",extention:e===Dt.PHONE&&e+"_EXT",optionalExtenstion:!0,requiredPattern:e===Dt.EMAIL?Ft:Ct,value:Lt(e,r),subValue:null===(t=null==r?void 0:r.primaryPhone)||void 0===t?void 0:t.phoneExtension,handleChange:function(e){return c(e)}})})]}),(0,w.jsxs)(zt,{legend:"ADDITIONAL_PHONE_NUMBER",required:!1,accessibleLegend:!0,children:[(0,w.jsx)(Ht,{requiredInput:!1,label:"ADDITIONAL_PHONE_NUMBER",subLabel:"TELEPHONE_FORMAT",extention:"ADDITIONAL_PHONE_EXT",optionalExtenstion:!0,requiredPattern:Ct,value:null===(e=null==r?void 0:r.additionalPhone)||void 0===e?void 0:e.phoneNumber,subValue:null===(t=null==r?void 0:r.additionalPhone)||void 0===t?void 0:t.phoneExtension,handleChange:function(e){return c(e)}}),(0,w.jsx)(Ht,{label:"APPARTMENT",value:null==a?void 0:a.apartment,handleChange:function(e){return c(e)}}),(0,w.jsx)(Ht,{label:"ENTRY_CODE",value:null==a?void 0:a.entryCode,handleChange:function(e){return c(e)}}),(0,w.jsx)(Ht,{label:"SUPERINTENDANT_NAME",value:null==a?void 0:a.superintendantName,handleChange:function(e){return c(e)}}),(0,w.jsx)(Ht,{label:"SUPERINTENDANT_PHONE",requiredPattern:Ct,value:null==a?void 0:a.superintendantPhone,handleChange:function(e){return c(e)}}),(0,w.jsx)(kt,{label:"INFORMED_SUPERINTENDANT",value:"YES",checked:null==a?void 0:a.informedSuperintendant,handleChange:function(e){return c(e)}}),(0,w.jsx)(Bt,{label:"SPECIAL_INSTRUCTIONS",subLabel:"SPECIAL_INSTRUCTIONS_SUBLABEL",value:null==a?void 0:a.specialInstructions,maxLength:200,handleChange:function(e){return c(e)}})]})]})]})},en=Oe.memo(function(e){var t,n=e.handleChange,r=e.preferredDate,a=e.checked,s=Cn().register;return(0,w.jsx)(w.Fragment,{children:(0,w.jsxs)("label",{id:"dateAndTime"+r.date,className:"graphical_ctrl pointer ctrl_radioBtn margin-10-bottom",children:[(0,w.jsx)("input",F({type:"radio"},s("dateAndTime",{required:!0}),{id:"timeOption"+r.date,value:JSON.stringify(r),onChange:function(e){return n(e)},checked:a.date===r.date})),(0,w.jsx)("label",{className:"block no-margin",htmlFor:"timeOption"+r.date,children:Boolean(r.date)?(0,w.jsx)(Rt.FormattedDate,{value:r.date,year:"numeric",weekday:"long",month:"long",day:"2-digit",timeZone:"UTC"}):"No Appointment Details"}),Boolean(r.timeSlots.length)?(0,w.jsx)("span",{className:"txtNormal block",children:(0,w.jsx)(Rt.FormattedMessage,{id:null===(t=r.timeSlots.find(function(e){return e.isAvailable}))||void 0===t?void 0:t.intervalType})}):null,(0,w.jsx)("span",{className:"ctrl_element"})]})})}),tn=function(e){function t(t){return e.call(this,t)||this}return u(t,e),t.prototype.componentDidMount=function(){this.props.initSlickSlider()},t.prototype.render=function(){var e=this.props,t=e.availableDates,n=e.selectDate,r=e.selectedDateTime;return(0,w.jsx)("div",{className:"flexBlock margin-15-bottom sub-option relative timeslot-picker",children:(0,w.jsx)("div",{className:"select-timeslot fill",children:t&&t.map(function(e,t){return(0,w.jsx)("div",{className:"",children:(0,w.jsxs)("div",{className:e.timeSlots[0].intervalType===It.AllDay?"allDayContainer":"day-container",children:[(0,w.jsxs)("label",{htmlFor:"dayIndex_"+t,className:"virginUltra sans-serif-xs txtBold-xs txtSize16 txtBlack lineHeight1-3 margin-15-bottom",children:[(0,w.jsx)(Rt.FormattedDate,{value:S(e.date),weekday:"long",timeZone:"UTC"}),(0,w.jsx)("br",{className:"hidden-m"}),(0,w.jsx)("span",{className:"d-sm-none d-md-none d-lg-none d-xl-none",children:", "}),(0,w.jsx)(Rt.FormattedDate,{value:S(e.date),year:"numeric",month:"short",day:"2-digit",timeZone:"UTC"})]}),(0,w.jsx)("ul",{className:"noMargin list-unstyled timeItem","aria-labelledby":"mondayList",children:e.timeSlots.map(function(t){var a=r.timeSlots[0].intervalType===t.intervalType&&r.date===e.date;return(0,w.jsx)("li",{className:"txtBlue ".concat(a?"selected":""),children:(0,w.jsx)("button",{id:"slot_".concat(t.intervalType),onClick:function(r){return n(r,e.date,t)},className:"btn btn-link ".concat(t.intervalType===It.AllDay?"flexCol flexJustify":""," ").concat(t.isAvailable?"":"disabled"," ").concat(t.isSelected?"selected":""),tabIndex:0,children:(0,w.jsx)(R.FormattedHTMLMessage,{id:t.intervalType})})})})})]})})})})})},t.displayName="TimeSlots",t}(Oe.Component),nn=R.Components.Visible,rn=function(e){function t(t){var n=e.call(this,t)||this;return n.handleChange=function(e){var t=e.target.value;"OTHER"===t?n.setState({showTimeSlots:!0}):n.setState({showTimeSlots:!1,selectedDateTime:JSON.parse(t)})},n.selectDate=function(e,t,r){e.preventDefault();var a=h([],m(n.state.preferredDates),!1);n.state.preferredDates[0].date===t&&n.state.preferredDates[0].timeSlots[0].intervalType===r.intervalType?n.setState({preferredDates:n.state.preferredDates,selectedDateTime:n.state.preferredDates[0],showTimeSlots:!1,showOther:!1}):(a[1]={date:t,timeSlots:[F(F({},r),{isSelected:!0})]},n.setState({preferredDates:a,selectedDateTime:a[1],showTimeSlots:!1,showOther:!1}))},n.changeBtn=function(e){e.preventDefault(),n.setState({showOther:!0,showTimeSlots:!0,preferredDates:[n.state.preferredDates[0]]})},n.state={showTimeSlots:!1,selectedDateTime:null,preferredDates:[],showOther:!0},n.handleChange.bind(n),n.changeBtn.bind(n),n}return u(t,e),t.prototype.componentDidUpdate=function(e){var t,n,r;this.props.availableDates&&this.props.availableDates.length&&JSON.stringify(this.props.availableDates)!==JSON.stringify(e.availableDates)&&(t=(r=(n=this.props.availableDates).filter(function(e){return e.timeSlots.find(function(e){return!0===e.isSelected})})).length>0?r:[n[0]],this.setState({preferredDates:t,selectedDateTime:t[0].date?t[0]:null,showOther:!(t.length>1)}))},t.prototype.render=function(){var e=this,t=this.props,n=t.installationAddress,r=t.availableDates,a=t.initSlickSlider,s=this.state,i=s.showTimeSlots,o=s.selectedDateTime,l=s.showOther,c=s.preferredDates,d={tag:Jt.H2,additionalClass:"txtSize22 txtSize24-xs",content:"INSTALLATION_DETAILS",description:"INSTALLATION_DETAILS_DESC"};return(0,w.jsxs)("div",{className:"margin-30-bottom",id:"section1",children:[(0,w.jsx)(Yt,F({},d)),(0,w.jsx)("span",{className:"spacer10 flex col-12 clear"}),(0,w.jsx)("p",{className:"noMargin txtItalic",children:(0,w.jsx)(Rt.FormattedMessage,{id:"REQUIRED_INFO_FLAG"})}),(0,w.jsxs)("div",{className:"pad-15-top",children:[(0,w.jsxs)(zt,{legend:"DATE_AND_TIME_LABEL",required:!0,accessibleLegend:!1,additionalClass:"flex-wrap",children:[(0,w.jsx)("div",{className:"spacer10 visible-xs"}),(0,w.jsxs)("div",{className:"flexCol lineHeight18",children:[c&&c.length&&c.map(function(t){return(0,w.jsx)(en,{handleChange:e.handleChange,preferredDate:t,checked:i||o})}),(0,w.jsx)(nn,{when:l,placeholder:(0,w.jsx)("div",{className:"pad-35-left relative changeBtn",children:(0,w.jsx)("button",{id:"CHANGE_BTN",className:"btn btn-link pad-0 txtSize14 txtUnderline txtVirginBlue",onClick:function(t){return e.changeBtn(t)},children:"Change"})}),children:(0,w.jsx)(Vt,{handleChange:this.handleChange,requiredInput:!0,checked:i,label:"dateAndTime",value:"OTHER"})})]}),i?(0,w.jsx)(tn,{selectDate:this.selectDate,availableDates:r,initSlickSlider:a,selectedDateTime:o}):null]}),(0,w.jsx)(nn,{when:Boolean(o),children:o&&"OTHER"!==o?(0,w.jsx)(zt,{legend:"ESTIMATED_DURATION",required:!1,accessibleLegend:!1,children:(0,w.jsxs)("div",{className:"flexCol",children:[(0,w.jsx)("span",{className:"block",children:(0,w.jsx)(Rt.FormattedMessage,{id:o.timeSlots[0].duration})}),(0,w.jsx)("span",{className:"block",children:(0,w.jsx)(Rt.FormattedMessage,{id:"ARRIVAL_OF_TECHNICIAN"})})]})}):null}),(0,w.jsx)(nn,{when:Boolean(n),children:(0,w.jsx)(zt,{legend:"SHIPPING_INSTALLATION_ADDRESS",required:!1,accessibleLegend:!1,children:(0,w.jsxs)("div",{className:"flexCol",children:[(0,w.jsxs)("span",{className:"block",children:[(0,w.jsxs)(nn,{when:(0,R.ValueOf)(n,"apartmentNumber",!1),children:[(0,R.ValueOf)(n,"apartmentNumber","")," - "]}),(0,R.ValueOf)(n,"address1","")," ",(0,R.ValueOf)(n,"address2","")," ",(0,R.ValueOf)(n,"streetType",""),", ",(0,R.ValueOf)(n,"city",""),", ",(0,R.ValueOf)(n,"province",""),", ",(0,R.ValueOf)(n,"postalCode","")]}),(0,w.jsx)("span",{className:"margin-10-top",children:(0,w.jsx)(R.FormattedHTMLMessage,{id:"CONTACT_US_NOTE"})})]})})})]})]})},t}(Oe.Component),an=(0,L.connect)(function(e){return{installationAddress:e.installationAddress,availableDates:e.availableDates,duration:e.duration}},function(e){return{initSlickSlider:function(){return e(Y())}}})(rn),sn=null,(on=function(e){var t,n=Oe.useRef(null),r=Cn().handleSubmit,a=(0,L.useDispatch)();return sn=function(){n.current.click()},t=function(e){return n=function(){return function(t,n){function r(r){return function(c){return function(r){if(a)throw new TypeError("Generator is already executing.");for(;l&&(l=0,r[0]&&(o=0)),o;)try{if(a=1,s&&(i=2&r[0]?s.return:r[0]?s.throw||((i=s.return)&&i.call(s),0):s.next)&&!(i=i.call(s,r[1])).done)return i;switch(s=0,i&&(r=[2&r[0],i.value]),r[0]){case 0:case 1:i=r;break;case 4:return o.label++,{value:r[1],done:!1};case 5:o.label++,s=r[1],r=[0];continue;case 7:r=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==r[0]&&2!==r[0])){o=0;continue}if(3===r[0]&&(!i||r[1]>i[0]&&r[1]<i[3])){o.label=r[1];break}if(6===r[0]&&o.label<i[1]){o.label=i[1],i=r;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(r);break}i[2]&&o.ops.pop(),o.trys.pop();continue}r=n.call(t,o)}catch(e){r=[6,e],s=0}finally{a=i=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,c])}}var a,s,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},l=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return l.next=r(0),l.throw=r(1),l.return=r(2),"function"==typeof Symbol&&(l[Symbol.iterator]=function(){return this}),l}(this,function(t){return e.preventDefault(),r(function(e){a(H(e))})(e),[2]})},new((t=void 0)||(t=Promise))(function(r,a){function s(t){try{o(n.next(t))}catch(e){a(e)}}function i(t){try{o(n.throw(t))}catch(e){a(e)}}function o(e){var n;e.done?r(e.value):(n=e.value,n instanceof t?n:new t(function(e){e(n)})).then(s,i)}o((n=n.apply(undefined,[])).next())});var t,n},(0,w.jsxs)("form",{id:"AppointmentForm",onSubmit:t,children:[(0,w.jsx)("div",{className:"spacer45 hidden-m"}),(0,w.jsx)("div",{className:"spacer20 d-block d-sm-none"}),(0,w.jsx)(an,{})," ",(0,w.jsx)(Kt,{})," ",(0,w.jsx)("button",{ref:n,type:"submit","aria-hidden":"true",style:{display:"none"}})]})}).useSubmitRef=function(){return sn},ln=on,cn=k.CommonFeatures.BasePipe,dn=function(e){function t(n){var r=e.call(this,n)||this;return t.instance=r,r}return u(t,e),t.Subscriptions=function(e){var t;return(t={})[R.Actions.onContinue.toString()]=function(t){var n=ln.useSubmitRef();n&&n(),e.dispatch(R.Actions.setWidgetStatus(R.EWidgetStatus.RENDERED))},t},t}(cn),un=R.Components.RestrictionModal,fn=R.Actions.widgetRenderComplete,pn=function(e){var t=(0,L.useDispatch)(),n=Cn().formState.errors;return Oe.useEffect(function(){t(fn(R.EWidgetName.APPOINTMENT))},[]),(0,w.jsxs)("main",{id:"mainContent",children:[(0,w.jsx)("span",{className:"flex spacer30 col-12","aria-hidden":"true"}),(0,w.jsx)(un,{id:"APPOINTMENT_RESTRICTION_MODAL"}),(0,w.jsx)(Qt,{errors:n}),(0,w.jsx)(R.Components.Container,{children:(0,w.jsx)(R.Components.Panel,{className:"pad-25-left pad-25-right clearfix",children:(0,w.jsx)(ln,{})})})]})},mn=R.Components.ApplicationRoot,hn=function(e){var t=function(e={}){const t=Oe.useRef(void 0),n=Oe.useRef(void 0),[r,a]=Oe.useState({isDirty:!1,isValidating:!1,isLoading:nt(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,isReady:!1,defaultValues:nt(e.defaultValues)?void 0:e.defaultValues});if(!t.current)if(e.formControl)t.current={...e.formControl,formState:r},e.defaultValues&&!nt(e.defaultValues)&&e.formControl.reset(e.defaultValues,e.resetOptions);else{const{formControl:n,...a}=function(e={}){let t,n={...Bn,...e},r={submitCount:0,isDirty:!1,isReady:!1,isLoading:nt(n.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:n.errors||{},disabled:n.disabled||!1},a={},s=(Le(n.defaultValues)||Le(n.values))&&g(n.defaultValues||n.values)||{},i=n.shouldUnregister?{}:g(s),o={action:!1,mount:!1,watch:!1},l={mount:new Set,disabled:new Set,unMount:new Set,array:new Set,watch:new Set},c=0;const d={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1};let u={...d};const f={array:Ke(),state:Ke()},p=n.criteriaMode===Dn,m=async e=>{if(!n.disabled&&(d.isValid||u.isValid||e)){const e=n.resolver?et((await N()).errors):await S(a,!0);e!==r.isValid&&f.state.next({isValid:e})}},h=(e,t)=>{!n.disabled&&(d.isValidating||d.validatingFields||u.isValidating||u.validatingFields)&&((e||Array.from(l.mount)).forEach(e=>{e&&(t?Ge(r.validatingFields,e,t):x(r.validatingFields,e))}),f.state.next({validatingFields:r.validatingFields,isValidating:!et(r.validatingFields)}))},b=(e,t,n,r)=>{const l=We(a,e);if(l){const a=We(i,e,qe(n)?We(s,e):n);qe(a)||r&&r.defaultChecked||t?Ge(i,e,t?a:A(l._f)):j(e,a),o.mount&&m()}},v=(e,t,a,i,o)=>{let l=!1,c=!1;const p={name:e};if(!n.disabled){if(!a||i){(d.isDirty||u.isDirty)&&(c=r.isDirty,r.isDirty=p.isDirty=_(),l=c!==p.isDirty);const n=y(We(s,e),t);c=!!We(r.dirtyFields,e),n?x(r.dirtyFields,e):Ge(r.dirtyFields,e,!0),p.dirtyFields=r.dirtyFields,l=l||(d.dirtyFields||u.dirtyFields)&&c!==!n}if(a){const t=We(r.touchedFields,e);t||(Ge(r.touchedFields,e,a),p.touchedFields=r.touchedFields,l=l||(d.touchedFields||u.touchedFields)&&t!==a)}l&&o&&f.state.next(p)}return l?p:{}},N=async e=>{h(e,!0);const t=await n.resolver(i,n.context,pt(e||l.mount,a,n.criteriaMode,n.shouldUseNativeValidation));return h(e),t},S=async(e,t,a={valid:!0})=>{for(const s in e){const o=e[s];if(o){const{_f:e,...c}=o;if(e){const c=l.array.has(e.name),u=o._f&&yt(o._f);u&&d.validatingFields&&h([s],!0);const f=await jt(o,l.disabled,i,p,n.shouldUseNativeValidation&&!t,c);if(u&&d.validatingFields&&h([s]),f[e.name]&&(a.valid=!1,t))break;!t&&(We(f,e.name)?c?St(r.errors,f,e.name):Ge(r.errors,e.name,f[e.name]):x(r.errors,e.name))}!et(c)&&await S(c,t,a)}}return a.valid},_=(e,t)=>!n.disabled&&(e&&t&&Ge(i,e,t),!y(w(),s)),T=(e,t,n)=>Je(e,l,{...o.mount?i:qe(t)?s:$e(e)?{[e]:t}:t},n,t),j=(e,t,n={})=>{const r=We(a,e);let s=t;if(r){const n=r._f;n&&(!n.disabled&&Ge(i,e,ut(t,n)),s=rt(n.ref)&&we(t)?"":t,at(n.ref)?[...n.ref.options].forEach(e=>e.selected=s.includes(e.value)):n.refs?Fe(n.ref)?n.refs.forEach(e=>{e.defaultChecked&&e.disabled||(Array.isArray(s)?e.checked=!!s.find(t=>t===e.value):e.checked=s===e.value||!!s)}):n.refs.forEach(e=>e.checked=e.value===s):tt(n.ref)?n.ref.value="":(n.ref.value=s,n.ref.type||f.state.next({name:e,values:g(i)})))}(n.shouldDirty||n.shouldTouch)&&v(e,s,n.shouldTouch,n.shouldDirty,!0),n.shouldValidate&&C(e)},D=(e,t,n)=>{for(const r in t){if(!t.hasOwnProperty(r))return;const s=t[r],i=e+"."+r,o=We(a,i);(l.array.has(e)||Le(s)||o&&!o._f)&&!Ce(s)?D(i,s,n):j(i,s,n)}},I=(e,t,n={})=>{const c=We(a,e),p=l.array.has(e),m=g(t);Ge(i,e,m),p?(f.array.next({name:e,values:g(i)}),(d.isDirty||d.dirtyFields||u.isDirty||u.dirtyFields)&&n.shouldDirty&&f.state.next({name:e,dirtyFields:ct(s,i),isDirty:_(e,m)})):!c||c._f||we(m)?j(e,m,n):D(e,m,n),bt(e,l)&&f.state.next({...r,name:e}),f.state.next({name:o.mount?e:void 0,values:g(i)})},O=async e=>{o.mount=!0;const s=e.target;let b=s.name,_=!0;const T=We(a,b),j=e=>{_=Number.isNaN(e)||Ce(e)&&isNaN(e.getTime())||y(e,We(i,b,e))},D=gt(n.mode),I=gt(n.reValidateMode);if(T){let o,O;const F=s.type?A(T._f):Re(e),w=e.type===Sn||e.type===_n,L=!xt(T._f)&&!n.resolver&&!We(r.errors,b)&&!T._f.deps||Et(w,We(r.touchedFields,b),r.isSubmitted,I,D),R=bt(b,l,w);Ge(i,b,F),w?s&&s.readOnly||(T._f.onBlur&&T._f.onBlur(e),t&&t(0)):T._f.onChange&&T._f.onChange(e);const k=v(b,F,w),P=!et(k)||R;if(!w&&f.state.next({name:b,type:e.type,values:g(i)}),L)return(d.isValid||u.isValid)&&("onBlur"===n.mode?w&&m():w||m()),P&&f.state.next({name:b,...R?{}:k});if(!w&&R&&f.state.next({...r}),n.resolver){const{errors:e}=await N([b]);if(j(F),_){const t=E(r.errors,a,b),n=E(e,a,t.name||b);o=n.error,b=n.name,O=et(e)}}else h([b],!0),o=(await jt(T,l.disabled,i,p,n.shouldUseNativeValidation))[b],h([b]),j(F),_&&(o?O=!1:(d.isValid||u.isValid)&&(O=await S(a,!0)));_&&(T._f.deps&&C(T._f.deps),((e,a,s,i)=>{const o=We(r.errors,e),l=(d.isValid||u.isValid)&&ze(a)&&r.isValid!==a;var p;if(n.delayError&&s?(p=()=>((e,t)=>{Ge(r.errors,e,t),f.state.next({errors:r.errors})})(e,s),t=e=>{clearTimeout(c),c=setTimeout(p,e)},t(n.delayError)):(clearTimeout(c),t=null,s?Ge(r.errors,e,s):x(r.errors,e)),(s?!y(o,s):o)||!et(i)||l){const t={...i,...l&&ze(a)?{isValid:a}:{},errors:r.errors,name:e};r={...r,...t},f.state.next(t)}})(b,O,o,k))}},F=(e,t)=>{if(We(r.errors,t)&&e.focus)return e.focus(),1},C=async(e,t={})=>{let s,i;const o=Qe(e);if(n.resolver){const t=await(async e=>{const{errors:t}=await N(e);if(e)for(const n of e){const e=We(t,n);e?Ge(r.errors,n,e):x(r.errors,n)}else r.errors=t;return t})(qe(e)?e:o);s=et(t),i=e?!o.some(e=>We(t,e)):s}else e?(i=(await Promise.all(o.map(async e=>{const t=We(a,e);return await S(t&&t._f?{[e]:t}:t)}))).every(Boolean),(i||r.isValid)&&m()):i=s=await S(a);return f.state.next({...!$e(e)||(d.isValid||u.isValid)&&s!==r.isValid?{}:{name:e},...n.resolver||!e?{isValid:s}:{},errors:r.errors}),t.shouldFocus&&!i&&Vn(a,F,e?o:l.mount),i},w=e=>{const t={...o.mount?i:s};return qe(e)?t:$e(e)?We(t,e):e.map(e=>We(t,e))},L=(e,t)=>({invalid:!!We((t||r).errors,e),isDirty:!!We((t||r).dirtyFields,e),error:We((t||r).errors,e),isValidating:!!We(r.validatingFields,e),isTouched:!!We((t||r).touchedFields,e)}),R=(e,t,n)=>{const s=(We(a,e,{_f:{}})._f||{}).ref,i=We(r.errors,e)||{},{ref:o,message:l,type:c,...d}=i;Ge(r.errors,e,{...d,...t,ref:s}),f.state.next({name:e,errors:r.errors,isValid:!1}),n&&n.shouldFocus&&s&&s.focus&&s.focus()},k=e=>f.state.subscribe({next:t=>{At(e.name,t.name,e.exact)&&vt(t,e.formState||d,W,e.reRenderRoot)&&e.callback({values:{...i},...r,...t,defaultValues:s})}}).unsubscribe,P=(e,t={})=>{for(const o of e?Qe(e):l.mount)l.mount.delete(o),l.array.delete(o),t.keepValue||(x(a,o),x(i,o)),!t.keepError&&x(r.errors,o),!t.keepDirty&&x(r.dirtyFields,o),!t.keepTouched&&x(r.touchedFields,o),!t.keepIsValidating&&x(r.validatingFields,o),!n.shouldUnregister&&!t.keepDefaultValue&&x(s,o);f.state.next({values:g(i)}),f.state.next({...r,...t.keepDirty?{isDirty:_()}:{}}),!t.keepIsValid&&m()},M=({disabled:e,name:t})=>{(ze(e)&&o.mount||e||l.disabled.has(t))&&(e?l.disabled.add(t):l.disabled.delete(t))},V=(e,t={})=>{let r=We(a,e);const i=ze(t.disabled)||ze(n.disabled);return Ge(a,e,{...r||{},_f:{...r&&r._f?r._f:{ref:{name:e}},name:e,mount:!0,...t}}),l.mount.add(e),r?M({disabled:ze(t.disabled)?t.disabled:n.disabled,name:e}):b(e,!0,t.value),{...i?{disabled:t.disabled||n.disabled}:{},...n.progressive?{required:!!t.required,min:ht(t.min),max:ht(t.max),minLength:ht(t.minLength),maxLength:ht(t.maxLength),pattern:ht(t.pattern)}:{},name:e,onChange:O,onBlur:O,ref:i=>{if(i){V(e,t),r=We(a,e);const n=qe(i.value)&&i.querySelectorAll&&i.querySelectorAll("input,select,textarea")[0]||i,o=it(n),l=r._f.refs||[];if(o?l.find(e=>e===n):n===r._f.ref)return;Ge(a,e,{_f:{...r._f,...o?{refs:[...l.filter(ot),n,...Array.isArray(We(s,e))?[{}]:[]],ref:{type:n.type,name:e}}:{ref:n}}}),b(e,!1,void 0,n)}else r=We(a,e,{}),r._f&&(r._f.mount=!1),(n.shouldUnregister||t.shouldUnregister)&&(!Pe(l.array,e)||!o.action)&&l.unMount.add(e)}}},B=()=>n.shouldFocusError&&Vn(a,F,l.mount),q=(e,t)=>async s=>{let o;s&&(s.preventDefault&&s.preventDefault(),s.persist&&s.persist());let c=g(i);if(f.state.next({isSubmitting:!0}),n.resolver){const{errors:e,values:t}=await N();r.errors=e,c=g(t)}else await S(a);if(l.disabled.size)for(const e of l.disabled)x(c,e);if(x(r.errors,"root"),et(r.errors)){f.state.next({errors:{}});try{await e(c,s)}catch(d){o=d}}else t&&await t({...r.errors},s),B(),setTimeout(B);if(f.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:et(r.errors)&&!o,submitCount:r.submitCount+1,errors:r.errors}),o)throw o},H=(e,t={})=>{const c=e?g(e):s,u=g(c),p=et(e),m=p?s:u;if(t.keepDefaultValues||(s=c),!t.keepValues){if(t.keepDirtyValues){const e=new Set([...l.mount,...Object.keys(ct(s,i))]);for(const t of Array.from(e))We(r.dirtyFields,t)?Ge(m,t,We(i,t)):I(t,We(m,t))}else{if(Ve&&qe(e))for(const e of l.mount){const t=We(a,e);if(t&&t._f){const e=Array.isArray(t._f.refs)?t._f.refs[0]:t._f.ref;if(rt(e)){const t=e.closest("form");if(t){t.reset();break}}}}if(t.keepFieldsRef)for(const e of l.mount)I(e,We(m,e));else a={}}i=n.shouldUnregister?t.keepDefaultValues?g(s):{}:g(m),f.array.next({values:{...m}}),f.state.next({values:{...m}})}l={mount:t.keepDirtyValues?l.mount:new Set,unMount:new Set,array:new Set,disabled:new Set,watch:new Set,watchAll:!1,focus:""},o.mount=!d.isValid||!!t.keepIsValid||!!t.keepDirtyValues,o.watch=!!n.shouldUnregister,f.state.next({submitCount:t.keepSubmitCount?r.submitCount:0,isDirty:!p&&(t.keepDirty?r.isDirty:!(!t.keepDefaultValues||y(e,s))),isSubmitted:!!t.keepIsSubmitted&&r.isSubmitted,dirtyFields:p?{}:t.keepDirtyValues?t.keepDefaultValues&&i?ct(s,i):r.dirtyFields:t.keepDefaultValues&&e?ct(s,e):t.keepDirty?r.dirtyFields:{},touchedFields:t.keepTouched?r.touchedFields:{},errors:t.keepErrors?r.errors:{},isSubmitSuccessful:!!t.keepIsSubmitSuccessful&&r.isSubmitSuccessful,isSubmitting:!1,defaultValues:s})},U=(e,t)=>H(nt(e)?e(i):e,t),W=e=>{r={...r,...e}},z={control:{register:V,unregister:P,getFieldState:L,handleSubmit:q,setError:R,_subscribe:k,_runSchema:N,_focusError:B,_getWatch:T,_getDirty:_,_setValid:m,_setFieldArray:(e,t=[],l,c,p=!0,m=!0)=>{if(c&&l&&!n.disabled){if(o.action=!0,m&&Array.isArray(We(a,e))){const t=l(We(a,e),c.argA,c.argB);p&&Ge(a,e,t)}if(m&&Array.isArray(We(r.errors,e))){const t=l(We(r.errors,e),c.argA,c.argB);p&&Ge(r.errors,e,t),Nt(r.errors,e)}if((d.touchedFields||u.touchedFields)&&m&&Array.isArray(We(r.touchedFields,e))){const t=l(We(r.touchedFields,e),c.argA,c.argB);p&&Ge(r.touchedFields,e,t)}(d.dirtyFields||u.dirtyFields)&&(r.dirtyFields=ct(s,i)),f.state.next({name:e,isDirty:_(e,t),dirtyFields:r.dirtyFields,errors:r.errors,isValid:r.isValid})}else Ge(i,e,t)},_setDisabledField:M,_setErrors:e=>{r.errors=e,f.state.next({errors:r.errors,isValid:!1})},_getFieldArray:e=>He(We(o.mount?i:s,e,n.shouldUnregister?We(s,e,[]):[])),_reset:H,_resetDefaultValues:()=>nt(n.defaultValues)&&n.defaultValues().then(e=>{U(e,n.resetOptions),f.state.next({isLoading:!1})}),_removeUnmounted:()=>{for(const e of l.unMount){const t=We(a,e);t&&(t._f.refs?t._f.refs.every(e=>!ot(e)):!ot(t._f.ref))&&P(e)}l.unMount=new Set},_disableForm:e=>{ze(e)&&(f.state.next({disabled:e}),Vn(a,(t,n)=>{const r=We(a,n);r&&(t.disabled=r._f.disabled||e,Array.isArray(r._f.refs)&&r._f.refs.forEach(t=>{t.disabled=r._f.disabled||e}))},0,!1))},_subjects:f,_proxyFormState:d,get _fields(){return a},get _formValues(){return i},get _state(){return o},set _state(e){o=e},get _defaultValues(){return s},get _names(){return l},set _names(e){l=e},get _formState(){return r},get _options(){return n},set _options(e){n={...n,...e}}},subscribe:e=>(o.mount=!0,u={...u,...e.formState},k({...e,formState:u})),trigger:C,register:V,handleSubmit:q,watch:(e,t)=>nt(e)?f.state.subscribe({next:n=>"values"in n&&e(T(void 0,t),n)}):T(e,t,!0),setValue:I,getValues:w,reset:U,resetField:(e,t={})=>{We(a,e)&&(qe(t.defaultValue)?I(e,g(We(s,e))):(I(e,t.defaultValue),Ge(s,e,g(t.defaultValue))),t.keepTouched||x(r.touchedFields,e),t.keepDirty||(x(r.dirtyFields,e),r.isDirty=t.defaultValue?_(e,g(We(s,e))):_()),t.keepError||(x(r.errors,e),d.isValid&&m()),f.state.next({...r}))},clearErrors:e=>{e&&Qe(e).forEach(e=>x(r.errors,e)),f.state.next({errors:e?r.errors:{}})},unregister:P,setError:R,setFocus:(e,t={})=>{const n=We(a,e),r=n&&n._f;if(r){const e=r.refs?r.refs[0]:r.ref;e.focus&&(e.focus(),t.shouldSelect&&nt(e.select)&&e.select())}},getFieldState:L};return{...z,formControl:z}}(e);t.current={...a,formState:r}}const s=t.current.control;return s._options=e,Ln(()=>{const e=s._subscribe({formState:s._proxyFormState,callback:()=>a({...s._formState}),reRenderRoot:!0});return a(e=>({...e,isReady:!0})),s._formState.isReady=!0,e},[s]),Oe.useEffect(()=>s._disableForm(e.disabled),[s,e.disabled]),Oe.useEffect(()=>{e.mode&&(s._options.mode=e.mode),e.reValidateMode&&(s._options.reValidateMode=e.reValidateMode)},[s,e.mode,e.reValidateMode]),Oe.useEffect(()=>{e.errors&&(s._setErrors(e.errors),s._focusError())},[s,e.errors]),Oe.useEffect(()=>{e.shouldUnregister&&s._subjects.state.next({values:s._getWatch()})},[s,e.shouldUnregister]),Oe.useEffect(()=>{if(s._proxyFormState.isDirty){const e=s._getDirty();e!==r.isDirty&&s._subjects.state.next({isDirty:e})}},[s,r.isDirty]),Oe.useEffect(()=>{e.values&&!y(e.values,n.current)?(s._reset(e.values,{keepFieldsRef:!0,...s._options.resetOptions}),n.current=e.values,a(e=>({...e}))):s._resetDefaultValues()},[s,e.values]),Oe.useEffect(()=>{s._state.mount||(s._setValid(),s._state.mount=!0),s._state.watch&&(s._state.watch=!1,s._subjects.state.next({...s._formState})),s._removeUnmounted()}),t.current.formState=Xe(r,s),t.current}();return(0,w.jsx)(mn,{children:(0,w.jsxs)(wn,F({},t,{children:[" ",(0,w.jsx)(pn,{})]}))})},gn=R.Actions.setWidgetProps,yn=R.Actions.setWidgetStatus,xn=L.Provider,bn=function(e){function t(t,n,r,a){var s=e.call(this)||this;return s.store=t,s.params=n,s.config=r,s.pipe=a,s}return u(t,e),t.prototype.init=function(){this.pipe.subscribe(dn.Subscriptions(this.store)),this.store.dispatch(gn(this.config)),this.store.dispatch(gn(this.params.props)),this.store.dispatch(yn(R.EWidgetStatus.INIT))},t.prototype.destroy=function(){this.pipe.unsubscribe(),this.store.destroy()},t.prototype.render=function(e){var t=this.store;e.render((0,w.jsx)(R.ContextProvider,{value:{config:this.config},children:(0,w.jsx)(xn,{store:t,children:(0,w.jsx)(hn,{})})}))},f([(0,k.Widget)({namespace:"Ordering"}),p("design:paramtypes",[Ie,k.ParamsProvider,ee,dn])],t)}(k.ViewWidget),vn=bn,D}()});
//# sourceMappingURL=widget.js.map