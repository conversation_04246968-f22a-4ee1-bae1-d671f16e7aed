import * as CommonBaseClient from "./BaseClient";
import * as CommonBaseConfig from "./BaseConfig";
import * as CommonBaseLocalization from "./BaseLocalization";
import * as CommonBaseStore from "./BaseStore";
import * as CommonBasePipe from "./BasePipe";
export declare const CommonFeatures: {
    BaseClient: typeof CommonBaseClient.BaseClient;
    BaseConfig: typeof CommonBaseConfig.BaseConfig;
    configProperty: any;
    BaseLocalization: typeof CommonBaseLocalization.BaseLocalization;
    BaseStore: typeof CommonBaseStore.BaseStore;
    BasePipe: typeof CommonBasePipe.BasePipe;
    actionsToComputedPropertyName: <T extends object>(actions: T) => { [key in keyof T]: string; };
};
