import { AjaxOptions, CustomAjaxOptions } from "../../../@types";
export declare class AjaxConfig {
    private options;
    private customOptions;
    private defaultOptions;
    private defaultCustomOptions;
    constructor(opts: {
        [key: string]: string;
    });
    private processOptions;
    mergeOptions(...opts: any[]): AjaxOptions;
    buildRequestConfig(options: AjaxOptions, path: string): {
        [k: string]: any;
    };
    buildRequestPath(path: string, options: CustomAjaxOptions): string;
    buildQueryParameters(url: string, path: string, params: any): string;
    getOptions(): AjaxOptions;
    setOptions(opts: {}): void;
}
