import { Components } from "omf-changepackage-components";
import { FormProvider, useForm } from "react-hook-form";
import { Application } from "./views";

const {
  ApplicationRoot
} = Components;

export const App = (props: any) => {
  const methods = useForm();
  return (
    <ApplicationRoot>
      <FormProvider {...methods}> { /** Create context for react-hook-form */}
        <Application />
      </FormProvider>
    </ApplicationRoot>
  );
};
