import { ConfigServices } from "../../../../@types";
export declare class ModuleIdResolver {
    private config;
    private moduleId;
    private get widgetBundle();
    private get versionDelimiter();
    constructor(config: ConfigServices, moduleId: string);
    resolve(): string;
    private resolveModulePath;
    private resolveBaseModulePath;
    private hasTargetVersion;
    private version;
    private module;
    private get moduleIdParts();
}
