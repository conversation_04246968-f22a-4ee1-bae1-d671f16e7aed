import { EventStreamServices, IEventStreamParams, IEventStreamCallback, IEventStreamErrorsShortcut, IEventStreamGlobalCallback } from "../../../@types";
export declare class DefaultEventStreamServices implements EventStreamServices {
    private subject;
    private errorStream;
    init(): void;
    private executeCallback;
    subscribeAll: (cb: IEventStreamCallback) => () => void;
    subscribe(param1: string | IEventStreamParams | IEventStreamGlobalCallback, param2?: IEventStreamCallback): () => void;
    subscribeError(callback: IEventStreamCallback): () => void;
    send(type: string, payload?: any): void;
    sendError(errorType: string, errorPayload?: any, widgetName?: string): void;
    sendError(error: Error, widgetName?: string): void;
    get errors(): IEventStreamErrorsShortcut;
    unsubscribe(): void;
    destroy(): void;
}
