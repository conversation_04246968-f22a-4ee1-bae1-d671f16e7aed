import { Id } from "../Id";
import { Store } from "../StoreServices";
import { Store as ReduxStore, Reducer, AnyAction, Observable, Action, Middleware } from "redux";
import { Epic } from "redux-observable";
export declare abstract class BaseStore<State> implements Store, ReduxStore<State> {
    private store;
    private epicMiddleware;
    constructor(store: Store);
    [Symbol.observable](): Observable<State>;
    init(): void;
    destroy(): void;
    createStore(): ReduxStore<State & {}, AnyAction> & {
        dispatch: unknown;
    };
    get id(): Id;
    get listenToAll(): boolean;
    set listenToAll(value: boolean);
    get middlewares(): Epic<Action<any>, Action<any>, State, any>;
    protected get additionalMiddlewares(): Middleware[];
    abstract get reducer(): Reducer<State>;
    get getState(): () => State;
    get dispatch(): any;
    get subscribe(): any;
    notifyActionListener(action: AnyAction): void;
    replaceReducer(): void;
}
declare global {
    interface Window {
        __REDUX_DEVTOOLS_EXTENSION_COMPOSE__?: any;
    }
}
