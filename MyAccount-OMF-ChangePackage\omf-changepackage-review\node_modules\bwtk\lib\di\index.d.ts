declare module "di" {
  export class Injector {
    constructor(modules?: any[], parentInjector?: Injector, providers?: any, scopes?: any[]);
    get(token: any): any;
    createChild(modules: any[]): any;
  }

  class Inject {
    constructor(...tokens: any[]);
    tokens: any[];
  }
  class Provide {
    constructor(token: any);
    hasPriority: boolean;
  }
  class FactoryProvider { }

  export function annotate(constructor: Function, annotation: any): any;
}
