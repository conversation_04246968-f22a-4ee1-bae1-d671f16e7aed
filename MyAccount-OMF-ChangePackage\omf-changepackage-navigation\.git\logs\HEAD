0000000000000000000000000000000000000000 440e186bdbc7351b9fbe4daa47419e73617f4eb0 <PERSON><PERSON>yed <<EMAIL>> 1755878347 -0400	clone: from https://gitlab.int.bell.ca/uxp/omf-changepackage-navigation.git
440e186bdbc7351b9fbe4daa47419e73617f4eb0 527aa14c5d2fca889ae4ee4ed06779c0ec3e6787 <PERSON><PERSON>yed <<EMAIL>> 1755878657 -0400	checkout: moving from master to bwtk-upgrade2
527aa14c5d2fca889ae4ee4ed06779c0ec3e6787 527aa14c5d2fca889ae4ee4ed06779c0ec3e6787 <PERSON><PERSON> ALmobayyed <<EMAIL>> 1755878669 -0400	checkout: moving from bwtk-upgrade2 to bwtk-upgrade2
527aa14c5d2fca889ae4ee4ed06779c0ec3e6787 527aa14c5d2fca889ae4ee4ed06779c0ec3e6787 Ramzi ALmobayyed <<EMAIL>> 1755878680 -0400	checkout: moving from bwtk-upgrade2 to bwtk-upgrade2
527aa14c5d2fca889ae4ee4ed06779c0ec3e6787 527aa14c5d2fca889ae4ee4ed06779c0ec3e6787 Ramzi ALmobayyed <<EMAIL>> 1755878692 -0400	checkout: moving from bwtk-upgrade2 to bwtk-upgrade2
527aa14c5d2fca889ae4ee4ed06779c0ec3e6787 5b4d362efc16f7e923af05527b0dab2ee3c72c8c Ramzi ALmobayyed <<EMAIL>> 1755881961 -0400	checkout: moving from bwtk-upgrade2 to feature-fmsb
5b4d362efc16f7e923af05527b0dab2ee3c72c8c 20618308cdd3a0e78222171abf6f58d490fded74 Ramzi ALmobayyed <<EMAIL>> 1755882006 -0400	commit (merge): Merge branch 'bwtk-upgrade2' of https://gitlab.int.bell.ca/uxp/omf-changepackage-navigation into feature-fmsb
20618308cdd3a0e78222171abf6f58d490fded74 e2e7fb36a6483d924cef6aa907f601add4d575c0 Ramzi ALmobayyed <<EMAIL>> 1755882337 -0400	pull origin Release: Merge made by the 'ort' strategy.
e2e7fb36a6483d924cef6aa907f601add4d575c0 3cc01caa7486c0642b0e0f9d271863603a7431f3 Ramzi ALmobayyed <<EMAIL>> 1756127254 -0400	commit: BWTK6.1 updates
3cc01caa7486c0642b0e0f9d271863603a7431f3 da0e887f3d9b553fbbc10730f3289e1c17459011 Ramzi ALmobayyed <<EMAIL>> 1756134614 -0400	pull: Fast-forward
