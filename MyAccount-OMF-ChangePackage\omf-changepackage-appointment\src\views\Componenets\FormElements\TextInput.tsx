import { useFormContext } from "react-hook-form";
import { FormattedMessage } from "react-intl";
import { Localization } from "../../../Localization";
import { FormattedHTMLMessage } from "omf-changepackage-components";

interface ComponentProps {
  label: string;
  subLabel?: string;
  handleChange: Function;
  extention?: string;
  optionalExtenstion?: boolean;
  requiredInput?: boolean;
  requiredPattern?: string;
  containerClass?: string;
  value?: string;
  subValue?: string;
}

export const TextInput: any = (props: ComponentProps) => {
  const privateProps = { ...defaultProps, ...props };
  const { label, subLabel, handleChange, containerClass, extention, optionalExtenstion, requiredInput, requiredPattern, value, subValue } = privateProps;
  const { register, errors }: any = useFormContext();

  const getAriaLabel = (label: string) => {
    switch (label) {
      case "TELEPHONE_FORMAT":
      case "PREFERED_PHONE_FORMAT":
      case "PREFERED_TEXT_MESSAGE_FORMAT":
      case "Phone_FORMAT":
      case "TextMessage_FORMAT":
        return Localization.getLocalizedString("TELEPHONE_FORMAT_ARIA");
      default:
        return Localization.getLocalizedString(label);
    }
  };

  return (
    <div className={`flexBlock flexCol-xs margin-15-bottom flexWrap ${containerClass}`}>
      <label htmlFor="additionalPhoneNumber" className={`installation-form-label ${requiredInput ? "form-required" : ""} ${errors && errors[label] ? "error" : ""}`}>
        <span className="txtBold block"><FormattedMessage id={label} /></span>
        {subLabel ? <span className="txtItalic block txtNormal" aria-label={getAriaLabel(subLabel)}><FormattedHTMLMessage id={subLabel} /></span> : null}
      </label>
      <div className={`flexCol relative ${errors && errors[label] ? "has-error" : ""}`}>
        <span className="topArrow text-left hide" aria-hidden="true"></span>
        <input
          type="text"
          ref={register({ required: requiredInput, pattern: requiredPattern })}
          className="brf3-virgin-form-input form-control"
          id={label}
          name={label}
          title={label}
          defaultValue={value}
          onBlur={handleChange as any}
          onChange={(e) => handleChange(e)}
        />
        {errors && errors[label] ? <span className="error margin-5-top">
          <span className="virgin-icon icon-warning margin-15-right" aria-hidden={true}>
            <span className="volt-icon path1"></span><span className="volt-icon path2"></span>
          </span>
          <span className="txtSize12"><FormattedHTMLMessage id={errors[label].type !== "pattern" ? `INLINE_ERROR_required` : `INLINE_ERROR_${label}_pattern`} /></span>
        </span> : null}
      </div>
      {
        extention ? <div className="flexCol brf3-virgin-form-subInput fill-sm">
          <div className="flexBlock flexCol-xs">
            <label htmlFor="extension" className="installation-form-label">
              <span className="txtBold block"><FormattedMessage id={extention} /></span>
              {optionalExtenstion ? <span className="txtItalic block txtNormal"><FormattedMessage id="OPTIONAL_LABEL" /></span> : null}
            </label>
            <div className="flexCol">
              <input
                type="text"
                ref={register}
                className="brf3-virgin-form-input form-control"
                id={extention}
                name={extention}
                title={extention}
                maxLength={10}
                defaultValue={subValue}
                onBlur={handleChange as any}
                onChange={(e) => handleChange(e)}
              />
            </div>
          </div>
        </div> : null
      }
    </div>
  );
};

const defaultProps = {
  requiredInput: false,
  requiredPattern: /.*/i,
  containerClass: "",
  value: "",
  subValue: ""
};
