/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("react"), require("react-redux"), require("omf-changepackage-components"), require("bwtk"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"), require("react-intl"));
	else if(typeof define === 'function' && define.amd)
		define(["react", "react-redux", "omf-changepackage-components", "bwtk", "redux", "redux-actions", "redux-observable", "rxjs", "react-intl"], factory);
	else {
		var a = typeof exports === 'object' ? factory(require("react"), require("react-redux"), require("omf-changepackage-components"), require("bwtk"), require("redux"), require("redux-actions"), require("redux-observable"), require("rxjs"), require("react-intl")) : factory(root["React"], root["ReactRedux"], root["OMFChangepackageComponents"], root["bwtk"], root["Redux"], root["ReduxActions"], root["ReduxObservable"], root["rxjs"], root["ReactIntl"]);
		for(var i in a) (typeof exports === 'object' ? exports : root)[i] = a[i];
	}
})(self, function(__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_redux__, __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__, __WEBPACK_EXTERNAL_MODULE_bwtk__, __WEBPACK_EXTERNAL_MODULE_redux__, __WEBPACK_EXTERNAL_MODULE_redux_actions__, __WEBPACK_EXTERNAL_MODULE_redux_observable__, __WEBPACK_EXTERNAL_MODULE_rxjs__, __WEBPACK_EXTERNAL_MODULE_react_intl__) {
return /******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "../src/Widget.tsx":
/*!**************************************!*\
  !*** ../src/Widget.tsx + 20 modules ***!
  \**************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval("{// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ Widget; }\n});\n\n// NAMESPACE OBJECT: ../src/store/Actions.ts\nvar Actions_namespaceObject = {};\n__webpack_require__.r(Actions_namespaceObject);\n__webpack_require__.d(Actions_namespaceObject, {\n  getAccountDetails: function() { return getAccountDetails; },\n  getInternetCatalog: function() { return getInternetCatalog; },\n  setAccountDetails: function() { return setAccountDetails; },\n  setInternetCatalog: function() { return setInternetCatalog; },\n  togglePackageSelection: function() { return togglePackageSelection; },\n  updateInternetCatalog: function() { return updateInternetCatalog; }\n});\n\n;// ./tslib/tslib.es6.mjs\n/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nfunction __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nvar __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nfunction __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nfunction __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nfunction __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nfunction __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nfunction __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nfunction __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nfunction __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nfunction __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nfunction __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nfunction __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nvar __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nfunction __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nfunction __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nfunction __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nfunction __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nfunction __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nfunction __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nfunction __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nfunction __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nfunction __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nfunction __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nfunction __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nfunction __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nfunction __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nfunction __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nfunction __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nfunction __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nfunction __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nfunction __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\n/* harmony default export */ var tslib_es6 = ({\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n});\n\n// EXTERNAL MODULE: ./react/jsx-runtime.js\nvar jsx_runtime = __webpack_require__(\"./react/jsx-runtime.js\");\n// EXTERNAL MODULE: external {\"root\":\"ReactRedux\",\"commonjs2\":\"react-redux\",\"commonjs\":\"react-redux\",\"amd\":\"react-redux\"}\nvar external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_ = __webpack_require__(\"react-redux\");\n// EXTERNAL MODULE: external {\"root\":\"OMFChangepackageComponents\",\"commonjs2\":\"omf-changepackage-components\",\"commonjs\":\"omf-changepackage-components\",\"amd\":\"omf-changepackage-components\"}\nvar external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_ = __webpack_require__(\"omf-changepackage-components\");\n// EXTERNAL MODULE: external {\"root\":\"bwtk\",\"commonjs2\":\"bwtk\",\"commonjs\":\"bwtk\",\"amd\":\"bwtk\"}\nvar external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_ = __webpack_require__(\"bwtk\");\n// EXTERNAL MODULE: external {\"root\":\"Redux\",\"commonjs2\":\"redux\",\"commonjs\":\"redux\",\"amd\":\"redux\"}\nvar external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_ = __webpack_require__(\"redux\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxActions\",\"commonjs2\":\"redux-actions\",\"commonjs\":\"redux-actions\",\"amd\":\"redux-actions\"}\nvar external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_ = __webpack_require__(\"redux-actions\");\n// EXTERNAL MODULE: external {\"root\":\"ReduxObservable\",\"commonjs2\":\"redux-observable\",\"commonjs\":\"redux-observable\",\"amd\":\"redux-observable\"}\nvar external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_ = __webpack_require__(\"redux-observable\");\n;// ../src/mutators/index.ts\n\n\nfunction serviceAccountMutatorFn(response) {\n    return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(response, \"ProductOfferings\", [{ Unavailable: true }]);\n}\nfunction catalogMutatorFn(response) {\n    var productOfferingGroup = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(response, \"productOfferingDetail.productOfferingGroups\", [])\n        .find(function (group) { return group.lineOfBusiness === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.ELineOfBusiness.Internet &&\n        group.productOfferingGroupType === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EProductOfferingGroupType.Default; });\n    return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(productOfferingGroup, \"productOfferings\", []);\n}\nfunction orderMutatorFn(response, catalog) {\n    var productOfferingGroup = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(response, \"productOfferingDetail.productOfferingGroups\", [])\n        .find(function (group) { return group.lineOfBusiness === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.ELineOfBusiness.Internet &&\n        group.productOfferingGroupType === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Volt.EProductOfferingGroupType.Delta; });\n    var productOfferings = (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(productOfferingGroup, \"productOfferings\", []);\n    productOfferings.forEach(function (product) {\n        var initial = catalog.find(function (pkg) { return pkg.id === product.id; }) || {};\n        Object.assign(initial, product);\n    });\n    return __spreadArray([], __read(catalog), false);\n}\n\n;// ../src/store/Actions.ts\n\n\nvar getAccountDetails = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"GET_ACCOUNT_DETAILS\");\nvar setAccountDetails = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_ACCOUNT_DETAILS\", serviceAccountMutatorFn);\nvar getInternetCatalog = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"GET_INTERNET_CATALOG\");\nvar setInternetCatalog = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"SET_INTERNET_CATALOG\", catalogMutatorFn);\nvar togglePackageSelection = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"TOGGLE_INTERNET_PACKAGE\");\nvar updateInternetCatalog = (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.createAction)(\"UPDATE_INTERNET_CATALOG\", orderMutatorFn);\n\n// EXTERNAL MODULE: external {\"root\":\"rxjs\",\"commonjs2\":\"rxjs\",\"commonjs\":\"rxjs\",\"amd\":\"rxjs\"}\nvar external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_ = __webpack_require__(\"rxjs\");\n;// ../src/Config.ts\n\n\nvar BaseConfig = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseConfig, configProperty = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.configProperty;\nvar Config = (function (_super) {\n    __extends(Config, _super);\n    function Config() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    __decorate([\n        configProperty(\"\"),\n        __metadata(\"design:type\", String)\n    ], Config.prototype, \"flowType\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"environmentVariables\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"mockdata\", void 0);\n    __decorate([\n        configProperty({}),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"headers\", void 0);\n    __decorate([\n        configProperty({ base: \"http://127.0.0.1:8881\" }),\n        __metadata(\"design:type\", Object)\n    ], Config.prototype, \"api\", void 0);\n    Config = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Config);\n    return Config;\n}(BaseConfig));\n\n\n;// ../src/Client.ts\n\n\n\n\nvar Client = (function (_super) {\n    __extends(Client, _super);\n    function Client(ajaxClient, config) {\n        return _super.call(this, ajaxClient, config) || this;\n    }\n    Client = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.AjaxServices, Config])\n    ], Client);\n    return Client;\n}(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.BaseClient));\n\n\n;// ../src/store/Epics/Catalog.ts\n\n\n\n\n\n\n\n\nvar errorOccured = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.errorOccured, setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus, clearCachedState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.clearCachedState, finalizeRestriction = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.finalizeRestriction;\nvar CatalogEpics = (function () {\n    function CatalogEpics(client, config) {\n        this.client = client;\n        this.config = config;\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    CatalogEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.requestCatalogEpic, this.togglePlanSelectionEpic, this.finalizeRestrictionEpic);\n    };\n    Object.defineProperty(CatalogEpics.prototype, \"requestCatalogEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === getInternetCatalog.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var _a;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)), _this.client.get(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.appendRefreshOnce(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getURLByFlowType((_a = {},\n                        _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.TV] = _this.config.api.catalogAPI,\n                        _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET] = _this.config.api.catalogAPI,\n                        _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE] = _this.config.api.bundleCatalogAPI,\n                        _a)))).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (response) {\n                        return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FilterRestrictionObservable)(response, [\n                            setInternetCatalog(response.data),\n                            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageLoaded(),\n                            setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED)\n                        ]);\n                    })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(errorOccured(new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandler(\"getInternetCatalog\", error))); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CatalogEpics.prototype, \"togglePlanSelectionEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) {\n                    return action.type === togglePackageSelection.toString();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.concat)((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING)), _this.client.action(payload).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (response) {\n                        return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FilterRestrictionObservable)(response, [\n                            updateInternetCatalog(response.data, state$.value.catalog),\n                            clearCachedState([external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.PREVIEW]),\n                            setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED)\n                        ]);\n                    })));\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(errorOccured(new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Models.ErrorHandler(\"togglePackageSelection\", error))); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(CatalogEpics.prototype, \"finalizeRestrictionEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) {\n                    return action.type === finalizeRestriction.toString();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (_a) {\n                    var payload = _a.payload;\n                    return Boolean(payload) &&\n                        Boolean(payload.productOfferingDetail) &&\n                        _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING;\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var payload = _a.payload;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setProductConfigurationTotal((0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(payload, \"productOfferingDetail.productConfigurationTotal\"))), updateInternetCatalog(payload, state$.value.catalog), clearCachedState([external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.PREVIEW]), setWidgetStatus(_this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.RENDERED));\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    CatalogEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, Config])\n    ], CatalogEpics);\n    return CatalogEpics;\n}());\n\n\n;// ../src/store/Epics/UserAccount.ts\n\n\n\n\n\n\n\n\nvar UserAccountEpics = (function () {\n    function UserAccountEpics(client, config) {\n        this.client = client;\n        this.config = config;\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    UserAccountEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.requestDataEpic);\n    };\n    Object.defineProperty(UserAccountEpics.prototype, \"requestDataEpic\", {\n        get: function () {\n            var _this = this;\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(getAccountDetails.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function () { return _this.widgetState !== external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.UPDATING; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () { return _this.client.get(_this.config.api.serviceAccountAPI).pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function (_a) {\n                    var data = _a.data;\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setAccountDetails(data), getInternetCatalog());\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(setAccountDetails({})); })); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    UserAccountEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, Config])\n    ], UserAccountEpics);\n    return UserAccountEpics;\n}());\n\n\n;// ../src/store/Epics/Omniture.ts\n\n\n\n\n\nvar omniPageLoaded = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageLoaded, omniPageSubmit = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageSubmit;\nvar OmnitureEpics = (function () {\n    function OmnitureEpics() {\n        this.widgetState = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT;\n    }\n    OmnitureEpics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.pageLoadedEpic, this.pageSubmitEpic);\n    };\n    Object.defineProperty(OmnitureEpics.prototype, \"pageLoadedEpic\", {\n        get: function () {\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(omniPageLoaded.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var accountDetails = state$.value.accountDetails;\n                    var omniture = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture();\n                    omniture.trackFragment({\n                        id: \"InternetPage\",\n                        s_oSS1: \"~\",\n                        s_oSS2: \"~\",\n                        s_oSS3: \"~\",\n                        s_oPGN: \"~\",\n                        s_oAPT: {\n                            actionresult: 1\n                        },\n                        s_oPLE: {\n                            type: external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.EMessageType.Information,\n                            content: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(accountDetails, \"0.Name\", \"\")\n                        }\n                    });\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(OmnitureEpics.prototype, \"pageSubmitEpic\", {\n        get: function () {\n            return function (action$, state$) {\n                return action$.pipe((0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.type === omniPageSubmit.toString(); }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var catalog = state$.value.catalog;\n                    var omniture = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture();\n                    omniture.trackAction({\n                        id: \"internetPageSubmit\",\n                        s_oAPT: {\n                            actionId: 647\n                        },\n                        s_oBTN: \"Continue\",\n                        s_oPRD: catalog\n                            .filter(function (pkg) { return (pkg.isSelected && !pkg.isCurrent); })\n                            .map(function (pkg) { return ({\n                            category: \"Internet\",\n                            name: pkg.name,\n                            sku: \"\",\n                            quantity: \"1\",\n                            price: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(pkg, \"regularPrice.price\", \"0\"),\n                            promo: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(pkg, \"promotionDetails.promotionalPrice.price\", \"\")\n                        }); })\n                    });\n                    return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)();\n                }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.catchError)(function (error) { return (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.of)(); }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    OmnitureEpics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], OmnitureEpics);\n    return OmnitureEpics;\n}());\n\n\n;// ../src/store/Epics.ts\n\n\n\n\n\n\n\n\n\nvar Epics_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus;\nvar Epics = (function () {\n    function Epics(catalogEpics, userAccountEpics, omniture) {\n        this.catalogEpics = catalogEpics;\n        this.userAccountEpics = userAccountEpics;\n        this.omniture = omniture;\n    }\n    Epics.prototype.combineEpics = function () {\n        return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.onWidgetStatusEpic);\n    };\n    Object.defineProperty(Epics.prototype, \"onWidgetStatusEpic\", {\n        get: function () {\n            return function (action$) {\n                return action$.pipe((0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.ofType)(Epics_setWidgetStatus.toString()), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.filter)(function (action) { return action.payload === external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT; }), (0,external_root_rxjs_commonjs2_rxjs_commonjs_rxjs_amd_rxjs_.mergeMap)(function () {\n                    var action, s_oSS2 = \"~\";\n                    switch (external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Utils.getFlowType()) {\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.INTERNET:\n                            action = 523;\n                            s_oSS2 = \"Internet\";\n                            break;\n                        case external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EFlowType.BUNDLE:\n                            s_oSS2 = \"Bundle\";\n                            break;\n                        default:\n                            break;\n                    }\n                    external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().updateContext({\n                        s_oSS1: \"~\",\n                        s_oSS2: s_oSS2,\n                        s_oSS3: \"Change package\",\n                        s_oPGN: \"Setup your service\",\n                        s_oAPT: {\n                            actionId: action\n                        }\n                    });\n                    return [\n                        getAccountDetails()\n                    ];\n                }));\n            };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Epics = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [CatalogEpics,\n            UserAccountEpics,\n            OmnitureEpics])\n    ], Epics);\n    return Epics;\n}());\n\n\n;// ../src/Localization.ts\n\n\n\nvar BaseLocalization = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseLocalization;\nvar Localization = (function (_super) {\n    __extends(Localization, _super);\n    function Localization() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Localization_1 = Localization;\n    Localization.getLocalizedString = function (id) {\n        Localization_1.Instance = Localization_1.Instance || external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ServiceLocator.instance.getService(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonServices.Localization);\n        var instance = Localization_1.Instance;\n        return instance ? instance.getLocalizedString(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.INTERNET, id, instance.locale) : id;\n    };\n    var Localization_1;\n    Localization.Instance = null;\n    Localization = Localization_1 = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable\n    ], Localization);\n    return Localization;\n}(BaseLocalization));\n\n\n;// ../src/store/Store.ts\n\n\n\n\n\n\n\n\n\n\nvar BaseStore = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BaseStore, actionsToComputedPropertyName = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.actionsToComputedPropertyName;\nvar _a = actionsToComputedPropertyName(Actions_namespaceObject), Store_setAccountDetails = _a.setAccountDetails, Store_setInternetCatalog = _a.setInternetCatalog, Store_updateInternetCatalog = _a.updateInternetCatalog;\nvar Store = (function (_super) {\n    __extends(Store, _super);\n    function Store(client, store, epics, localization) {\n        var _this = _super.call(this, store) || this;\n        _this.client = client;\n        _this.epics = epics;\n        _this.localization = localization;\n        return _this;\n    }\n    Object.defineProperty(Store.prototype, \"reducer\", {\n        get: function () {\n            var _a, _b;\n            return (0,external_root_Redux_commonjs2_redux_commonjs_redux_amd_redux_.combineReducers)(__assign(__assign(__assign(__assign({}, external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetBaseLifecycle(this.localization)), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetLightboxes()), external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Reducers.WidgetRestrictions()), { accountDetails: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_a = {},\n                    _a[Store_setAccountDetails] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _a), [{}]), catalog: (0,external_root_ReduxActions_commonjs2_redux_actions_commonjs_redux_actions_amd_redux_actions_.handleActions)((_b = {},\n                    _b[Store_setInternetCatalog] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _b[Store_updateInternetCatalog] = function (state, _a) {\n                        var payload = _a.payload;\n                        return payload || state;\n                    },\n                    _b), []) }));\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Store.prototype, \"middlewares\", {\n        get: function () {\n            return (0,external_root_ReduxObservable_commonjs2_redux_observable_commonjs_redux_observable_amd_redux_observable_.combineEpics)(this.epics.omniture.combineEpics(), this.epics.userAccountEpics.combineEpics(), this.epics.catalogEpics.combineEpics(), this.epics.combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ModalEpics().combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.RestricitonsEpics(this.client, \"INTERNET_RESTRICTION_MODAL\").combineEpics(), new external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.LifecycleEpics().combineEpics());\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Store = __decorate([\n        external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Injectable,\n        __metadata(\"design:paramtypes\", [Client, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Store, Epics, Localization])\n    ], Store);\n    return Store;\n}(BaseStore));\n\n\n;// ../src/store/index.ts\n\n\n\n;// ../src/Pipe.ts\n\n\n\nvar BasePipe = external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.CommonFeatures.BasePipe;\nvar Pipe = (function (_super) {\n    __extends(Pipe, _super);\n    function Pipe(arg) {\n        var _this = _super.call(this, arg) || this;\n        Pipe.instance = _this;\n        return _this;\n    }\n    Pipe.Subscriptions = function (store) {\n        var _a;\n        return _a = {},\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.onContinue.toString()] = function () {\n                store.dispatch(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageSubmit());\n                external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.historyForward());\n            },\n            _a[external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageSubmit.toString()] = function () {\n                external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.broadcastUpdate(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.omniPageSubmit());\n            },\n            _a;\n    };\n    return Pipe;\n}(BasePipe));\n\n\n// EXTERNAL MODULE: external {\"root\":\"React\",\"commonjs2\":\"react\",\"commonjs\":\"react\",\"amd\":\"react\"}\nvar external_root_React_commonjs2_react_commonjs_react_amd_react_ = __webpack_require__(\"react\");\n// EXTERNAL MODULE: external {\"root\":\"ReactIntl\",\"commonjs2\":\"react-intl\",\"commonjs\":\"react-intl\",\"amd\":\"react-intl\"}\nvar external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_ = __webpack_require__(\"react-intl\");\n;// ../src/views/header/index.tsx\n\n\n\n\n\n\nvar Component = function (_a) {\n    var accountDetails = _a.accountDetails;\n    var _b = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(false), 2), expanded = _b[0], toggleState = _b[1];\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        if (expanded) {\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n                id: \"myCurrentPackageClick\",\n                s_oAPT: {\n                    actionId: 648\n                },\n                s_oEPN: \"My current Home Internet package\"\n            });\n        }\n    }, [expanded]);\n    var collapseIcon = expanded ? \"icon-Collapse\" : \"icon-Expand\";\n    return (0,jsx_runtime.jsx)(\"section\", { className: \"bgVirginGradiant accss-focus-outline-override-pad\", children: (0,jsx_runtime.jsx)(\"div\", { className: \"container liquid-container sans-serif\", children: (0,jsx_runtime.jsxs)(\"div\", { className: \"accordion-group internet-current-package flexCol\", children: [(0,jsx_runtime.jsx)(\"div\", { className: \"accordion-heading col-xs-12 noPaddingImp\", children: (0,jsx_runtime.jsxs)(\"a\", { id: \"accordion_expand_link\", href: \"javascript:void(0)\", onClick: function () { return toggleState(!expanded); }, \"aria-controls\": \"div1-accessible\", className: \"accordion-accessible-toggle txtSize18 txtDecorationNoneHover txtWhite flexRow align-items-center accss-width-fit-content\", \"aria-expanded\": expanded, role: \"button\", children: [(0,jsx_runtime.jsx)(\"span\", { className: \"sr-only accordion-label\", \"aria-live\": \"polite\", \"aria-atomic\": \"true\", \"aria-hidden\": \"true\", children: (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: expanded ? \"Collapse\" : \"Expand\" }) }), (0,jsx_runtime.jsxs)(\"span\", { className: \"\".concat(collapseIcon, \" virgin-icon txtSize24 virginRedIcon\"), \"aria-hidden\": \"true\", children: [(0,jsx_runtime.jsx)(\"span\", { className: \"virgin-icon path1 \".concat(collapseIcon) }), (0,jsx_runtime.jsx)(\"span\", { className: \"virgin-icon path2 \".concat(collapseIcon) })] }), (0,jsx_runtime.jsxs)(\"div\", { className: \"margin-15-left flexCol\", children: [(0,jsx_runtime.jsx)(\"span\", { className: \"txtWhite txtBold txtSize18\", children: (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"My current Home Internet package\" }) }), (0,jsx_runtime.jsx)(\"span\", { className: \"expand txtWhite txtSize12 no-margin-top\", style: { display: expanded ? \"none\" : undefined }, children: (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Expand to view details\" }) })] })] }) }), (0,jsx_runtime.jsx)(\"div\", { id: \"div1-accessible\", className: \"collapse-accordion-accessible-toggle accordion-body txtWhite col-xs-12 margin-5-top margin-40-left\", style: { display: expanded ? \"block\" : \"none\" }, children: (0,jsx_runtime.jsx)(\"div\", { className: \"accordion-inner\", children: accountDetails.map(function (_a) {\n                                var Name = _a.Name, RegularPrice = _a.RegularPrice, PromotionDetails = _a.PromotionDetails;\n                                return (0,jsx_runtime.jsxs)(\"div\", { className: \"col-sm-5\", children: [(0,jsx_runtime.jsx)(\"div\", { className: \"spacer10\", \"aria-hidden\": \"true\" }), (0,jsx_runtime.jsxs)(\"div\", { className: \"flexRow flexEnd\", children: [(0,jsx_runtime.jsx)(\"div\", { className: \"flexGrow\", children: Name }), (0,jsx_runtime.jsxs)(\"div\", { style: { whiteSpace: \"nowrap\" }, children: [(0,jsx_runtime.jsx)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.BellCurrency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(RegularPrice, \"Price\", 0) }), (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"PER_MO\" })] })] }), (0,jsx_runtime.jsxs)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: !!PromotionDetails, children: [(0,jsx_runtime.jsx)(\"div\", { className: \"spacer5\", \"aria-hidden\": \"true\" }), (0,jsx_runtime.jsx)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"Description\", false), children: (0,jsx_runtime.jsxs)(\"div\", { className: \"flexRow\", children: [(0,jsx_runtime.jsx)(\"div\", { className: \"flexGrow\", children: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"Description\", \"\") }), (0,jsx_runtime.jsxs)(\"div\", { children: [(0,jsx_runtime.jsx)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.BellCurrency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"PromotionalPrice.Price\", 0) }), (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"PER_MO\" })] })] }) }), (0,jsx_runtime.jsx)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"ExpiryDate\", false), children: (0,jsx_runtime.jsx)(\"div\", { children: (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(PromotionDetails, \"ExpiryDate\", \"\"), format: \"yMMMMd\", timeZone: \"UTC\", children: function (expiryDate) { return (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"PromotionExpires\", values: { expiryDate: expiryDate } }); } }) }) })] })] });\n                            }) }) })] }) }) });\n};\nvar Header = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var accountDetails = _a.accountDetails;\n    return ({ accountDetails: accountDetails || [] });\n})(Component);\n\n;// ../src/views/catalog/Package.tsx\n\n\n\n\n\n\n\nvar Visible = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, Currency = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Currency;\nvar Package_Component = function (_a) {\n    var id = _a.id, name = _a.name, shortDescription = _a.shortDescription, usagePlan = _a.usagePlan, isSelectable = _a.isSelectable, isSelected = _a.isSelected, regularPrice = _a.regularPrice, promotionDetails = _a.promotionDetails, offeringAction = _a.offeringAction, onPackageClicked = _a.onPackageClicked;\n    var onPackageAction = function (e) {\n        e.stopPropagation();\n        e.preventDefault();\n        if (isSelected)\n            return;\n        if (e.keyCode === undefined || e.keyCode === 32 || e.keyCode === 13) {\n            onPackageClicked(offeringAction);\n        }\n    };\n    var _b = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(false), 2), uploadExpanded = _b[0], ExpandUpload = _b[1];\n    var onUploadClick = function (e) {\n        if ((e.keyCode === undefined || e.keyCode === 13) && e.target.classList.contains(\"txtUnderline\")) {\n            ExpandUpload(!uploadExpanded);\n        }\n    };\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        $(\"#\" + id)\n            .find(\"[data-toggle]\")\n            .addClass(\"txtUnderline txtBlue pointer accss-text-blue-on-bg-white accss-width-fit-content\")\n            .attr(\"tabindex\", \"0\")\n            .next()\n            .addClass(\"downloadTray\")\n            .removeAttr(\"id\");\n    });\n    return (0,jsx_runtime.jsx)(\"div\", { id: id, className: \"virgin-internet-box txtGray margin-15-bottom \".concat(isSelected ? \"selected\" : \"\"), children: (0,jsx_runtime.jsxs)(\"div\", { className: \"flexRow bgWhite border-radius-3 virgin-title-block pad-30 pad-15-left-right-sm accss-focus-outline-override-white-bg\", children: [(0,jsx_runtime.jsx)(\"div\", { className: \"package_ctrl\", children: (0,jsx_runtime.jsxs)(\"span\", { id: \"CTA_\".concat(id), className: \"graphical_ctrl ctrl_radioBtn pointer\", onClick: onPackageAction, children: [(0,jsx_runtime.jsx)(\"input\", { id: \"OPT_\".concat(id), name: \"internetpackage\", checked: isSelected, type: \"radio\", \"aria-labelledby\": \"PACKAGE_CTA_\".concat(id), \"aria-describedby\": \"PACKAGE_CTA_DESC_\".concat(id), \"aria-checked\": isSelected, className: \"radioBtn-active data-feature\" }), (0,jsx_runtime.jsx)(\"span\", { className: \"ctrl_element pointer data-addon-active data-addon-border\" })] }) }), (0,jsx_runtime.jsxs)(\"div\", { className: \"package-desc fill\", children: [(0,jsx_runtime.jsx)(\"div\", { id: \"PACKAGE_CTA_\".concat(id), className: \"fill pad-15-left content-width valign-top pad-0-xs pointer\", onClick: onPackageAction, children: (0,jsx_runtime.jsx)(\"h2\", { className: \"virginUltraReg txtSize16 floatL txtUppercase no-margin\", children: name }) }), (0,jsx_runtime.jsx)(\"div\", { className: \"spacer10 d-none d-sm-block d-md-none clear\", \"aria-hidden\": \"true\" }), (0,jsx_runtime.jsx)(\"div\", { className: \"spacer15 clear\", \"aria-hidden\": \"true\" }), (0,jsx_runtime.jsx)(\"div\", { className: \"spacer1 bgGrayLight6 clear margin-30-right\", \"aria-hidden\": \"true\" }), (0,jsx_runtime.jsx)(\"div\", { className: \"spacer15 hidden-m\", \"aria-hidden\": \"true\" }), (0,jsx_runtime.jsx)(\"div\", { className: \"pkg-pull-left neg-margin-left-40-sm flexBlock\", id: \"PACKAGE_CTA_DESC_\".concat(id), children: (0,jsx_runtime.jsxs)(\"div\", { className: \"flexRow fill flexCol-xs\", children: [(0,jsx_runtime.jsx)(\"ul\", { id: \"UPLOAD_CTA_\".concat(id), className: \"speed-box1 flexRow flexCol-xs mb-0 pl-0 list-unstyled \".concat(uploadExpanded ? \"expanded\" : \"\"), onKeyUp: onUploadClick, onClick: onUploadClick, dangerouslySetInnerHTML: { __html: shortDescription } }), (0,jsx_runtime.jsx)(\"ul\", { className: \"speed-box2 mb-0 list-unstyled\", dangerouslySetInnerHTML: { __html: usagePlan } }), (0,jsx_runtime.jsx)(\"div\", { className: \"speed-box3\", children: (0,jsx_runtime.jsxs)(\"div\", { className: \"pad-30-left no-pad-xs margin-10-left-xs\", children: [(0,jsx_runtime.jsx)(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"expiryDate\", false), children: (0,jsx_runtime.jsx)(\"span\", { className: \"txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3\", children: (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedDate, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"expiryDate\", \"\"), format: \"yMMMMd\", timeZone: \"UTC\", children: function (expiryDate) { return (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Your monthly credit expires\", values: { expiryDate: expiryDate } }); } }) }) }), (0,jsx_runtime.jsx)(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"discountDuration\", false), children: (0,jsx_runtime.jsx)(\"span\", { className: \"txtSize12 txtBlack block txtBold bgGray2 pad-5 border-radius-3\", children: (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Get a credit for months\", values: { credit: Math.abs((0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"discountPrice.price\", 0)), duration: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"discountDuration\", 0) } }) }) }), (0,jsx_runtime.jsx)(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(!promotionDetails, undefined, false), children: (0,jsx_runtime.jsx)(\"div\", { className: \"spacer15 clear hidden-m\", \"aria-hidden\": \"true\" }) }), (0,jsx_runtime.jsxs)(\"div\", { className: \"price virginUltraReg txtSize40 line-height-1 margin-10-top\", children: [(0,jsx_runtime.jsxs)(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, undefined, false), children: [(0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Now\" }), \"\\u00A0\"] }), (0,jsx_runtime.jsx)(Currency, { value: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"?promotionalPrice.price\", false) === false\n                                                                ? (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(regularPrice, \"price\", 0)\n                                                                : (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"promotionalPrice.price\", 0), monthly: true }), (0,jsx_runtime.jsx)(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, undefined, false), children: (0,jsx_runtime.jsx)(\"p\", { className: \"txtSize12 txtBlack txtBold sans-serif no-margin\", children: (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Current Price\", values: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(regularPrice, undefined, {}) }) }) })] }), (0,jsx_runtime.jsx)(Visible, { when: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, undefined, false), children: (0,jsx_runtime.jsx)(\"p\", { className: \"txtSize12 txtBlack sans-serif no-margin pad-10-top\", children: (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(promotionDetails, \"legalMessage\", (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Prices may increase legal\" })) }) })] }) })] }) })] })] }) });\n};\nvar Package = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) { return ({}); }, function (dispatch) { return ({\n    onPackageClicked: function (action) { return dispatch(togglePackageSelection(action)); }\n}); })(Package_Component);\n\n;// ../src/views/catalog/Legal.tsx\n\n\n\n\n\nvar Footer = function () {\n    var _a = __read(external_root_React_commonjs2_react_commonjs_react_amd_react_.useState(false), 2), expanded = _a[0], toggleState = _a[1];\n    external_root_React_commonjs2_react_commonjs_react_amd_react_.useEffect(function () {\n        expanded &&\n            external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Omniture.useOmniture().trackAction({\n                id: \"ligalStuffClick\",\n                s_oAPT: {\n                    actionId: 648\n                },\n                s_oEPN: \"Legal Stuff\"\n            });\n    }, [expanded]);\n    return (0,jsx_runtime.jsxs)(\"div\", { className: \"virginUltraReg margin-15-top\", id: \"moreInfo\", children: [(0,jsx_runtime.jsxs)(\"button\", { id: \"Legal_stuff\", className: \"btn btn-link noUnderlineAll noPaddingImp links-blue-on-bg-gray accss-focus-outline-override-grey-bg-element\", onClick: function () { return toggleState(!expanded); }, \"aria-expanded\": expanded, children: [(0,jsx_runtime.jsx)(\"span\", { className: \"volt-icon \".concat(expanded ? \"icon-collapse_m\" : \"icon-expand_m\"), \"aria-hidden\": \"true\" }), \"\\u00A0\\u00A0\", (0,jsx_runtime.jsx)(external_root_ReactIntl_commonjs2_react_intl_commonjs_react_intl_amd_react_intl_.FormattedMessage, { id: \"Legal stuff label\" })] }), (0,jsx_runtime.jsx)(\"div\", { className: \"spacer30\", \"aria-hidden\": \"true\" }), (0,jsx_runtime.jsx)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.Visible, { when: expanded, children: (0,jsx_runtime.jsxs)(\"div\", { className: \"moreInfoBox bgWhite pad30 margin-30-bottom accss-link-override accss-focus-outline-override-white-bg\", children: [(0,jsx_runtime.jsx)(\"button\", { id: \"LEGALBOX_CLOSE\", type: \"button\", onClick: function () { return toggleState(false); }, className: \"close moreInfoLink x-inner txtDarkGrey txtSize18 txtBold\", \"aria-label\": \"close\", children: (0,jsx_runtime.jsx)(\"span\", { className: \"virgin-icon icon-big_X\", \"aria-hidden\": \"true\" }) }), (0,jsx_runtime.jsx)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.FormattedHTMLMessage, { id: \"GOOD TO KNOW\" })] }) })] });\n};\n\n;// ../src/utils/Characteristics.ts\n\nfunction toCharacteristicsJSON(charactgerstics) {\n    return (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(charactgerstics, undefined, []).reduce(function (json, charactgerstic) {\n        if (charactgerstic.name) {\n            json[charactgerstic.name] = charactgerstic.value;\n        }\n        return json;\n    }, {});\n}\n\n;// ../src/views/catalog/index.tsx\n\n\n\n\n\n\n\nvar catalog_Component = function (_a) {\n    var catalog = _a.catalog;\n    return (0,jsx_runtime.jsxs)(\"div\", { className: \"container liquid-container noSpacing\", role: \"radiogroup\", children: [(0,jsx_runtime.jsx)(\"style\", { children: \".icon-upload-ico:before {\\n                    content: \\\"\\\\e99d\\\";\\n                }\\n                .icon-download-ico:before {\\n                    content: \\\"\\\\e929\\\";\\n                }\\n                .package-desc li {\\n                    display: block;\\n                    list-style: none;\\n                    position: relative;\\n                    width: calc(100% / 2);\\n                }\\n                    .package-desc li:not(:last-of-type) {\\n                        padding-right: 15px;\\n                    }\\n                    .package-desc li .volt-icon {\\n                        position: absolute;\\n                        display: block;\\n                        color: #cc0000;\\n                        font-size: 32px;\\n                        width: 42px;\\n                        height: 42px;\\n                        left: 0;\\n                    }\\n                    .package-desc li span {\\n                        display: block;\\n                        font-size: 12px;\\n                    }\\n                    .package-desc .speed-box2 span:first-of-type,\\n                    .package-desc li span.speed {\\n                        font-size: 22px;\\n                        color: black;\\n                        text-transform: uppercase;\\n                        font-family: \\\"VMUltramagneticNormalRegular\\\", Helvetica, Arial, sans-serif;\\n                }\\n                .package-desc .speed-box2 span.usage {\\n                    white-space: nowrap;\\n                }\\n                .speed-box1 li {\\n                    margin-top: 10px;\\n                    padding-left: 42px;\\n                }\\n                .package-desc li span.downloadTray {\\n                    display: none;\\n                }\\n                .package-desc .speed-box1.expanded li span.downloadTray {\\n                    display: block;\\n                }\\n                @media (max-width: 991.98px) {\\n                    .package-desc li {\\n                        width: 100%;\\n                    }\\n                  .pkg-pull-left {\\n                      margin-left: -40px;\\n                  }\\n                }\" }), catalog.filter(function (pkg) { return !pkg.isCurrent; })\n                .sort(function (a, b) { return ((0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(toCharacteristicsJSON(a.characteristics), \"sortPriority\", 0) -\n                (0,external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ValueOf)(toCharacteristicsJSON(b.characteristics), \"sortPriority\", 0)); })\n                .map(function (internetPackage) { return (0,jsx_runtime.jsx)(Package, __assign({}, internetPackage)); }), (0,jsx_runtime.jsx)(Footer, {})] });\n};\nvar Catalog = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) {\n    var catalog = _a.catalog;\n    return ({ catalog: catalog });\n})(catalog_Component);\n\n;// ../src/views/index.tsx\n\n\n\n\n\n\n\nvar RestrictionModal = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.RestrictionModal;\nvar views_errorOccured = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.errorOccured, widgetRenderComplete = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.widgetRenderComplete;\nvar views_Component = (function (_super) {\n    __extends(Component, _super);\n    function Component() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    Component.prototype.componentDidCatch = function (err) {\n        this.props.onErrorEncountered(err);\n    };\n    Component.prototype.componentDidMount = function () {\n        this.props.widgetRenderComplete(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetName.INTERNET);\n    };\n    Component.prototype.render = function () {\n        return (0,jsx_runtime.jsxs)(\"main\", { id: \"mainContent\", children: [(0,jsx_runtime.jsx)(Header, {}), (0,jsx_runtime.jsx)(\"div\", { className: \"spacer30\", \"aria-hidden\": \"true\" }), (0,jsx_runtime.jsx)(Catalog, {}), (0,jsx_runtime.jsx)(RestrictionModal, { id: \"INTERNET_RESTRICTION_MODAL\" })] });\n    };\n    return Component;\n}(external_root_React_commonjs2_react_commonjs_react_amd_react_.Component));\nvar Application = (0,external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.connect)(function (_a) { return ({}); }, function (dispatch) { return ({\n    onErrorEncountered: function (error) { return dispatch(views_errorOccured(error)); },\n    widgetRenderComplete: function () { return dispatch(widgetRenderComplete()); }\n}); })(views_Component);\n\n;// ../src/App.tsx\n\n\n\nvar ApplicationRoot = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Components.ApplicationRoot;\nvar App = function () { return (0,jsx_runtime.jsx)(ApplicationRoot, { children: (0,jsx_runtime.jsx)(Application, {}) }); };\n\n;// ../src/Widget.tsx\n\n\n\n\n\n\n\n\n\nvar setWidgetProps = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetProps, Widget_setWidgetStatus = external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.Actions.setWidgetStatus;\nvar WidgetContainer = (function (_super) {\n    __extends(WidgetContainer, _super);\n    function WidgetContainer(store, params, config, pipe) {\n        var _this = _super.call(this) || this;\n        _this.store = store;\n        _this.params = params;\n        _this.config = config;\n        _this.pipe = pipe;\n        return _this;\n    }\n    WidgetContainer.prototype.init = function () {\n        this.pipe.subscribe(Pipe.Subscriptions(this.store));\n        this.store.dispatch(setWidgetProps(this.config));\n        this.store.dispatch(setWidgetProps(this.params.props));\n        this.store.dispatch(Widget_setWidgetStatus(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.EWidgetStatus.INIT));\n    };\n    WidgetContainer.prototype.destroy = function () {\n        this.pipe.unsubscribe();\n        this.store.destroy();\n    };\n    WidgetContainer.prototype.render = function (root) {\n        var store = this.store;\n        root.render((0,jsx_runtime.jsx)(external_root_OMFChangepackageComponents_commonjs2_omf_changepackage_components_commonjs_omf_changepackage_components_amd_omf_changepackage_components_.ContextProvider, { value: { config: this.config }, children: (0,jsx_runtime.jsx)(external_root_ReactRedux_commonjs2_react_redux_commonjs_react_redux_amd_react_redux_.Provider, { store: store, children: (0,jsx_runtime.jsx)(App, {}) }) }));\n    };\n    WidgetContainer = __decorate([\n        (0,external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.Widget)({ namespace: \"Ordering\" }),\n        __metadata(\"design:paramtypes\", [Store, external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ParamsProvider, Config, Pipe])\n    ], WidgetContainer);\n    return WidgetContainer;\n}(external_root_bwtk_commonjs2_bwtk_commonjs_bwtk_amd_bwtk_.ViewWidget));\n/* harmony default export */ var Widget = (WidgetContainer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi4vc3JjL1dpZGdldC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsU0FBUyxnQkFBZ0Isc0NBQXNDLGtCQUFrQjtBQUNqRix3QkFBd0I7QUFDeEI7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQjtBQUNsQjtBQUNBOztBQUVPO0FBQ1A7QUFDQSwrQ0FBK0MsT0FBTztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkRBQTJELGNBQWM7QUFDekU7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQSwyQ0FBMkMsUUFBUTtBQUNuRDtBQUNBOztBQUVPO0FBQ1Asa0NBQWtDO0FBQ2xDOztBQUVPO0FBQ1AsdUJBQXVCLHVGQUF1RjtBQUM5RztBQUNBO0FBQ0EseUdBQXlHO0FBQ3pHO0FBQ0Esc0NBQXNDLFFBQVE7QUFDOUM7QUFDQSxnRUFBZ0U7QUFDaEU7QUFDQSw4Q0FBOEMseUZBQXlGO0FBQ3ZJLDhEQUE4RCwyQ0FBMkM7QUFDekc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQSxrQkFBa0IseUJBQXlCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTs7QUFFTztBQUNQO0FBQ0EsNENBQTRDLHlFQUF5RTtBQUNySDs7QUFFTztBQUNQO0FBQ0E7O0FBRU87QUFDUCwwQkFBMEIsK0RBQStELGlCQUFpQjtBQUMxRztBQUNBLGtDQUFrQyxNQUFNLCtCQUErQixZQUFZO0FBQ25GLGlDQUFpQyxNQUFNLG1DQUFtQyxZQUFZO0FBQ3RGLDhCQUE4QjtBQUM5QjtBQUNBLEdBQUc7QUFDSDs7QUFFTztBQUNQLFlBQVksNkJBQTZCLDBCQUEwQixjQUFjLHFCQUFxQjtBQUN0RywySUFBMkksY0FBYztBQUN6SixxQkFBcUIsc0JBQXNCO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQztBQUN0QyxpQ0FBaUMsU0FBUztBQUMxQyxpQ0FBaUMsV0FBVyxVQUFVO0FBQ3RELHdDQUF3QyxjQUFjO0FBQ3REO0FBQ0EsNEdBQTRHLE9BQU87QUFDbkgsK0VBQStFLGlCQUFpQjtBQUNoRyx1REFBdUQsZ0JBQWdCLFFBQVE7QUFDL0UsNkNBQTZDLGdCQUFnQixnQkFBZ0I7QUFDN0U7QUFDQSxnQ0FBZ0M7QUFDaEM7QUFDQTtBQUNBLFFBQVEsWUFBWSxhQUFhLFNBQVMsVUFBVTtBQUNwRCxrQ0FBa0MsU0FBUztBQUMzQztBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsZUFBZSxvQ0FBb0M7QUFDbkQ7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0EsQ0FBQzs7QUFFTTtBQUNQO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CO0FBQ25CO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLE1BQU07QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0I7QUFDaEI7QUFDQTtBQUNBOztBQUVBO0FBQ087QUFDUCwyQkFBMkIsc0JBQXNCO0FBQ2pEO0FBQ0E7QUFDQTs7QUFFQTtBQUNPO0FBQ1AsZ0RBQWdELFFBQVE7QUFDeEQsdUNBQXVDLFFBQVE7QUFDL0MsdURBQXVELFFBQVE7QUFDL0Q7QUFDQTtBQUNBOztBQUVPO0FBQ1AsMkVBQTJFLE9BQU87QUFDbEY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBLHdNQUF3TSxjQUFjO0FBQ3ROLDRCQUE0QixzQkFBc0I7QUFDbEQsd0JBQXdCLFlBQVksc0JBQXNCLHFDQUFxQywyQ0FBMkMsTUFBTTtBQUNoSiwwQkFBMEIsTUFBTSxpQkFBaUIsWUFBWTtBQUM3RCxxQkFBcUI7QUFDckIsNEJBQTRCO0FBQzVCLDJCQUEyQjtBQUMzQiwwQkFBMEI7QUFDMUI7O0FBRU87QUFDUDtBQUNBLGVBQWUsNkNBQTZDLFVBQVUsc0RBQXNELGNBQWM7QUFDMUksd0JBQXdCLDZCQUE2QixvQkFBb0IsdUNBQXVDLGtCQUFrQjtBQUNsSTs7QUFFTztBQUNQO0FBQ0E7QUFDQSx5R0FBeUcsdUZBQXVGLGNBQWM7QUFDOU0scUJBQXFCLDhCQUE4QixnREFBZ0Qsd0RBQXdEO0FBQzNKLDJDQUEyQyxzQ0FBc0MsVUFBVSxtQkFBbUIsSUFBSTtBQUNsSDs7QUFFTztBQUNQLCtCQUErQix1Q0FBdUMsWUFBWSxLQUFLLE9BQU87QUFDOUY7QUFDQTs7QUFFQTtBQUNBLHdDQUF3Qyw0QkFBNEI7QUFDcEUsQ0FBQztBQUNEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQSxxREFBcUQsY0FBYztBQUNuRTtBQUNBO0FBQ0E7O0FBRU87QUFDUCwyQ0FBMkM7QUFDM0M7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxNQUFNLG9CQUFvQixZQUFZO0FBQzVFLHFCQUFxQiw4Q0FBOEM7QUFDbkU7QUFDQTtBQUNBLHFCQUFxQixhQUFhO0FBQ2xDO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1RkFBdUYsU0FBUyxnQkFBZ0I7QUFDaEg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTs7QUFFQSw4Q0FBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLEVBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQy9ZMkQ7QUFFdEQsU0FBUyx1QkFBdUIsQ0FBQyxRQUE0QjtJQUNsRSxPQUFPLG1LQUFPLENBQXlCLFFBQVEsRUFBRSxrQkFBa0IsRUFBRSxDQUFDLEVBQUUsV0FBVyxFQUFFLElBQUksRUFBRSxDQUFDLENBQUMsQ0FBQztBQUNoRyxDQUFDO0FBRU0sU0FBUyxnQkFBZ0IsQ0FBQyxRQUEyQjtJQUMxRCxJQUFNLG9CQUFvQixHQUNwQixtS0FBTyxDQUFDLFFBQVEsRUFBRSw2Q0FBNkMsRUFBRSxFQUFFLENBQUM7U0FDakUsSUFBSSxDQUFDLFVBQUMsS0FBaUMsSUFBSyxZQUFLLENBQUMsY0FBYyxLQUFLLDRKQUFJLENBQUMsZUFBZSxDQUFDLFFBQVE7UUFDN0YsS0FBSyxDQUFDLHdCQUF3QixLQUFLLDRKQUFJLENBQUMseUJBQXlCLENBQUMsT0FBTyxFQURsQyxDQUNrQyxDQUFDLENBQUM7SUFDekYsT0FBTyxtS0FBTyxDQUFDLG9CQUFvQixFQUFFLGtCQUFrQixFQUFFLEVBQUUsQ0FBQyxDQUFDO0FBQy9ELENBQUM7QUFFTSxTQUFTLGNBQWMsQ0FBQyxRQUEyQixFQUFFLE9BQXdCO0lBQ2xGLElBQU0sb0JBQW9CLEdBQ3BCLG1LQUFPLENBQUMsUUFBUSxFQUFFLDZDQUE2QyxFQUFFLEVBQUUsQ0FBQztTQUNqRSxJQUFJLENBQUMsVUFBQyxLQUFpQyxJQUFLLFlBQUssQ0FBQyxjQUFjLEtBQUssNEpBQUksQ0FBQyxlQUFlLENBQUMsUUFBUTtRQUM3RixLQUFLLENBQUMsd0JBQXdCLEtBQUssNEpBQUksQ0FBQyx5QkFBeUIsQ0FBQyxLQUFLLEVBRGhDLENBQ2dDLENBQUMsQ0FBQztJQUN2RixJQUFNLGdCQUFnQixHQUFpQyxtS0FBTyxDQUFDLG9CQUFvQixFQUFFLGtCQUFrQixFQUFFLEVBQUUsQ0FBQyxDQUFDO0lBQzdHLGdCQUFnQixDQUFDLE9BQU8sQ0FBQyxpQkFBTztRQUM5QixJQUFNLE9BQU8sR0FBYSxPQUFPLENBQUMsSUFBSSxDQUFDLGFBQUcsSUFBSSxVQUFHLENBQUMsRUFBRSxLQUFLLE9BQU8sQ0FBQyxFQUFFLEVBQXJCLENBQXFCLENBQUMsSUFBSSxFQUFjLENBQUM7UUFDdkYsTUFBTSxDQUFDLE1BQU0sQ0FBQyxPQUFPLEVBQUUsT0FBTyxDQUFDLENBQUM7SUFDbEMsQ0FBQyxDQUFDLENBQUM7SUFDSCxPQUFPLHlCQUFJLE9BQU8sU0FBb0IsQ0FBQztBQUN6QyxDQUFDOzs7QUMxQm9EO0FBRW1DO0FBSWpGLElBQU0saUJBQWlCLEdBQUcsNkdBQVksQ0FBQyxxQkFBcUIsQ0FBQyxDQUFDO0FBQzlELElBQU0saUJBQWlCLEdBQUcsNkdBQVksQ0FBeUIscUJBQXFCLEVBQUUsdUJBQThCLENBQXFFLENBQUM7QUFDMUwsSUFBTSxrQkFBa0IsR0FBRyw2R0FBWSxDQUFDLHNCQUFzQixDQUFDLENBQUM7QUFDaEUsSUFBTSxrQkFBa0IsR0FBRyw2R0FBWSxDQUFrQixzQkFBc0IsRUFBRSxnQkFBdUIsQ0FBNkQsQ0FBQztBQUV0SyxJQUFNLHNCQUFzQixHQUFHLDZHQUFZLENBQXlCLHlCQUF5QixDQUFDLENBQUM7QUFDL0YsSUFBTSxxQkFBcUIsR0FBRyw2R0FBWSxDQUFrQix5QkFBeUIsRUFBRSxjQUFxQixDQUF1RixDQUFDOzs7Ozs7QUNaeko7QUFHMUMsY0FBVSxHQUFxQix3RUFBYyxXQUFuQyxFQUFFLGNBQWMsR0FBSyx3RUFBYyxlQUFuQixDQUFvQjtBQW9CdEQ7SUFBNEIsMEJBQXNCO0lBQWxEOztJQU1BLENBQUM7SUFMcUI7UUFBbkIsY0FBYyxDQUFDLEVBQUUsQ0FBQzs7NENBQWtCO0lBQ2pCO1FBQW5CLGNBQWMsQ0FBQyxFQUFFLENBQUM7O3dEQUEyQjtJQUMxQjtRQUFuQixjQUFjLENBQUMsRUFBRSxDQUFDOzs0Q0FBZTtJQUNkO1FBQW5CLGNBQWMsQ0FBQyxFQUFFLENBQUM7OzJDQUFjO0lBQ2dCO1FBQWhELGNBQWMsQ0FBQyxFQUFDLElBQUksRUFBRSx1QkFBdUIsRUFBQyxDQUFDOzt1Q0FBYztJQUxuRCxNQUFNO1FBRGxCLG9FQUFVO09BQ0UsTUFBTSxDQU1sQjtJQUFELGFBQUM7Q0FBQSxDQU4yQixVQUFVLEdBTXJDO0FBTmtCOzs7O0FDdkI2QjtBQUNVO0FBRXhCO0FBVWxDO0lBQTRCLDBCQUFVO0lBQ3BDLGdCQUFZLFVBQXdCLEVBQUUsTUFBYztRQUNsRCxhQUFLLFlBQUMsVUFBVSxFQUFFLE1BQU0sQ0FBQyxTQUFDO0lBQzVCLENBQUM7SUFIVSxNQUFNO1FBRGxCLG9FQUFVO3lDQUVlLHNFQUFZLEVBQVUsTUFBTTtPQUR6QyxNQUFNLENBSWxCO0lBQUQsYUFBQztDQUFBLENBSjJCLGtLQUFVLEdBSXJDO0FBSmtCOzs7O0FDYmU7QUFDb0I7QUFDaUg7QUFDMUY7QUFFdkM7QUFTbEI7QUFDa0I7QUFHcEMsZ0JBQVksR0FJViwrSkFBTyxhQUpHLEVBQ1osZUFBZSxHQUdiLCtKQUFPLGdCQUhNLEVBQ2YsZ0JBQWdCLEdBRWQsK0pBQU8saUJBRk8sRUFDaEIsbUJBQW1CLEdBQ2pCLCtKQUFPLG9CQURVLENBQ1Q7QUFHWjtJQUdFLHNCQUFvQixNQUFjLEVBQVUsTUFBYztRQUF0QyxXQUFNLEdBQU4sTUFBTSxDQUFRO1FBQVUsV0FBTSxHQUFOLE1BQU0sQ0FBUTtRQUYxRCxnQkFBVyxHQUFrQixxS0FBYSxDQUFDLElBQUksQ0FBQztJQUVjLENBQUM7SUFFL0QsbUNBQVksR0FBWjtRQUNFLE9BQU8seUhBQVksQ0FDakIsSUFBSSxDQUFDLGtCQUFrQixFQUN2QixJQUFJLENBQUMsdUJBQXVCLEVBQzVCLElBQUksQ0FBQyx1QkFBdUIsQ0FDN0IsQ0FBQztJQUNKLENBQUM7SUFFRCxzQkFBWSw0Q0FBa0I7YUFBOUI7WUFBQSxpQkEyQkM7WUExQkMsT0FBTyxVQUFDLE9BQWdDO2dCQUN0QyxjQUFPLENBQUMsSUFBSSxDQUNWLG9FQUFNLENBQUMsVUFBQyxNQUFNLElBQTRCLGFBQU0sQ0FBQyxJQUFJLEtBQUssa0JBQWtCLENBQUMsUUFBUSxFQUFFLEVBQTdDLENBQTZDLENBQUMsRUFDeEYsb0VBQU0sQ0FBQyxjQUFNLFlBQUksQ0FBQyxXQUFXLEtBQUsscUtBQWEsQ0FBQyxRQUFRLEVBQTNDLENBQTJDLENBQUMsRUFDekQsc0VBQVEsQ0FBQzs7b0JBQU0sMkVBQU0sQ0FDbkIsZ0VBQUUsQ0FBQyxlQUFlLENBQUMsS0FBSSxDQUFDLFdBQVcsR0FBRyxxS0FBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLEVBQzlELEtBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFrQyw2SkFBSyxDQUFDLGlCQUFpQixDQUN0RSw2SkFBSyxDQUFDLGdCQUFnQjt3QkFDcEIsR0FBQyxpS0FBUyxDQUFDLEVBQUUsSUFBRyxLQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxVQUFVO3dCQUMxQyxHQUFDLGlLQUFTLENBQUMsUUFBUSxJQUFHLEtBQUksQ0FBQyxNQUFNLENBQUMsR0FBRyxDQUFDLFVBQVU7d0JBQ2hELEdBQUMsaUtBQVMsQ0FBQyxNQUFNLElBQUcsS0FBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsZ0JBQWdCOzRCQUNwRCxDQUNILENBQUMsQ0FBQyxJQUFJLENBQ0wsc0VBQVEsQ0FBQyxVQUFDLFFBQXlDO3dCQUNqRCw4TEFBMkIsQ0FBQyxRQUFRLEVBQUU7NEJBQ3BDLGtCQUFrQixDQUFDLFFBQVEsQ0FBQyxJQUFJLENBQUM7NEJBQ2pDLCtKQUFPLENBQUMsY0FBYyxFQUFFOzRCQUN4QixlQUFlLENBQUMsS0FBSSxDQUFDLFdBQVcsR0FBRyxxS0FBYSxDQUFDLFFBQVEsQ0FBQzt5QkFDM0QsQ0FBQztvQkFKRixDQUlFLENBQ0gsQ0FDRixDQUNGO2dCQWpCYyxDQWlCZCxDQUFDLEVBQ0Ysd0VBQVUsQ0FBQyxVQUFDLEtBQWUsSUFBSyx1RUFBRSxDQUNoQyxZQUFZLENBQUMsSUFBSSw4SkFBTSxDQUFDLFlBQVksQ0FBQyxvQkFBb0IsRUFBRSxLQUFLLENBQUMsQ0FBQyxDQUNuRSxFQUYrQixDQUUvQixDQUFDLENBQ3lCO1lBeEI3QixDQXdCNkIsQ0FBQztRQUNsQyxDQUFDOzs7T0FBQTtJQUVELHNCQUFZLGlEQUF1QjthQUFuQztZQUFBLGlCQXVCQztZQXRCQyxPQUFPLFVBQUMsT0FBZ0MsRUFBRSxNQUFNO2dCQUM5QyxjQUFPLENBQUMsSUFBSSxDQUNWLG9FQUFNLENBQUMsVUFBQyxNQUFNO29CQUNaLGFBQU0sQ0FBQyxJQUFJLEtBQUssc0JBQXNCLENBQUMsUUFBUSxFQUFFO2dCQUFqRCxDQUFpRCxDQUNsRCxFQUNELG9FQUFNLENBQUMsY0FBTSxZQUFJLENBQUMsV0FBVyxLQUFLLHFLQUFhLENBQUMsUUFBUSxFQUEzQyxDQUEyQyxDQUFDLEVBQ3pELHNFQUFRLENBQUMsVUFBQyxFQUFXO3dCQUFULE9BQU87b0JBQ2pCLDJFQUFNLENBQ0osZ0VBQUUsQ0FBQyxlQUFlLENBQUMsS0FBSSxDQUFDLFdBQVcsR0FBRyxxS0FBYSxDQUFDLFFBQVEsQ0FBQyxDQUFDLEVBQzlELEtBQUksQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFrQyxPQUFPLENBQUMsQ0FBQyxJQUFJLENBQy9ELHNFQUFRLENBQUMsVUFBQyxRQUF5Qzt3QkFDakQsOExBQTJCLENBQUMsUUFBUSxFQUFFOzRCQUNwQyxxQkFBcUIsQ0FBQyxRQUFRLENBQUMsSUFBSSxFQUFHLE1BQWMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDOzRCQUNuRSxnQkFBZ0IsQ0FBQyxDQUFDLG1LQUFXLENBQUMsT0FBTyxDQUFDLENBQUM7NEJBQ3ZDLGVBQWUsQ0FBQyxLQUFJLENBQUMsV0FBVyxHQUFHLHFLQUFhLENBQUMsUUFBUSxDQUFDO3lCQUMzRCxDQUFDO29CQUpGLENBSUUsQ0FDSCxDQUNGLENBQ0Y7Z0JBWEQsQ0FXQyxDQUNGLEVBQ0Qsd0VBQVUsQ0FBQyxVQUFDLEtBQWUsSUFBSyx1RUFBRSxDQUFDLFlBQVksQ0FBQyxJQUFJLDhKQUFNLENBQUMsWUFBWSxDQUFDLHdCQUF3QixFQUFFLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBMUUsQ0FBMEUsQ0FBQyxDQUNoRjtZQXBCN0IsQ0FvQjZCLENBQUM7UUFDbEMsQ0FBQzs7O09BQUE7SUFFRCxzQkFBWSxpREFBdUI7YUFBbkM7WUFBQSxpQkFrQkM7WUFqQkMsT0FBTyxVQUFDLE9BQWdDLEVBQUUsTUFBTTtnQkFDOUMsY0FBTyxDQUFDLElBQUksQ0FDVixvRUFBTSxDQUFDLFVBQUMsTUFBTTtvQkFDWixhQUFNLENBQUMsSUFBSSxLQUFLLG1CQUFtQixDQUFDLFFBQVEsRUFBRTtnQkFBOUMsQ0FBOEMsQ0FDL0MsRUFDRCxvRUFBTSxDQUFDLFVBQUMsRUFBVzt3QkFBVCxPQUFPO29CQUNmLGNBQU8sQ0FBQyxPQUFPLENBQUM7d0JBQ2hCLE9BQU8sQ0FBQyxPQUFPLENBQUMscUJBQXFCLENBQUM7d0JBQ3RDLEtBQUksQ0FBQyxXQUFXLEtBQUsscUtBQWEsQ0FBQyxRQUFRO2dCQUYzQyxDQUUyQyxDQUM1QyxFQUNELHNFQUFRLENBQUMsVUFBQyxFQUFXO3dCQUFULE9BQU87b0JBQU8sdUVBQUUsQ0FDMUIsK0pBQU8sQ0FBQyxlQUFlLENBQUMsK0pBQU8sQ0FBQyw0QkFBNEIsQ0FBQyxtS0FBTyxDQUFDLE9BQU8sRUFBRSxpREFBaUQsQ0FBQyxDQUFDLENBQUMsRUFDbEkscUJBQXFCLENBQUMsT0FBTyxFQUFHLE1BQWMsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLEVBQzdELGdCQUFnQixDQUFDLENBQUMsbUtBQVcsQ0FBQyxPQUFPLENBQUMsQ0FBQyxFQUN2QyxlQUFlLENBQUMsS0FBSSxDQUFDLFdBQVcsR0FBRyxxS0FBYSxDQUFDLFFBQVEsQ0FBQyxDQUMzRDtnQkFMeUIsQ0FLekIsQ0FBQyxDQUN5QjtZQWY3QixDQWU2QixDQUFDO1FBQ2xDLENBQUM7OztPQUFBO0lBckZVLFlBQVk7UUFEeEIsb0VBQVU7eUNBSW1CLE1BQU0sRUFBa0IsTUFBTTtPQUgvQyxZQUFZLENBc0Z4QjtJQUFELG1CQUFDO0NBQUE7QUF0RndCOzs7O0FDekJTO0FBQzRCO0FBQ2E7QUFDbEI7QUFFbkI7QUFRbEI7QUFDa0I7QUFJdEM7SUFHRSwwQkFBb0IsTUFBYyxFQUFVLE1BQWM7UUFBdEMsV0FBTSxHQUFOLE1BQU0sQ0FBUTtRQUFVLFdBQU0sR0FBTixNQUFNLENBQVE7UUFGMUQsZ0JBQVcsR0FBa0IscUtBQWEsQ0FBQyxJQUFJLENBQUM7SUFFYyxDQUFDO0lBRS9ELHVDQUFZLEdBQVo7UUFDRSxPQUFPLHlIQUFZLENBQ2pCLElBQUksQ0FBQyxlQUFlLENBQ3JCLENBQUM7SUFDSixDQUFDO0lBRUQsc0JBQVksNkNBQWU7YUFBM0I7WUFBQSxpQkFhQztZQVpDLE9BQU8sVUFBQyxPQUFPLEVBQUUsTUFBTTtnQkFDckIsY0FBTyxDQUFDLElBQUksQ0FDVixtSEFBTSxDQUFDLGlCQUFpQixDQUFDLFFBQVEsRUFBRSxDQUFDLEVBQ3BDLG9FQUFNLENBQUMsY0FBTSxZQUFJLENBQUMsV0FBVyxLQUFLLHFLQUFhLENBQUMsUUFBUSxFQUEzQyxDQUEyQyxDQUFDLEVBQ3pELHNFQUFRLENBQUMsY0FBTSxZQUFJLENBQUMsTUFBTSxDQUFDLEdBQUcsQ0FBbUMsS0FBSSxDQUFDLE1BQU0sQ0FBQyxHQUFHLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxJQUFJLENBQ3RHLHNFQUFRLENBQUMsVUFBQyxFQUFzQzt3QkFBcEMsSUFBSTtvQkFBcUMsdUVBQUUsQ0FDckQsaUJBQWlCLENBQUMsSUFBSSxDQUFDLEVBQ3ZCLGtCQUFrQixFQUFFLENBQ3JCO2dCQUhvRCxDQUdwRCxDQUFDLEVBQ0Ysd0VBQVUsQ0FBQyxVQUFDLEtBQWUsSUFBSyx1RUFBRSxDQUFDLGlCQUFpQixDQUFDLEVBQXdCLENBQUMsQ0FBQyxFQUEvQyxDQUErQyxDQUFDLENBQ2pGLEVBTmMsQ0FNZCxDQUFDLENBQ0g7WUFWRCxDQVVDLENBQUM7UUFDTixDQUFDOzs7T0FBQTtJQXhCVSxnQkFBZ0I7UUFENUIsb0VBQVU7eUNBSW1CLE1BQU0sRUFBa0IsTUFBTTtPQUgvQyxnQkFBZ0IsQ0F5QjVCO0lBQUQsdUJBQUM7Q0FBQTtBQXpCNEI7Ozs7QUNsQks7QUFDNEI7QUFDMkI7QUFDcEI7QUFPbkUsa0JBQWMsR0FFWiwrSkFBTyxlQUZLLEVBQ2QsY0FBYyxHQUNaLCtKQUFPLGVBREssQ0FDSjtBQU9aO0lBQUE7UUFDRSxnQkFBVyxHQUFrQixxS0FBYSxDQUFDLElBQUksQ0FBQztJQTZFbEQsQ0FBQztJQTNFQyxvQ0FBWSxHQUFaO1FBQ0UsT0FBTyx5SEFBWSxDQUNqQixJQUFJLENBQUMsY0FBYyxFQUNuQixJQUFJLENBQUMsY0FBYyxDQUNwQixDQUFDO0lBQ0osQ0FBQztJQVlELHNCQUFZLHlDQUFjO2FBQTFCO1lBQ0UsT0FBTyxVQUFDLE9BQWdDLEVBQUUsTUFBTTtnQkFDOUMsY0FBTyxDQUFDLElBQUksQ0FDVixtSEFBTSxDQUFDLGNBQWMsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxFQUNqQyxzRUFBUSxDQUFDO29CQUNDLGtCQUFjLEdBQU0sTUFBYyxDQUFDLEtBQUssZUFBMUIsQ0FBMkI7b0JBQ2pELElBQU0sUUFBUSxHQUFHLGdLQUFRLENBQUMsV0FBVyxFQUFFLENBQUM7b0JBQ3hDLFFBQVEsQ0FBQyxhQUFhLENBQUM7d0JBQ3JCLEVBQUUsRUFBRSxjQUFjO3dCQUNsQixNQUFNLEVBQUUsR0FBRzt3QkFDWCxNQUFNLEVBQUUsR0FBRzt3QkFDWCxNQUFNLEVBQUUsR0FBRzt3QkFDWCxNQUFNLEVBQUUsR0FBRzt3QkFDWCxNQUFNLEVBQUU7NEJBQ04sWUFBWSxFQUFFLENBQUM7eUJBQ2hCO3dCQUNELE1BQU0sRUFBRTs0QkFDTixJQUFJLEVBQUUsZ0tBQVEsQ0FBQyxZQUFZLENBQUMsV0FBVzs0QkFDdkMsT0FBTyxFQUFFLG1LQUFPLENBQUMsY0FBYyxFQUFFLFFBQVEsRUFBRSxFQUFFLENBQUM7eUJBQy9DO3FCQUNGLENBQUMsQ0FBQztvQkFDSCxPQUFPLGdFQUFFLEVBQUUsQ0FBQztnQkFDZCxDQUFDLENBQUMsRUFDRix3RUFBVSxDQUFDLFVBQUMsS0FBZSxJQUFLLHVFQUFFLEVBQUUsRUFBSixDQUFJLENBQUMsQ0FDVjtZQXRCN0IsQ0FzQjZCLENBQUM7UUFDbEMsQ0FBQzs7O09BQUE7SUFFRCxzQkFBWSx5Q0FBYzthQUExQjtZQUNFLE9BQU8sVUFBQyxPQUFnQyxFQUFFLE1BQU07Z0JBQzlDLGNBQU8sQ0FBQyxJQUFJLENBQ1Ysb0VBQU0sQ0FBQyxVQUFDLE1BQU0sSUFBSyxhQUFNLENBQUMsSUFBSSxLQUFLLGNBQWMsQ0FBQyxRQUFRLEVBQUUsRUFBekMsQ0FBeUMsQ0FBQyxFQUM3RCxzRUFBUSxDQUFDO29CQUNDLFdBQU8sR0FBTSxNQUFjLENBQUMsS0FBSyxRQUExQixDQUEyQjtvQkFDMUMsSUFBTSxRQUFRLEdBQUcsZ0tBQVEsQ0FBQyxXQUFXLEVBQUUsQ0FBQztvQkFDeEMsUUFBUSxDQUFDLFdBQVcsQ0FBQzt3QkFDbkIsRUFBRSxFQUFFLG9CQUFvQjt3QkFDeEIsTUFBTSxFQUFFOzRCQUNOLFFBQVEsRUFBRSxHQUFHO3lCQUNkO3dCQUNELE1BQU0sRUFBRSxVQUFVO3dCQUNsQixNQUFNLEVBQUUsT0FBTzs2QkFDWixNQUFNLENBQUMsVUFBQyxHQUFnRCxJQUFLLFFBQUMsR0FBRyxDQUFDLFVBQVUsSUFBSSxDQUFDLEdBQUcsQ0FBQyxTQUFTLENBQUMsRUFBbEMsQ0FBa0MsQ0FBQzs2QkFDaEcsR0FBRyxDQUNGLFVBQUMsR0FBcUIsSUFBSyxRQUFDOzRCQUMxQixRQUFRLEVBQUUsVUFBVTs0QkFDcEIsSUFBSSxFQUFFLEdBQUcsQ0FBQyxJQUFJOzRCQUNkLEdBQUcsRUFBRSxFQUFFOzRCQUNQLFFBQVEsRUFBRSxHQUFHOzRCQUNiLEtBQUssRUFBRSxtS0FBTyxDQUFTLEdBQUcsRUFBRSxvQkFBb0IsRUFBRSxHQUFHLENBQUM7NEJBQ3RELEtBQUssRUFBRSxtS0FBTyxDQUFTLEdBQUcsRUFBRSx5Q0FBeUMsRUFBRSxFQUFFLENBQUM7eUJBQzNFLENBQUMsRUFQeUIsQ0FPekIsQ0FDSDtxQkFDSixDQUFDLENBQUM7b0JBQ0gsT0FBTyxnRUFBRSxFQUFFLENBQUM7Z0JBQ2QsQ0FBQyxDQUFDLEVBQ0Ysd0VBQVUsQ0FBQyxVQUFDLEtBQWUsSUFBSyx1RUFBRSxFQUFFLEVBQUosQ0FBSSxDQUFDLENBQ1Y7WUEzQjdCLENBMkI2QixDQUFDO1FBQ2xDLENBQUM7OztPQUFBO0lBN0VVLGFBQWE7UUFEekIsb0VBQVU7T0FDRSxhQUFhLENBOEV6QjtJQUFELG9CQUFDO0NBQUE7QUE5RXlCOzs7O0FDbkJRO0FBQzRCO0FBQ3RCO0FBQzBEO0FBR3BEO0FBQ0M7QUFDUTtBQUNOO0FBRy9DLHlCQUFlLEdBQ2IsK0pBQU8sZ0JBRE0sQ0FDTDtBQUtaO0lBQ0UsZUFDUyxZQUEwQixFQUMxQixnQkFBa0MsRUFDbEMsUUFBdUI7UUFGdkIsaUJBQVksR0FBWixZQUFZLENBQWM7UUFDMUIscUJBQWdCLEdBQWhCLGdCQUFnQixDQUFrQjtRQUNsQyxhQUFRLEdBQVIsUUFBUSxDQUFlO0lBQzVCLENBQUM7SUFFTCw0QkFBWSxHQUFaO1FBQ0UsT0FBTyx5SEFBWSxDQUNqQixJQUFJLENBQUMsa0JBQWtCLENBQ3hCLENBQUM7SUFDSixDQUFDO0lBRUQsc0JBQVkscUNBQWtCO2FBQTlCO1lBQ0UsT0FBTyxVQUFDLE9BQU87Z0JBQ2IsY0FBTyxDQUFDLElBQUksQ0FDVixtSEFBTSxDQUFDLHFCQUFlLENBQUMsUUFBUSxFQUFFLENBQUMsRUFDbEMsb0VBQU0sQ0FBQyxVQUFDLE1BQTBDLElBQUssYUFBTSxDQUFDLE9BQU8sS0FBSyxxS0FBYSxDQUFDLElBQUksRUFBckMsQ0FBcUMsQ0FBQyxFQUM3RixzRUFBUSxDQUFDO29CQUNQLElBQUksTUFBTSxFQUFFLE1BQU0sR0FBRyxHQUFHLENBQUM7b0JBQ3pCLFFBQVEsNkpBQUssQ0FBQyxXQUFXLEVBQUUsRUFBRSxDQUFDO3dCQUM1QixLQUFLLGlLQUFTLENBQUMsUUFBUTs0QkFDckIsTUFBTSxHQUFHLEdBQUcsQ0FBQzs0QkFDYixNQUFNLEdBQUcsVUFBVSxDQUFDOzRCQUNwQixNQUFNO3dCQUNSLEtBQUssaUtBQVMsQ0FBQyxNQUFNOzRCQUNuQixNQUFNLEdBQUcsUUFBUSxDQUFDOzRCQUNsQixNQUFNO3dCQUNSOzRCQUVFLE1BQU07b0JBQ1YsQ0FBQztvQkFDRCxnS0FBUSxDQUFDLFdBQVcsRUFBRSxDQUFDLGFBQWEsQ0FBQzt3QkFDbkMsTUFBTSxFQUFFLEdBQUc7d0JBQUUsTUFBTTt3QkFDbkIsTUFBTSxFQUFFLGdCQUFnQjt3QkFDeEIsTUFBTSxFQUFFLG9CQUFvQjt3QkFDNUIsTUFBTSxFQUFFOzRCQUNOLFFBQVEsRUFBRSxNQUFNO3lCQUNqQjtxQkFDRixDQUFDLENBQUM7b0JBQ0gsT0FBTzt3QkFDTCxpQkFBaUIsRUFBRTtxQkFDcEIsQ0FBQztnQkFDSixDQUFDLENBQUMsQ0FBQztZQTVCTCxDQTRCSyxDQUFDO1FBQ1YsQ0FBQzs7O09BQUE7SUE1Q1UsS0FBSztRQURqQixvRUFBVTt5Q0FHYyxZQUFZO1lBQ1IsZ0JBQWdCO1lBQ3hCLGFBQWE7T0FKckIsS0FBSyxDQThDakI7SUFBRCxZQUFDO0NBQUE7QUE5Q2lCOzs7O0FDbEJnRTtBQUN2QjtBQUVuRCxvQkFBZ0IsR0FBSyx3RUFBYyxpQkFBbkIsQ0FBb0I7QUFHNUM7SUFBa0MsZ0NBQWdCO0lBQWxEOztJQU9BLENBQUM7cUJBUFksWUFBWTtJQUVoQiwrQkFBa0IsR0FBekIsVUFBMEIsRUFBVTtRQUNsQyxjQUFZLENBQUMsUUFBUSxHQUFHLGNBQVksQ0FBQyxRQUFRLElBQUksd0VBQWMsQ0FBQyxRQUFRLENBQUMsVUFBVSxDQUFDLHdFQUFjLENBQUMsWUFBWSxDQUFDLENBQUM7UUFDakgsSUFBTSxRQUFRLEdBQVEsY0FBWSxDQUFDLFFBQVEsQ0FBQztRQUM1QyxPQUFPLFFBQVEsQ0FBQyxDQUFDLENBQUMsUUFBUSxDQUFDLGtCQUFrQixDQUFDLG1LQUFXLENBQUMsUUFBUSxFQUFFLEVBQUUsRUFBRSxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQztJQUNoRyxDQUFDOztJQUxNLHFCQUFRLEdBQUcsSUFBSSxDQUFDO0lBRFosWUFBWTtRQUR4QixvRUFBVTtPQUNFLFlBQVksQ0FPeEI7SUFBRCxtQkFBQztDQUFBLENBUGlDLGdCQUFnQixHQU9qRDtBQVB3Qjs7OztBQ05lO0FBQ2M7QUFDTjtBQUN1RDtBQUVqQztBQUVqQztBQUdMO0FBQ2U7QUFDWjtBQUUzQixhQUFTLEdBQW9DLHdFQUFjLFVBQWxELEVBQUUsNkJBQTZCLEdBQUssd0VBQWMsOEJBQW5CLENBQW9CO0FBQzlELFNBSUYsNkJBQTZCLENBQUMsdUJBQU8sQ0FBQyxFQUh4Qyx1QkFBaUIseUJBQ2pCLHdCQUFrQiwwQkFDbEIsMkJBQXFCLDJCQUNtQixDQUFDO0FBRzNDO0lBQTJCLHlCQUFzQjtJQUMvQyxlQUFvQixNQUFjLEVBQUUsS0FBZ0IsRUFBVSxLQUFZLEVBQVUsWUFBMEI7UUFDNUcsa0JBQUssWUFBQyxLQUFLLENBQUMsU0FBQztRQURLLFlBQU0sR0FBTixNQUFNLENBQVE7UUFBNEIsV0FBSyxHQUFMLEtBQUssQ0FBTztRQUFVLGtCQUFZLEdBQVosWUFBWSxDQUFjOztJQUU5RyxDQUFDO0lBRUQsc0JBQUksMEJBQU87YUFBWDs7WUFDRSxPQUFPLGlGQUFlLENBQUMsd0NBRWxCLGdLQUFRLENBQUMsbUJBQW1CLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBUSxHQUN0RCxnS0FBUSxDQUFDLGdCQUFnQixFQUFTLEdBQ2xDLGdLQUFRLENBQUMsa0JBQWtCLEVBQVMsS0FFdkMsY0FBYyxFQUFFLDhHQUFhO29CQUMzQixHQUFDLHVCQUFpQixJQUFHLFVBQUMsS0FBSyxFQUFFLEVBQTJDOzRCQUF6QyxPQUFPO3dCQUF1QyxjQUFPLElBQUksS0FBSztvQkFBaEIsQ0FBZ0I7eUJBQzVGLENBQUMsRUFBRSxDQUEyQixDQUFDLEVBQ2xDLE9BQU8sRUFBRSw4R0FBYTtvQkFDcEIsR0FBQyx3QkFBa0IsSUFBRyxVQUFDLEtBQUssRUFBRSxFQUFvQzs0QkFBbEMsT0FBTzt3QkFBZ0MsY0FBTyxJQUFJLEtBQUs7b0JBQWhCLENBQWdCO29CQUN2RixHQUFDLDJCQUFxQixJQUFHLFVBQUMsS0FBSyxFQUFFLEVBQW9DOzRCQUFsQyxPQUFPO3dCQUFnQyxjQUFPLElBQUksS0FBSztvQkFBaEIsQ0FBZ0I7eUJBQ3pGLEVBQUUsQ0FBQyxJQUNDLENBQUM7UUFDWixDQUFDOzs7T0FBQTtJQVNELHNCQUFJLDhCQUFXO2FBQWY7WUFDRSxPQUFPLHlIQUFZLENBQUMsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsWUFBWSxFQUFFLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxnQkFBZ0IsQ0FBQyxZQUFZLEVBQUUsRUFDaEcsSUFBSSxDQUFDLEtBQUssQ0FBQyxZQUFZLENBQUMsWUFBWSxFQUFFLEVBQUUsSUFBSSxDQUFDLEtBQUssQ0FBQyxZQUFZLEVBQUUsRUFBRSxJQUFJLGtLQUFVLEVBQUUsQ0FBQyxZQUFZLEVBQUUsRUFDbEcsSUFBSSx5S0FBaUIsQ0FBQyxJQUFJLENBQUMsTUFBTSxFQUFFLDRCQUE0QixDQUFDLENBQUMsWUFBWSxFQUFFLEVBQUUsSUFBSSxzS0FBYyxFQUFFLENBQUMsWUFBWSxFQUFFLENBQUMsQ0FBQztRQUMxSCxDQUFDOzs7T0FBQTtJQWpDVSxLQUFLO1FBRGpCLG9FQUFVO3lDQUVtQixNQUFNLEVBQVMsK0RBQVMsRUFBaUIsS0FBSyxFQUF3QixZQUFZO09BRG5HLEtBQUssQ0FrQ2pCO0lBQUQsWUFBQztDQUFBLENBbEMwQixTQUFTLEdBa0NuQztBQWxDaUI7OztBQ3RCTTtBQUNFOzs7O0FDRFk7QUFFaUI7QUFHL0MsWUFBUSxHQUFLLHdFQUFjLFNBQW5CLENBQW9CO0FBVXBDO0lBQTBCLHdCQUFRO0lBa0JoQyxjQUFZLEdBQVE7UUFDbEIsa0JBQUssWUFBQyxHQUFHLENBQUMsU0FBQztRQUNYLElBQUksQ0FBQyxRQUFRLEdBQUcsS0FBSSxDQUFDOztJQUN2QixDQUFDO0lBcEJNLGtCQUFhLEdBQXBCLFVBQXFCLEtBQVk7O1FBQy9CO1lBQ0UsR0FBQywrSkFBTyxDQUFDLFVBQVUsQ0FBQyxRQUFRLEVBQUUsSUFBRztnQkFDL0IsS0FBSyxDQUFDLFFBQVEsQ0FBQywrSkFBTyxDQUFDLGNBQWMsRUFBRSxDQUFDLENBQUM7Z0JBQ3pDLCtKQUFPLENBQUMsZUFBZSxDQUFDLCtKQUFPLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FBQztZQUNwRCxDQUFDO1lBQ0QsR0FBQywrSkFBTyxDQUFDLGNBQWMsQ0FBQyxRQUFRLEVBQUUsSUFBRztnQkFDbkMsK0pBQU8sQ0FBQyxlQUFlLENBQUMsK0pBQU8sQ0FBQyxjQUFjLEVBQUUsQ0FBQyxDQUFDO1lBQ3BELENBQUM7ZUFDRDtJQUNKLENBQUM7SUFXSCxXQUFDO0FBQUQsQ0FBQyxDQXRCeUIsUUFBUSxHQXNCakM7Ozs7Ozs7Ozs7QUNyQzhCO0FBQ087QUFDdUI7QUFDZ0I7QUFPN0UsSUFBTSxTQUFTLEdBQThCLFVBQUMsRUFBa0I7UUFBaEIsY0FBYztJQUN0RCxnQkFBMEIsc0VBQWMsQ0FBQyxLQUFLLENBQUMsTUFBOUMsUUFBUSxVQUFFLFdBQVcsUUFBeUIsQ0FBQztJQUV0RCx1RUFBZSxDQUFDO1FBRWQsSUFBSSxRQUFRLEVBQUUsQ0FBQztZQUNiLGdLQUFRLENBQUMsV0FBVyxFQUFFLENBQUMsV0FBVyxDQUFDO2dCQUNqQyxFQUFFLEVBQUUsdUJBQXVCO2dCQUMzQixNQUFNLEVBQUU7b0JBQ04sUUFBUSxFQUFFLEdBQUc7aUJBQ2Q7Z0JBQ0QsTUFBTSxFQUFFLGtDQUFrQzthQUMzQyxDQUFDLENBQUM7UUFDTCxDQUFDO0lBQ0gsQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztJQUVmLElBQU0sWUFBWSxHQUFHLFFBQVEsQ0FBQyxDQUFDLENBQUMsZUFBZSxDQUFDLENBQUMsQ0FBQyxhQUFhLENBQUM7SUFDaEUsT0FBTyxpQ0FBUyxTQUFTLEVBQUMsbURBQW1ELFlBQzNFLDZCQUFLLFNBQVMsRUFBQyx1Q0FBdUMsWUFDcEQsOEJBQUssU0FBUyxFQUFDLGtEQUFrRCxhQUMvRCw2QkFBSyxTQUFTLEVBQUMsMENBQTBDLFlBQ3ZELDRCQUFHLEVBQUUsRUFBQyx1QkFBdUIsRUFBQyxJQUFJLEVBQUMsb0JBQW9CLEVBQUMsT0FBTyxFQUFFLGNBQU0sa0JBQVcsQ0FBQyxDQUFDLFFBQVEsQ0FBQyxFQUF0QixDQUFzQixtQkFBZ0IsaUJBQWlCLEVBQzVILFNBQVMsRUFBQywwSEFBMEgsbUJBQ3JILFFBQVEsRUFBRSxJQUFJLEVBQUMsUUFBUSxhQUN0Qyw4QkFBTSxTQUFTLEVBQUMseUJBQXlCLGVBQVcsUUFBUSxpQkFBYSxNQUFNLGlCQUNqRSxNQUFNLFlBQUMsb0JBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUMsVUFBVSxDQUFDLENBQUMsQ0FBQyxRQUFRLEdBQUksR0FBTyxFQUN0RiwrQkFBTSxTQUFTLEVBQUUsVUFBRyxZQUFZLHlDQUFzQyxpQkFDeEQsTUFBTSxhQUNsQiw4QkFBTSxTQUFTLEVBQUUsNEJBQXFCLFlBQVksQ0FBRSxHQUFJLEVBQ3hELDhCQUFNLFNBQVMsRUFBRSw0QkFBcUIsWUFBWSxDQUFFLEdBQUksSUFDbkQsRUFDUCw4QkFBSyxTQUFTLEVBQUMsd0JBQXdCLGFBQ3JDLDhCQUFNLFNBQVMsRUFBQyw0QkFBNEIsWUFBQyxvQkFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUMsa0NBQWtDLEdBQUcsR0FBTyxFQUM5Ryw4QkFBTSxTQUFTLEVBQUMseUNBQXlDLEVBQUMsS0FBSyxFQUFFLEVBQUUsT0FBTyxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxTQUFTLEVBQUUsWUFBRSxvQkFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUMsd0JBQXdCLEdBQUcsR0FBTyxJQUNoSyxJQUNKLEdBQ0EsRUFDTiw2QkFBSyxFQUFFLEVBQUMsaUJBQWlCLEVBQ3ZCLFNBQVMsRUFBQyxvR0FBb0csRUFDOUcsS0FBSyxFQUFFLEVBQUUsT0FBTyxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxNQUFNLEVBQUUsWUFDL0MsNkJBQUssU0FBUyxFQUFDLGlCQUFpQixZQUU1QixjQUFjLENBQUMsR0FBRyxDQUFDLFVBQUMsRUFJbkI7b0NBSEMsSUFBSSxZQUNKLFlBQVksb0JBQ1osZ0JBQWdCO2dDQUVoQixxQ0FBSyxTQUFTLEVBQUMsVUFBVSxhQUN2Qiw2QkFBSyxTQUFTLEVBQUMsVUFBVSxpQkFBYSxNQUFNLEdBQUcsRUFDL0MsOEJBQUssU0FBUyxFQUFDLGlCQUFpQixhQUM5Qiw2QkFBSyxTQUFTLEVBQUMsVUFBVSxZQUFFLElBQUksR0FBTyxFQUN0Qyw4QkFBSyxLQUFLLEVBQUUsRUFBRSxVQUFVLEVBQUUsUUFBUSxFQUFFLGFBQUUsb0JBQUMsa0tBQVUsQ0FBQyxZQUFZLElBQUMsS0FBSyxFQUFFLG1LQUFPLENBQUMsWUFBWSxFQUFFLE9BQU8sRUFBRSxDQUFDLENBQUMsR0FBSSxzQkFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUMsUUFBUSxHQUFHLElBQU0sSUFDN0ksRUFDTixxQkFBQyxrS0FBVSxDQUFDLE9BQU8sSUFBQyxJQUFJLEVBQUUsQ0FBQyxDQUFDLGdCQUFnQixhQUMxQyw2QkFBSyxTQUFTLEVBQUMsU0FBUyxpQkFBYSxNQUFNLEdBQUcsRUFDOUMsb0JBQUMsa0tBQVUsQ0FBQyxPQUFPLElBQUMsSUFBSSxFQUFFLG1LQUFPLENBQUMsZ0JBQWdCLEVBQUUsYUFBYSxFQUFFLEtBQUssQ0FBQyxZQUN2RSw4QkFBSyxTQUFTLEVBQUMsU0FBUyxhQUN0Qiw2QkFBSyxTQUFTLEVBQUMsVUFBVSxZQUFFLG1LQUFPLENBQUMsZ0JBQWdCLEVBQUUsYUFBYSxFQUFFLEVBQUUsQ0FBQyxHQUFPLEVBQzlFLHlDQUFLLG9CQUFDLGtLQUFVLENBQUMsWUFBWSxJQUFDLEtBQUssRUFBRSxtS0FBTyxDQUFDLGdCQUFnQixFQUFFLHdCQUF3QixFQUFFLENBQUMsQ0FBQyxHQUFJLHNCQUFDLGlHQUFnQixJQUFDLEVBQUUsRUFBQyxRQUFRLEdBQUcsSUFBTSxJQUNqSSxHQUNhLEVBQ3JCLG9CQUFDLGtLQUFVLENBQUMsT0FBTyxJQUFDLElBQUksRUFBRSxtS0FBTyxDQUFDLGdCQUFnQixFQUFFLFlBQVksRUFBRSxLQUFLLENBQUMsWUFDdEUsdUNBQ0Usb0JBQUMsOEZBQWEsSUFBQyxLQUFLLEVBQUUsbUtBQU8sQ0FBQyxnQkFBZ0IsRUFBRSxZQUFZLEVBQUUsRUFBRSxDQUFDLEVBQUUsTUFBTSxFQUFDLFFBQVEsRUFBQyxRQUFRLEVBQUMsS0FBSyxZQUM5RixVQUFDLFVBQVUsSUFBSywyQkFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUMsa0JBQWtCLEVBQUMsTUFBTSxFQUFFLEVBQUUsVUFBVSxjQUFFLEdBQUksRUFBbEUsQ0FBa0UsR0FDckUsR0FDWixHQUNhLElBQ0YsSUFDakI7NEJBdEJOLENBc0JNLENBQUMsR0FFUCxHQUNGLElBQ0YsR0FDRixHQUNFLENBQUM7QUFDYixDQUFDLENBQUM7QUFFSyxJQUFNLE1BQU0sR0FBRyxnR0FBTyxDQUMzQixVQUFDLEVBQStCO1FBQTdCLGNBQWM7SUFBb0IsUUFBQyxFQUFFLGNBQWMsRUFBRSxjQUFjLElBQUksRUFBRSxFQUFFLENBQUM7QUFBMUMsQ0FBMEMsQ0FDaEYsQ0FBQyxTQUFTLENBQUMsQ0FBQzs7Ozs7QUMxRjREO0FBQzFDO0FBQzhCO0FBQ3ZCO0FBRWU7QUFNbkQsV0FBTyxHQUVMLGtLQUFVLFFBRkwsRUFDUCxRQUFRLEdBQ04sa0tBQVUsU0FESixDQUNLO0FBb0JmLElBQU0saUJBQVMsR0FBOEMsVUFBQyxFQWdCN0Q7UUFmQyxFQUFFLFVBQ0YsSUFBSSxZQUNKLGdCQUFnQix3QkFDaEIsU0FBUyxpQkFLVCxZQUFZLG9CQUNaLFVBQVUsa0JBRVYsWUFBWSxvQkFDWixnQkFBZ0Isd0JBQ2hCLGNBQWMsc0JBQ2QsZ0JBQWdCO0lBRWhCLElBQU0sZUFBZSxHQUFHLFVBQUMsQ0FBTTtRQUM3QixDQUFDLENBQUMsZUFBZSxFQUFFLENBQUM7UUFDcEIsQ0FBQyxDQUFDLGNBQWMsRUFBRSxDQUFDO1FBQ25CLElBQUksVUFBVTtZQUFFLE9BQU87UUFDdkIsSUFBSSxDQUFDLENBQUMsT0FBTyxLQUFLLFNBQVMsSUFBSSxDQUFDLENBQUMsT0FBTyxLQUFLLEVBQUUsSUFBSSxDQUFDLENBQUMsT0FBTyxLQUFLLEVBQUUsRUFBRSxDQUFDO1lBQ3BFLGdCQUFnQixDQUFDLGNBQWMsQ0FBQyxDQUFDO1FBQ25DLENBQUM7SUFDSCxDQUFDLENBQUM7SUFDSSxnQkFBaUMsc0VBQWMsQ0FBQyxLQUFLLENBQUMsTUFBckQsY0FBYyxVQUFFLFlBQVksUUFBeUIsQ0FBQztJQUM3RCxJQUFNLGFBQWEsR0FBRyxVQUFDLENBQU07UUFDM0IsSUFBSSxDQUFDLENBQUMsQ0FBQyxPQUFPLEtBQUssU0FBUyxJQUFJLENBQUMsQ0FBQyxPQUFPLEtBQUssRUFBRSxDQUFDLElBQUksQ0FBQyxDQUFDLE1BQU0sQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLGNBQWMsQ0FBQyxFQUFFLENBQUM7WUFDakcsWUFBWSxDQUFDLENBQUMsY0FBYyxDQUFDLENBQUM7UUFDaEMsQ0FBQztJQUNILENBQUMsQ0FBQztJQUNGLHVFQUFlLENBQUM7UUFDZCxDQUFDLENBQUMsR0FBRyxHQUFHLEVBQUUsQ0FBQzthQUNSLElBQUksQ0FBQyxlQUFlLENBQUM7YUFDckIsUUFBUSxDQUFDLGtGQUFrRixDQUFDO2FBQzVGLElBQUksQ0FBQyxVQUFVLEVBQUUsR0FBRyxDQUFDO2FBQ3JCLElBQUksRUFBRTthQUNOLFFBQVEsQ0FBQyxjQUFjLENBQUM7YUFDeEIsVUFBVSxDQUFDLElBQUksQ0FBQyxDQUFDO0lBQ3RCLENBQUMsQ0FBQyxDQUFDO0lBRUgsT0FBTyw2QkFBSyxFQUFFLEVBQUUsRUFBRSxFQUFFLFNBQVMsRUFBRSx1REFBZ0QsVUFBVSxDQUFDLENBQUMsQ0FBQyxVQUFVLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBRSxZQUMzRyw4QkFBSyxTQUFTLEVBQUMsc0hBQXNILGFBQ25JLDZCQUFLLFNBQVMsRUFBQyxjQUFjLFlBQzNCLCtCQUFNLEVBQUUsRUFBRSxjQUFPLEVBQUUsQ0FBRSxFQUFFLFNBQVMsRUFBQyxzQ0FBc0MsRUFBQyxPQUFPLEVBQUUsZUFBZSxhQUM5RiwrQkFBTyxFQUFFLEVBQUUsY0FBTyxFQUFFLENBQUUsRUFBRSxJQUFJLEVBQUMsaUJBQWlCLEVBQUMsT0FBTyxFQUFFLFVBQVUsRUFBRSxJQUFJLEVBQUMsT0FBTyxxQkFBa0Isc0JBQWUsRUFBRSxDQUFFLHNCQUFvQiwyQkFBb0IsRUFBRSxDQUFFLGtCQUFnQixVQUFVLEVBQUUsU0FBUyxFQUFDLDhCQUE4QixHQUFHLEVBQ3hPLDhCQUFNLFNBQVMsRUFBQywwREFBMEQsR0FBRyxJQUN4RSxHQUNILEVBQ04sOEJBQUssU0FBUyxFQUFDLG1CQUFtQixhQUNoQyw2QkFBSyxFQUFFLEVBQUUsc0JBQWUsRUFBRSxDQUFFLEVBQUUsU0FBUyxFQUFDLDREQUE0RCxFQUFDLE9BQU8sRUFBRSxlQUFlLFlBQzNILDRCQUFJLFNBQVMsRUFBQyx3REFBd0QsWUFBRSxJQUFJLEdBQU0sR0FDOUUsRUFDTiw2QkFBSyxTQUFTLEVBQUMsNENBQTRDLGlCQUFhLE1BQU0sR0FBRyxFQUNqRiw2QkFBSyxTQUFTLEVBQUMsZ0JBQWdCLGlCQUFhLE1BQU0sR0FBRyxFQUNyRCw2QkFBSyxTQUFTLEVBQUMsNENBQTRDLGlCQUFhLE1BQU0sR0FBRyxFQUNqRiw2QkFBSyxTQUFTLEVBQUMsbUJBQW1CLGlCQUFhLE1BQU0sR0FBRyxFQUN4RCw2QkFBSyxTQUFTLEVBQUMsK0NBQStDLEVBQUMsRUFBRSxFQUFFLDJCQUFvQixFQUFFLENBQUUsWUFDekYsOEJBQUssU0FBUyxFQUFDLHlCQUF5QixhQUN0Qyw0QkFBSSxFQUFFLEVBQUUscUJBQWMsRUFBRSxDQUFFLEVBQUUsU0FBUyxFQUFFLGdFQUF5RCxjQUFjLENBQUMsQ0FBQyxDQUFDLFVBQVUsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFFLEVBQUUsT0FBTyxFQUFFLGFBQWEsRUFBRSxPQUFPLEVBQUUsYUFBYSxFQUFFLHVCQUF1QixFQUFFLEVBQUUsTUFBTSxFQUFFLGdCQUFnQixFQUFFLEdBQUksRUFDN08sNEJBQUksU0FBUyxFQUFDLCtCQUErQixFQUFDLHVCQUF1QixFQUFFLEVBQUUsTUFBTSxFQUFFLFNBQVMsRUFBRSxHQUFJLEVBQ2hHLDZCQUFLLFNBQVMsRUFBQyxZQUFZLFlBQ3pCLDhCQUFLLFNBQVMsRUFBQyx5Q0FBeUMsYUFDdEQsb0JBQUMsT0FBTyxJQUFDLElBQUksRUFBRSxtS0FBTyxDQUFDLGdCQUFnQixFQUFFLFlBQVksRUFBRSxLQUFLLENBQUMsWUFDM0QsOEJBQU0sU0FBUyxFQUFDLGdFQUFnRSxZQUM5RSxvQkFBQyw4RkFBYSxJQUFDLEtBQUssRUFBRSxtS0FBTyxDQUFDLGdCQUFnQixFQUFFLFlBQVksRUFBRSxFQUFFLENBQUMsRUFBRSxNQUFNLEVBQUMsUUFBUSxFQUFDLFFBQVEsRUFBQyxLQUFLLFlBRTdGLFVBQUMsVUFBVSxJQUFLLDJCQUFDLGlHQUFnQixJQUFDLEVBQUUsRUFBQyw2QkFBNkIsRUFBQyxNQUFNLEVBQUUsRUFBRSxVQUFVLGNBQUUsR0FBSSxFQUE3RSxDQUE2RSxHQUVqRixHQUNYLEdBQ0MsRUFDVixvQkFBQyxPQUFPLElBQUMsSUFBSSxFQUFFLG1LQUFPLENBQUMsZ0JBQWdCLEVBQUUsa0JBQWtCLEVBQUUsS0FBSyxDQUFDLFlBQ2pFLDhCQUFNLFNBQVMsRUFBQyxnRUFBZ0UsWUFDOUUsb0JBQUMsaUdBQWdCLElBQUMsRUFBRSxFQUFDLHlCQUF5QixFQUFDLE1BQU0sRUFBRSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsR0FBRyxDQUFDLG1LQUFPLENBQUMsZ0JBQWdCLEVBQUUscUJBQXFCLEVBQUUsQ0FBQyxDQUFDLENBQUMsRUFBRSxRQUFRLEVBQUUsbUtBQU8sQ0FBQyxnQkFBZ0IsRUFBRSxrQkFBa0IsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFJLEdBQzNMLEdBQ0MsRUFDVixvQkFBQyxPQUFPLElBQUMsSUFBSSxFQUFFLG1LQUFPLENBQUMsQ0FBQyxnQkFBZ0IsRUFBRSxTQUFTLEVBQUUsS0FBSyxDQUFDLFlBQ3pELDZCQUFLLFNBQVMsRUFBQyx5QkFBeUIsaUJBQWEsTUFBTSxHQUFHLEdBQ3RELEVBQ1YsOEJBQUssU0FBUyxFQUFDLDREQUE0RCxhQUN6RSxxQkFBQyxPQUFPLElBQUMsSUFBSSxFQUFFLG1LQUFPLENBQUMsZ0JBQWdCLEVBQUUsU0FBUyxFQUFFLEtBQUssQ0FBQyxhQUN4RCxvQkFBQyxpR0FBZ0IsSUFBQyxFQUFFLEVBQUMsS0FBSyxHQUFHLGNBQ3JCLEVBQ1Ysb0JBQUMsUUFBUSxJQUFDLEtBQUssRUFDYixtS0FBTyxDQUFDLGdCQUFnQixFQUFFLHlCQUF5QixFQUFFLEtBQUssQ0FBQyxLQUFLLEtBQUs7Z0VBQ25FLENBQUMsQ0FBQyxtS0FBTyxDQUFDLFlBQVksRUFBRSxPQUFPLEVBQUUsQ0FBQyxDQUFDO2dFQUNuQyxDQUFDLENBQUMsbUtBQU8sQ0FBQyxnQkFBZ0IsRUFBRSx3QkFBd0IsRUFBRSxDQUFDLENBQUMsRUFDMUQsT0FBTyxFQUFFLElBQUksR0FBSSxFQUNuQixvQkFBQyxPQUFPLElBQUMsSUFBSSxFQUFFLG1LQUFPLENBQUMsZ0JBQWdCLEVBQUUsU0FBUyxFQUFFLEtBQUssQ0FBQyxZQUN4RCwyQkFBRyxTQUFTLEVBQUMsaURBQWlELFlBQzVELG9CQUFDLGlHQUFnQixJQUFDLEVBQUUsRUFBQyxlQUFlLEVBQUMsTUFBTSxFQUFFLG1LQUFPLENBQUMsWUFBWSxFQUFFLFNBQVMsRUFBRSxFQUFFLENBQVEsR0FBSSxHQUMxRixHQUNJLElBQ04sRUFDTixvQkFBQyxPQUFPLElBQUMsSUFBSSxFQUFFLG1LQUFPLENBQUMsZ0JBQWdCLEVBQUUsU0FBUyxFQUFFLEtBQUssQ0FBQyxZQUN4RCwyQkFBRyxTQUFTLEVBQUMsb0RBQW9ELFlBQzlELG1LQUFPLENBQUMsZ0JBQWdCLEVBQUUsY0FBYyxFQUFFLG9CQUFDLGlHQUFnQixJQUFDLEVBQUUsRUFBQywyQkFBMkIsR0FBRyxDQUFDLEdBQzdGLEdBQ0ksSUFDTixHQUNGLElBQ0YsR0FDRixJQUNGLElBQ0YsR0FDRixDQUFDO0FBQ1QsQ0FBQyxDQUFDO0FBR0ssSUFBTSxPQUFPLEdBQUcsZ0dBQU8sQ0FDNUIsVUFBQyxFQUFnQixJQUFLLFFBQUMsRUFBRSxDQUFDLEVBQUosQ0FBSSxFQUMxQixrQkFBUSxJQUFJLFFBQUM7SUFDWCxnQkFBZ0IsRUFBRSxVQUFDLE1BQThCLElBQUssZUFBUSxDQUFDLHNCQUFzQixDQUFDLE1BQU0sQ0FBQyxDQUFDLEVBQXhDLENBQXdDO0NBQy9GLENBQUMsRUFGVSxDQUVWLENBQ0gsQ0FBQyxpQkFBUyxDQUFDLENBQUM7Ozs7O0FDcEo2RTtBQUMzRDtBQUNlO0FBSXZDLElBQU0sTUFBTSxHQUE4QjtJQUN6QyxnQkFBMEIsc0VBQWMsQ0FBQyxLQUFLLENBQUMsTUFBOUMsUUFBUSxVQUFFLFdBQVcsUUFBeUIsQ0FBQztJQUN0RCx1RUFBZSxDQUFDO1FBQ2QsUUFBUTtZQUNBLGdLQUFRLENBQUMsV0FBVyxFQUFFLENBQUMsV0FBVyxDQUFDO2dCQUNqQyxFQUFFLEVBQUUsaUJBQWlCO2dCQUNyQixNQUFNLEVBQUU7b0JBQ04sUUFBUSxFQUFFLEdBQUc7aUJBQ2Q7Z0JBQ0QsTUFBTSxFQUFFLGFBQWE7YUFDdEIsQ0FBQyxDQUFDO0lBQ2IsQ0FBQyxFQUFFLENBQUMsUUFBUSxDQUFDLENBQUMsQ0FBQztJQUNmLE9BQU8sOEJBQUssU0FBUyxFQUFDLDhCQUE4QixFQUFDLEVBQUUsRUFBQyxVQUFVLGFBQ2hFLGlDQUFRLEVBQUUsRUFBQyxhQUFhLEVBQUMsU0FBUyxFQUFDLDZHQUE2RyxFQUFDLE9BQU8sRUFBRSxjQUFNLGtCQUFXLENBQUMsQ0FBQyxRQUFRLENBQUMsRUFBdEIsQ0FBc0IsbUJBQWlCLFFBQVEsYUFDN00sOEJBQU0sU0FBUyxFQUFFLG9CQUFhLFFBQVEsQ0FBQyxDQUFDLENBQUMsaUJBQWlCLENBQUMsQ0FBQyxDQUFDLGVBQWUsQ0FBRSxpQkFBYyxNQUFNLEdBQUcsa0JBQ3JHLG9CQUFDLGlHQUFnQixJQUFDLEVBQUUsRUFBQyxtQkFBbUIsR0FBRyxJQUNwQyxFQUNULDZCQUFLLFNBQVMsRUFBQyxVQUFVLGlCQUFhLE1BQU0sR0FBRyxFQUMvQyxvQkFBQyxrS0FBVSxDQUFDLE9BQU8sSUFBQyxJQUFJLEVBQUUsUUFBUSxZQUNoQyw4QkFBSyxTQUFTLEVBQUMsc0dBQXNHLGFBQ25ILGdDQUFRLEVBQUUsRUFBQyxnQkFBZ0IsRUFBQyxJQUFJLEVBQUMsUUFBUSxFQUFDLE9BQU8sRUFBRSxjQUFNLGtCQUFXLENBQUMsS0FBSyxDQUFDLEVBQWxCLENBQWtCLEVBQUUsU0FBUyxFQUFDLDBEQUEwRCxnQkFBWSxPQUFPLFlBQ2xLLDhCQUFNLFNBQVMsRUFBQyx3QkFBd0IsaUJBQWEsTUFBTSxHQUFHLEdBQ3ZELEVBQ1Qsb0JBQUMsNEtBQW9CLElBQUMsRUFBRSxFQUFDLGNBQWMsR0FBRyxJQUN0QyxHQUNhLElBQ2pCLENBQUM7QUFDVCxDQUFDLENBQUM7OztBQ2pDMkQ7QUFFdEQsU0FBUyxxQkFBcUIsQ0FBQyxlQUE0QztJQUNoRixPQUFPLG1LQUFPLENBQThCLGVBQWUsRUFBRSxTQUFTLEVBQUUsRUFBRSxDQUFDLENBQUMsTUFBTSxDQUNoRixVQUFDLElBQUksRUFBRSxjQUFjO1FBQ25CLElBQUksY0FBYyxDQUFDLElBQUksRUFBRSxDQUFDO1lBQ3hCLElBQUksQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLEdBQUksY0FBYyxDQUFDLEtBQUssQ0FBQztRQUNwRCxDQUFDO1FBQ0QsT0FBTyxJQUFJLENBQUM7SUFDZCxDQUFDLEVBQUUsRUFBUyxDQUNiLENBQUM7QUFDSixDQUFDOzs7OztBQ1ZxQztBQUNpQjtBQUVuQjtBQUNIO0FBQ21DO0FBTXBFLElBQU0saUJBQVMsR0FBOEIsVUFBQyxFQUU3QztRQURDLE9BQU87SUFDSCxxQ0FBSyxTQUFTLEVBQUMsc0NBQXNDLEVBQUMsSUFBSSxFQUFDLFlBQVksYUFDM0UseUNBRUksNm5FQXVEWSxHQUVSLEVBR04sT0FBTyxDQUFDLE1BQU0sQ0FBQyxhQUFHLElBQUksUUFBQyxHQUFHLENBQUMsU0FBUyxFQUFkLENBQWMsQ0FBQztpQkFDbEMsSUFBSSxDQUNILFVBQUMsQ0FBQyxFQUFFLENBQUMsSUFBSyxRQUNSLG1LQUFPLENBQVMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxFQUFFLGNBQWMsRUFBRSxDQUFDLENBQUM7Z0JBQzlELG1LQUFPLENBQVMscUJBQXFCLENBQUMsQ0FBQyxDQUFDLGVBQWUsQ0FBQyxFQUFFLGNBQWMsRUFBRSxDQUFDLENBQUMsQ0FDM0YsRUFIUyxDQUdULENBQ0Y7aUJBQ0EsR0FBRyxDQUNGLHlCQUFlLElBQUksMkJBQUMsT0FBTyxlQUFLLGVBQWUsRUFBSSxFQUFoQyxDQUFnQyxDQUNwRCxFQUVMLG9CQUFDLE1BQU0sS0FBRyxJQUNOO0FBM0VBLENBMkVBLENBQUM7QUFHQSxJQUFNLE9BQU8sR0FBRyxnR0FBTyxDQUM1QixVQUFDLEVBQXdCO1FBQXRCLE9BQU87SUFBb0IsUUFBQyxFQUFFLE9BQU8sV0FBRSxDQUFDO0FBQWIsQ0FBYSxDQUM1QyxDQUFDLGlCQUFTLENBQUMsQ0FBQzs7Ozs7QUM5RmtCO0FBQ087QUFDMEM7QUFFOUM7QUFDRTtBQUdsQyxvQkFBZ0IsR0FDZCxrS0FBVSxpQkFESSxDQUNIO0FBR2Isc0JBQVksR0FFViwrSkFBTyxhQUZHLEVBQ1osb0JBQW9CLEdBQ2xCLCtKQUFPLHFCQURXLENBQ1Y7QUFVWjtJQUF3Qiw2QkFBdUQ7SUFBL0U7O0lBaUJBLENBQUM7SUFoQkMscUNBQWlCLEdBQWpCLFVBQWtCLEdBQVE7UUFDeEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxrQkFBa0IsQ0FBQyxHQUFHLENBQUMsQ0FBQztJQUNyQyxDQUFDO0lBRUQscUNBQWlCLEdBQWpCO1FBQ0UsSUFBSSxDQUFDLEtBQUssQ0FBQyxvQkFBb0IsQ0FBQyxtS0FBVyxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ3hELENBQUM7SUFFRCwwQkFBTSxHQUFOO1FBQ0UsT0FBTywrQkFBTSxFQUFFLEVBQUMsYUFBYSxhQUMzQixvQkFBQyxNQUFNLEtBQUcsRUFDViw2QkFBSyxTQUFTLEVBQUMsVUFBVSxpQkFBYSxNQUFNLEdBQUcsRUFDL0Msb0JBQUMsT0FBTyxLQUFHLEVBQ1gsb0JBQUMsZ0JBQWdCLElBQUMsRUFBRSxFQUFDLDRCQUE0QixHQUFHLElBQy9DLENBQUM7SUFDVixDQUFDO0lBQ0gsZ0JBQUM7QUFBRCxDQUFDLENBakJ1Qix1RUFBZSxHQWlCdEM7QUFFTSxJQUFNLFdBQVcsR0FBRyxnR0FBTyxDQUNoQyxVQUFDLEVBQWlCLElBQUssUUFBQyxFQUFJLENBQUMsRUFBTixDQUFNLEVBQzdCLFVBQUMsUUFBUSxJQUFLLFFBQUM7SUFDYixrQkFBa0IsRUFBRSxVQUFDLEtBQVUsSUFBSyxlQUFRLENBQUMsa0JBQVksQ0FBQyxLQUFLLENBQUMsQ0FBQyxFQUE3QixDQUE2QjtJQUNqRSxvQkFBb0IsRUFBRSxjQUFNLGVBQVEsQ0FBQyxvQkFBb0IsRUFBRSxDQUFDLEVBQWhDLENBQWdDO0NBQzdELENBQUMsRUFIWSxDQUdaLENBQ0gsQ0FBQyxlQUFTLENBQUMsQ0FBQzs7OztBQ2hENkM7QUFDcEI7QUFHcEMsbUJBQWUsR0FDYixrS0FBVSxnQkFERyxDQUNGO0FBRVIsSUFBTSxHQUFHLEdBQUcsY0FBTSwyQkFBQyxlQUFlLGNBQ3ZDLG9CQUFDLFdBQVcsS0FBRyxHQUNDLEVBRk8sQ0FFUCxDQUFDOzs7OztBQ1RxQztBQUMrQjtBQUM3QjtBQUMxQjtBQUVGO0FBQ0k7QUFDTjtBQUkxQixrQkFBYyxHQUVaLCtKQUFPLGVBRkssRUFDZCxzQkFBZSxHQUNiLCtKQUFPLGdCQURNLENBQ0w7QUFHWjtJQUE2QyxtQ0FBVTtJQUNyRCx5QkFBb0IsS0FBWSxFQUFVLE1BQXlDLEVBQVUsTUFBYyxFQUFVLElBQVU7UUFDN0gsa0JBQUssV0FBRSxTQUFDO1FBRFUsV0FBSyxHQUFMLEtBQUssQ0FBTztRQUFVLFlBQU0sR0FBTixNQUFNLENBQW1DO1FBQVUsWUFBTSxHQUFOLE1BQU0sQ0FBUTtRQUFVLFVBQUksR0FBSixJQUFJLENBQU07O0lBRS9ILENBQUM7SUFRRCw4QkFBSSxHQUFKO1FBQ0UsSUFBSSxDQUFDLElBQUksQ0FBQyxTQUFTLENBQUMsSUFBSSxDQUFDLGFBQWEsQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztRQUNwRCxJQUFJLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxjQUFjLENBQUMsSUFBSSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUM7UUFDakQsSUFBSSxDQUFDLEtBQUssQ0FBQyxRQUFRLENBQUMsY0FBYyxDQUFDLElBQUksQ0FBQyxNQUFNLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQztRQUN2RCxJQUFJLENBQUMsS0FBSyxDQUFDLFFBQVEsQ0FBQyxzQkFBZSxDQUFDLHFLQUFhLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQztJQUMzRCxDQUFDO0lBT0QsaUNBQU8sR0FBUDtRQUNFLElBQUksQ0FBQyxJQUFJLENBQUMsV0FBVyxFQUFFLENBQUM7UUFDeEIsSUFBSSxDQUFDLEtBQUssQ0FBQyxPQUFPLEVBQUUsQ0FBQztJQUN2QixDQUFDO0lBVUQsZ0NBQU0sR0FBTixVQUFPLElBQVU7UUFDUCxTQUFLLEdBQUssSUFBSSxNQUFULENBQVU7UUFDdkIsSUFBSSxDQUFDLE1BQU0sQ0FDVCxvQkFBQyx1S0FBZSxJQUFDLEtBQUssRUFBRSxFQUFFLE1BQU0sRUFBRSxJQUFJLENBQUMsTUFBTSxFQUFFLFlBQzdDLG9CQUFDLDZGQUFhLElBQU8sS0FBSyxtQkFBSSxvQkFBQyxHQUFHLEtBQUcsR0FBZ0IsR0FDckMsQ0FDbkIsQ0FBQztJQUNKLENBQUM7SUEzQ2tCLGVBQWU7UUFEbkMsb0VBQU0sQ0FBQyxFQUFFLFNBQVMsRUFBRSxVQUFVLEVBQUUsQ0FBQzt5Q0FFTCxLQUFLLEVBQWtCLHdFQUFjLEVBQXFDLE1BQU0sRUFBZ0IsSUFBSTtPQUQ1RyxlQUFlLENBNENuQztJQUFELHNCQUFDO0NBQUEsQ0E1QzRDLG9FQUFVLEdBNEN0RDsyQ0E1Q29CLGVBQWUiLCJzb3VyY2VzIjpbIm9tZi1jaGFuZ2VwYWNrYWdlLWludGVybmV0Oi8vLy4vdHNsaWIvdHNsaWIuZXM2Lm1qcz8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvbXV0YXRvcnMvaW5kZXgudHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtaW50ZXJuZXQ6Ly8vLi4vc3JjL3N0b3JlL0FjdGlvbnMudHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtaW50ZXJuZXQ6Ly8vLi4vc3JjL0NvbmZpZy50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvQ2xpZW50LnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWludGVybmV0Oi8vLy4uL3NyYy9zdG9yZS9FcGljcy9DYXRhbG9nLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWludGVybmV0Oi8vLy4uL3NyYy9zdG9yZS9FcGljcy9Vc2VyQWNjb3VudC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvc3RvcmUvRXBpY3MvT21uaXR1cmUudHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtaW50ZXJuZXQ6Ly8vLi4vc3JjL3N0b3JlL0VwaWNzLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWludGVybmV0Oi8vLy4uL3NyYy9Mb2NhbGl6YXRpb24udHM/Iiwib21mLWNoYW5nZXBhY2thZ2UtaW50ZXJuZXQ6Ly8vLi4vc3JjL3N0b3JlL1N0b3JlLnRzPyIsIm9tZi1jaGFuZ2VwYWNrYWdlLWludGVybmV0Oi8vLy4uL3NyYy9zdG9yZS9pbmRleC50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvUGlwZS50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvdmlld3MvaGVhZGVyL2luZGV4LnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvdmlld3MvY2F0YWxvZy9QYWNrYWdlLnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvdmlld3MvY2F0YWxvZy9MZWdhbC50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtaW50ZXJuZXQ6Ly8vLi4vc3JjL3V0aWxzL0NoYXJhY3RlcmlzdGljcy50cz8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvdmlld3MvY2F0YWxvZy9pbmRleC50c3g/Iiwib21mLWNoYW5nZXBhY2thZ2UtaW50ZXJuZXQ6Ly8vLi4vc3JjL3ZpZXdzL2luZGV4LnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvQXBwLnRzeD8iLCJvbWYtY2hhbmdlcGFja2FnZS1pbnRlcm5ldDovLy8uLi9zcmMvV2lkZ2V0LnRzeD8iXSwic291cmNlc0NvbnRlbnQiOlsiLyoqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKlxuQ29weXJpZ2h0IChjKSBNaWNyb3NvZnQgQ29ycG9yYXRpb24uXG5cblBlcm1pc3Npb24gdG8gdXNlLCBjb3B5LCBtb2RpZnksIGFuZC9vciBkaXN0cmlidXRlIHRoaXMgc29mdHdhcmUgZm9yIGFueVxucHVycG9zZSB3aXRoIG9yIHdpdGhvdXQgZmVlIGlzIGhlcmVieSBncmFudGVkLlxuXG5USEUgU09GVFdBUkUgSVMgUFJPVklERUQgXCJBUyBJU1wiIEFORCBUSEUgQVVUSE9SIERJU0NMQUlNUyBBTEwgV0FSUkFOVElFUyBXSVRIXG5SRUdBUkQgVE8gVEhJUyBTT0ZUV0FSRSBJTkNMVURJTkcgQUxMIElNUExJRUQgV0FSUkFOVElFUyBPRiBNRVJDSEFOVEFCSUxJVFlcbkFORCBGSVRORVNTLiBJTiBOTyBFVkVOVCBTSEFMTCBUSEUgQVVUSE9SIEJFIExJQUJMRSBGT1IgQU5ZIFNQRUNJQUwsIERJUkVDVCxcbklORElSRUNULCBPUiBDT05TRVFVRU5USUFMIERBTUFHRVMgT1IgQU5ZIERBTUFHRVMgV0hBVFNPRVZFUiBSRVNVTFRJTkcgRlJPTVxuTE9TUyBPRiBVU0UsIERBVEEgT1IgUFJPRklUUywgV0hFVEhFUiBJTiBBTiBBQ1RJT04gT0YgQ09OVFJBQ1QsIE5FR0xJR0VOQ0UgT1Jcbk9USEVSIFRPUlRJT1VTIEFDVElPTiwgQVJJU0lORyBPVVQgT0YgT1IgSU4gQ09OTkVDVElPTiBXSVRIIFRIRSBVU0UgT1JcblBFUkZPUk1BTkNFIE9GIFRISVMgU09GVFdBUkUuXG4qKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKioqKiAqL1xuLyogZ2xvYmFsIFJlZmxlY3QsIFByb21pc2UsIFN1cHByZXNzZWRFcnJvciwgU3ltYm9sLCBJdGVyYXRvciAqL1xuXG52YXIgZXh0ZW5kU3RhdGljcyA9IGZ1bmN0aW9uKGQsIGIpIHtcbiAgZXh0ZW5kU3RhdGljcyA9IE9iamVjdC5zZXRQcm90b3R5cGVPZiB8fFxuICAgICAgKHsgX19wcm90b19fOiBbXSB9IGluc3RhbmNlb2YgQXJyYXkgJiYgZnVuY3Rpb24gKGQsIGIpIHsgZC5fX3Byb3RvX18gPSBiOyB9KSB8fFxuICAgICAgZnVuY3Rpb24gKGQsIGIpIHsgZm9yICh2YXIgcCBpbiBiKSBpZiAoT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKGIsIHApKSBkW3BdID0gYltwXTsgfTtcbiAgcmV0dXJuIGV4dGVuZFN0YXRpY3MoZCwgYik7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gX19leHRlbmRzKGQsIGIpIHtcbiAgaWYgKHR5cGVvZiBiICE9PSBcImZ1bmN0aW9uXCIgJiYgYiAhPT0gbnVsbClcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDbGFzcyBleHRlbmRzIHZhbHVlIFwiICsgU3RyaW5nKGIpICsgXCIgaXMgbm90IGEgY29uc3RydWN0b3Igb3IgbnVsbFwiKTtcbiAgZXh0ZW5kU3RhdGljcyhkLCBiKTtcbiAgZnVuY3Rpb24gX18oKSB7IHRoaXMuY29uc3RydWN0b3IgPSBkOyB9XG4gIGQucHJvdG90eXBlID0gYiA9PT0gbnVsbCA/IE9iamVjdC5jcmVhdGUoYikgOiAoX18ucHJvdG90eXBlID0gYi5wcm90b3R5cGUsIG5ldyBfXygpKTtcbn1cblxuZXhwb3J0IHZhciBfX2Fzc2lnbiA9IGZ1bmN0aW9uKCkge1xuICBfX2Fzc2lnbiA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24gX19hc3NpZ24odCkge1xuICAgICAgZm9yICh2YXIgcywgaSA9IDEsIG4gPSBhcmd1bWVudHMubGVuZ3RoOyBpIDwgbjsgaSsrKSB7XG4gICAgICAgICAgcyA9IGFyZ3VtZW50c1tpXTtcbiAgICAgICAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkpIHRbcF0gPSBzW3BdO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHQ7XG4gIH1cbiAgcmV0dXJuIF9fYXNzaWduLmFwcGx5KHRoaXMsIGFyZ3VtZW50cyk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3Jlc3QocywgZSkge1xuICB2YXIgdCA9IHt9O1xuICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkgJiYgZS5pbmRleE9mKHApIDwgMClcbiAgICAgIHRbcF0gPSBzW3BdO1xuICBpZiAocyAhPSBudWxsICYmIHR5cGVvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzID09PSBcImZ1bmN0aW9uXCIpXG4gICAgICBmb3IgKHZhciBpID0gMCwgcCA9IE9iamVjdC5nZXRPd25Qcm9wZXJ0eVN5bWJvbHMocyk7IGkgPCBwLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgaWYgKGUuaW5kZXhPZihwW2ldKSA8IDAgJiYgT2JqZWN0LnByb3RvdHlwZS5wcm9wZXJ0eUlzRW51bWVyYWJsZS5jYWxsKHMsIHBbaV0pKVxuICAgICAgICAgICAgICB0W3BbaV1dID0gc1twW2ldXTtcbiAgICAgIH1cbiAgcmV0dXJuIHQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2RlY29yYXRlKGRlY29yYXRvcnMsIHRhcmdldCwga2V5LCBkZXNjKSB7XG4gIHZhciBjID0gYXJndW1lbnRzLmxlbmd0aCwgciA9IGMgPCAzID8gdGFyZ2V0IDogZGVzYyA9PT0gbnVsbCA/IGRlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHRhcmdldCwga2V5KSA6IGRlc2MsIGQ7XG4gIGlmICh0eXBlb2YgUmVmbGVjdCA9PT0gXCJvYmplY3RcIiAmJiB0eXBlb2YgUmVmbGVjdC5kZWNvcmF0ZSA9PT0gXCJmdW5jdGlvblwiKSByID0gUmVmbGVjdC5kZWNvcmF0ZShkZWNvcmF0b3JzLCB0YXJnZXQsIGtleSwgZGVzYyk7XG4gIGVsc2UgZm9yICh2YXIgaSA9IGRlY29yYXRvcnMubGVuZ3RoIC0gMTsgaSA+PSAwOyBpLS0pIGlmIChkID0gZGVjb3JhdG9yc1tpXSkgciA9IChjIDwgMyA/IGQocikgOiBjID4gMyA/IGQodGFyZ2V0LCBrZXksIHIpIDogZCh0YXJnZXQsIGtleSkpIHx8IHI7XG4gIHJldHVybiBjID4gMyAmJiByICYmIE9iamVjdC5kZWZpbmVQcm9wZXJ0eSh0YXJnZXQsIGtleSwgciksIHI7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3BhcmFtKHBhcmFtSW5kZXgsIGRlY29yYXRvcikge1xuICByZXR1cm4gZnVuY3Rpb24gKHRhcmdldCwga2V5KSB7IGRlY29yYXRvcih0YXJnZXQsIGtleSwgcGFyYW1JbmRleCk7IH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fZXNEZWNvcmF0ZShjdG9yLCBkZXNjcmlwdG9ySW4sIGRlY29yYXRvcnMsIGNvbnRleHRJbiwgaW5pdGlhbGl6ZXJzLCBleHRyYUluaXRpYWxpemVycykge1xuICBmdW5jdGlvbiBhY2NlcHQoZikgeyBpZiAoZiAhPT0gdm9pZCAwICYmIHR5cGVvZiBmICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJGdW5jdGlvbiBleHBlY3RlZFwiKTsgcmV0dXJuIGY7IH1cbiAgdmFyIGtpbmQgPSBjb250ZXh0SW4ua2luZCwga2V5ID0ga2luZCA9PT0gXCJnZXR0ZXJcIiA/IFwiZ2V0XCIgOiBraW5kID09PSBcInNldHRlclwiID8gXCJzZXRcIiA6IFwidmFsdWVcIjtcbiAgdmFyIHRhcmdldCA9ICFkZXNjcmlwdG9ySW4gJiYgY3RvciA/IGNvbnRleHRJbltcInN0YXRpY1wiXSA/IGN0b3IgOiBjdG9yLnByb3RvdHlwZSA6IG51bGw7XG4gIHZhciBkZXNjcmlwdG9yID0gZGVzY3JpcHRvckluIHx8ICh0YXJnZXQgPyBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKHRhcmdldCwgY29udGV4dEluLm5hbWUpIDoge30pO1xuICB2YXIgXywgZG9uZSA9IGZhbHNlO1xuICBmb3IgKHZhciBpID0gZGVjb3JhdG9ycy5sZW5ndGggLSAxOyBpID49IDA7IGktLSkge1xuICAgICAgdmFyIGNvbnRleHQgPSB7fTtcbiAgICAgIGZvciAodmFyIHAgaW4gY29udGV4dEluKSBjb250ZXh0W3BdID0gcCA9PT0gXCJhY2Nlc3NcIiA/IHt9IDogY29udGV4dEluW3BdO1xuICAgICAgZm9yICh2YXIgcCBpbiBjb250ZXh0SW4uYWNjZXNzKSBjb250ZXh0LmFjY2Vzc1twXSA9IGNvbnRleHRJbi5hY2Nlc3NbcF07XG4gICAgICBjb250ZXh0LmFkZEluaXRpYWxpemVyID0gZnVuY3Rpb24gKGYpIHsgaWYgKGRvbmUpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJDYW5ub3QgYWRkIGluaXRpYWxpemVycyBhZnRlciBkZWNvcmF0aW9uIGhhcyBjb21wbGV0ZWRcIik7IGV4dHJhSW5pdGlhbGl6ZXJzLnB1c2goYWNjZXB0KGYgfHwgbnVsbCkpOyB9O1xuICAgICAgdmFyIHJlc3VsdCA9ICgwLCBkZWNvcmF0b3JzW2ldKShraW5kID09PSBcImFjY2Vzc29yXCIgPyB7IGdldDogZGVzY3JpcHRvci5nZXQsIHNldDogZGVzY3JpcHRvci5zZXQgfSA6IGRlc2NyaXB0b3Jba2V5XSwgY29udGV4dCk7XG4gICAgICBpZiAoa2luZCA9PT0gXCJhY2Nlc3NvclwiKSB7XG4gICAgICAgICAgaWYgKHJlc3VsdCA9PT0gdm9pZCAwKSBjb250aW51ZTtcbiAgICAgICAgICBpZiAocmVzdWx0ID09PSBudWxsIHx8IHR5cGVvZiByZXN1bHQgIT09IFwib2JqZWN0XCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJPYmplY3QgZXhwZWN0ZWRcIik7XG4gICAgICAgICAgaWYgKF8gPSBhY2NlcHQocmVzdWx0LmdldCkpIGRlc2NyaXB0b3IuZ2V0ID0gXztcbiAgICAgICAgICBpZiAoXyA9IGFjY2VwdChyZXN1bHQuc2V0KSkgZGVzY3JpcHRvci5zZXQgPSBfO1xuICAgICAgICAgIGlmIChfID0gYWNjZXB0KHJlc3VsdC5pbml0KSkgaW5pdGlhbGl6ZXJzLnVuc2hpZnQoXyk7XG4gICAgICB9XG4gICAgICBlbHNlIGlmIChfID0gYWNjZXB0KHJlc3VsdCkpIHtcbiAgICAgICAgICBpZiAoa2luZCA9PT0gXCJmaWVsZFwiKSBpbml0aWFsaXplcnMudW5zaGlmdChfKTtcbiAgICAgICAgICBlbHNlIGRlc2NyaXB0b3Jba2V5XSA9IF87XG4gICAgICB9XG4gIH1cbiAgaWYgKHRhcmdldCkgT2JqZWN0LmRlZmluZVByb3BlcnR5KHRhcmdldCwgY29udGV4dEluLm5hbWUsIGRlc2NyaXB0b3IpO1xuICBkb25lID0gdHJ1ZTtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3J1bkluaXRpYWxpemVycyh0aGlzQXJnLCBpbml0aWFsaXplcnMsIHZhbHVlKSB7XG4gIHZhciB1c2VWYWx1ZSA9IGFyZ3VtZW50cy5sZW5ndGggPiAyO1xuICBmb3IgKHZhciBpID0gMDsgaSA8IGluaXRpYWxpemVycy5sZW5ndGg7IGkrKykge1xuICAgICAgdmFsdWUgPSB1c2VWYWx1ZSA/IGluaXRpYWxpemVyc1tpXS5jYWxsKHRoaXNBcmcsIHZhbHVlKSA6IGluaXRpYWxpemVyc1tpXS5jYWxsKHRoaXNBcmcpO1xuICB9XG4gIHJldHVybiB1c2VWYWx1ZSA/IHZhbHVlIDogdm9pZCAwO1xufTtcblxuZXhwb3J0IGZ1bmN0aW9uIF9fcHJvcEtleSh4KSB7XG4gIHJldHVybiB0eXBlb2YgeCA9PT0gXCJzeW1ib2xcIiA/IHggOiBcIlwiLmNvbmNhdCh4KTtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3NldEZ1bmN0aW9uTmFtZShmLCBuYW1lLCBwcmVmaXgpIHtcbiAgaWYgKHR5cGVvZiBuYW1lID09PSBcInN5bWJvbFwiKSBuYW1lID0gbmFtZS5kZXNjcmlwdGlvbiA/IFwiW1wiLmNvbmNhdChuYW1lLmRlc2NyaXB0aW9uLCBcIl1cIikgOiBcIlwiO1xuICByZXR1cm4gT2JqZWN0LmRlZmluZVByb3BlcnR5KGYsIFwibmFtZVwiLCB7IGNvbmZpZ3VyYWJsZTogdHJ1ZSwgdmFsdWU6IHByZWZpeCA/IFwiXCIuY29uY2F0KHByZWZpeCwgXCIgXCIsIG5hbWUpIDogbmFtZSB9KTtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBfX21ldGFkYXRhKG1ldGFkYXRhS2V5LCBtZXRhZGF0YVZhbHVlKSB7XG4gIGlmICh0eXBlb2YgUmVmbGVjdCA9PT0gXCJvYmplY3RcIiAmJiB0eXBlb2YgUmVmbGVjdC5tZXRhZGF0YSA9PT0gXCJmdW5jdGlvblwiKSByZXR1cm4gUmVmbGVjdC5tZXRhZGF0YShtZXRhZGF0YUtleSwgbWV0YWRhdGFWYWx1ZSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2F3YWl0ZXIodGhpc0FyZywgX2FyZ3VtZW50cywgUCwgZ2VuZXJhdG9yKSB7XG4gIGZ1bmN0aW9uIGFkb3B0KHZhbHVlKSB7IHJldHVybiB2YWx1ZSBpbnN0YW5jZW9mIFAgPyB2YWx1ZSA6IG5ldyBQKGZ1bmN0aW9uIChyZXNvbHZlKSB7IHJlc29sdmUodmFsdWUpOyB9KTsgfVxuICByZXR1cm4gbmV3IChQIHx8IChQID0gUHJvbWlzZSkpKGZ1bmN0aW9uIChyZXNvbHZlLCByZWplY3QpIHtcbiAgICAgIGZ1bmN0aW9uIGZ1bGZpbGxlZCh2YWx1ZSkgeyB0cnkgeyBzdGVwKGdlbmVyYXRvci5uZXh0KHZhbHVlKSk7IH0gY2F0Y2ggKGUpIHsgcmVqZWN0KGUpOyB9IH1cbiAgICAgIGZ1bmN0aW9uIHJlamVjdGVkKHZhbHVlKSB7IHRyeSB7IHN0ZXAoZ2VuZXJhdG9yW1widGhyb3dcIl0odmFsdWUpKTsgfSBjYXRjaCAoZSkgeyByZWplY3QoZSk7IH0gfVxuICAgICAgZnVuY3Rpb24gc3RlcChyZXN1bHQpIHsgcmVzdWx0LmRvbmUgPyByZXNvbHZlKHJlc3VsdC52YWx1ZSkgOiBhZG9wdChyZXN1bHQudmFsdWUpLnRoZW4oZnVsZmlsbGVkLCByZWplY3RlZCk7IH1cbiAgICAgIHN0ZXAoKGdlbmVyYXRvciA9IGdlbmVyYXRvci5hcHBseSh0aGlzQXJnLCBfYXJndW1lbnRzIHx8IFtdKSkubmV4dCgpKTtcbiAgfSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2dlbmVyYXRvcih0aGlzQXJnLCBib2R5KSB7XG4gIHZhciBfID0geyBsYWJlbDogMCwgc2VudDogZnVuY3Rpb24oKSB7IGlmICh0WzBdICYgMSkgdGhyb3cgdFsxXTsgcmV0dXJuIHRbMV07IH0sIHRyeXM6IFtdLCBvcHM6IFtdIH0sIGYsIHksIHQsIGcgPSBPYmplY3QuY3JlYXRlKCh0eXBlb2YgSXRlcmF0b3IgPT09IFwiZnVuY3Rpb25cIiA/IEl0ZXJhdG9yIDogT2JqZWN0KS5wcm90b3R5cGUpO1xuICByZXR1cm4gZy5uZXh0ID0gdmVyYigwKSwgZ1tcInRocm93XCJdID0gdmVyYigxKSwgZ1tcInJldHVyblwiXSA9IHZlcmIoMiksIHR5cGVvZiBTeW1ib2wgPT09IFwiZnVuY3Rpb25cIiAmJiAoZ1tTeW1ib2wuaXRlcmF0b3JdID0gZnVuY3Rpb24oKSB7IHJldHVybiB0aGlzOyB9KSwgZztcbiAgZnVuY3Rpb24gdmVyYihuKSB7IHJldHVybiBmdW5jdGlvbiAodikgeyByZXR1cm4gc3RlcChbbiwgdl0pOyB9OyB9XG4gIGZ1bmN0aW9uIHN0ZXAob3ApIHtcbiAgICAgIGlmIChmKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiR2VuZXJhdG9yIGlzIGFscmVhZHkgZXhlY3V0aW5nLlwiKTtcbiAgICAgIHdoaWxlIChnICYmIChnID0gMCwgb3BbMF0gJiYgKF8gPSAwKSksIF8pIHRyeSB7XG4gICAgICAgICAgaWYgKGYgPSAxLCB5ICYmICh0ID0gb3BbMF0gJiAyID8geVtcInJldHVyblwiXSA6IG9wWzBdID8geVtcInRocm93XCJdIHx8ICgodCA9IHlbXCJyZXR1cm5cIl0pICYmIHQuY2FsbCh5KSwgMCkgOiB5Lm5leHQpICYmICEodCA9IHQuY2FsbCh5LCBvcFsxXSkpLmRvbmUpIHJldHVybiB0O1xuICAgICAgICAgIGlmICh5ID0gMCwgdCkgb3AgPSBbb3BbMF0gJiAyLCB0LnZhbHVlXTtcbiAgICAgICAgICBzd2l0Y2ggKG9wWzBdKSB7XG4gICAgICAgICAgICAgIGNhc2UgMDogY2FzZSAxOiB0ID0gb3A7IGJyZWFrO1xuICAgICAgICAgICAgICBjYXNlIDQ6IF8ubGFiZWwrKzsgcmV0dXJuIHsgdmFsdWU6IG9wWzFdLCBkb25lOiBmYWxzZSB9O1xuICAgICAgICAgICAgICBjYXNlIDU6IF8ubGFiZWwrKzsgeSA9IG9wWzFdOyBvcCA9IFswXTsgY29udGludWU7XG4gICAgICAgICAgICAgIGNhc2UgNzogb3AgPSBfLm9wcy5wb3AoKTsgXy50cnlzLnBvcCgpOyBjb250aW51ZTtcbiAgICAgICAgICAgICAgZGVmYXVsdDpcbiAgICAgICAgICAgICAgICAgIGlmICghKHQgPSBfLnRyeXMsIHQgPSB0Lmxlbmd0aCA+IDAgJiYgdFt0Lmxlbmd0aCAtIDFdKSAmJiAob3BbMF0gPT09IDYgfHwgb3BbMF0gPT09IDIpKSB7IF8gPSAwOyBjb250aW51ZTsgfVxuICAgICAgICAgICAgICAgICAgaWYgKG9wWzBdID09PSAzICYmICghdCB8fCAob3BbMV0gPiB0WzBdICYmIG9wWzFdIDwgdFszXSkpKSB7IF8ubGFiZWwgPSBvcFsxXTsgYnJlYWs7IH1cbiAgICAgICAgICAgICAgICAgIGlmIChvcFswXSA9PT0gNiAmJiBfLmxhYmVsIDwgdFsxXSkgeyBfLmxhYmVsID0gdFsxXTsgdCA9IG9wOyBicmVhazsgfVxuICAgICAgICAgICAgICAgICAgaWYgKHQgJiYgXy5sYWJlbCA8IHRbMl0pIHsgXy5sYWJlbCA9IHRbMl07IF8ub3BzLnB1c2gob3ApOyBicmVhazsgfVxuICAgICAgICAgICAgICAgICAgaWYgKHRbMl0pIF8ub3BzLnBvcCgpO1xuICAgICAgICAgICAgICAgICAgXy50cnlzLnBvcCgpOyBjb250aW51ZTtcbiAgICAgICAgICB9XG4gICAgICAgICAgb3AgPSBib2R5LmNhbGwodGhpc0FyZywgXyk7XG4gICAgICB9IGNhdGNoIChlKSB7IG9wID0gWzYsIGVdOyB5ID0gMDsgfSBmaW5hbGx5IHsgZiA9IHQgPSAwOyB9XG4gICAgICBpZiAob3BbMF0gJiA1KSB0aHJvdyBvcFsxXTsgcmV0dXJuIHsgdmFsdWU6IG9wWzBdID8gb3BbMV0gOiB2b2lkIDAsIGRvbmU6IHRydWUgfTtcbiAgfVxufVxuXG5leHBvcnQgdmFyIF9fY3JlYXRlQmluZGluZyA9IE9iamVjdC5jcmVhdGUgPyAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgdmFyIGRlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG0sIGspO1xuICBpZiAoIWRlc2MgfHwgKFwiZ2V0XCIgaW4gZGVzYyA/ICFtLl9fZXNNb2R1bGUgOiBkZXNjLndyaXRhYmxlIHx8IGRlc2MuY29uZmlndXJhYmxlKSkge1xuICAgICAgZGVzYyA9IHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbigpIHsgcmV0dXJuIG1ba107IH0gfTtcbiAgfVxuICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIGRlc2MpO1xufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgaWYgKGsyID09PSB1bmRlZmluZWQpIGsyID0gaztcbiAgb1trMl0gPSBtW2tdO1xufSk7XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2V4cG9ydFN0YXIobSwgbykge1xuICBmb3IgKHZhciBwIGluIG0pIGlmIChwICE9PSBcImRlZmF1bHRcIiAmJiAhT2JqZWN0LnByb3RvdHlwZS5oYXNPd25Qcm9wZXJ0eS5jYWxsKG8sIHApKSBfX2NyZWF0ZUJpbmRpbmcobywgbSwgcCk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3ZhbHVlcyhvKSB7XG4gIHZhciBzID0gdHlwZW9mIFN5bWJvbCA9PT0gXCJmdW5jdGlvblwiICYmIFN5bWJvbC5pdGVyYXRvciwgbSA9IHMgJiYgb1tzXSwgaSA9IDA7XG4gIGlmIChtKSByZXR1cm4gbS5jYWxsKG8pO1xuICBpZiAobyAmJiB0eXBlb2Ygby5sZW5ndGggPT09IFwibnVtYmVyXCIpIHJldHVybiB7XG4gICAgICBuZXh0OiBmdW5jdGlvbiAoKSB7XG4gICAgICAgICAgaWYgKG8gJiYgaSA+PSBvLmxlbmd0aCkgbyA9IHZvaWQgMDtcbiAgICAgICAgICByZXR1cm4geyB2YWx1ZTogbyAmJiBvW2krK10sIGRvbmU6ICFvIH07XG4gICAgICB9XG4gIH07XG4gIHRocm93IG5ldyBUeXBlRXJyb3IocyA/IFwiT2JqZWN0IGlzIG5vdCBpdGVyYWJsZS5cIiA6IFwiU3ltYm9sLml0ZXJhdG9yIGlzIG5vdCBkZWZpbmVkLlwiKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fcmVhZChvLCBuKSB7XG4gIHZhciBtID0gdHlwZW9mIFN5bWJvbCA9PT0gXCJmdW5jdGlvblwiICYmIG9bU3ltYm9sLml0ZXJhdG9yXTtcbiAgaWYgKCFtKSByZXR1cm4gbztcbiAgdmFyIGkgPSBtLmNhbGwobyksIHIsIGFyID0gW10sIGU7XG4gIHRyeSB7XG4gICAgICB3aGlsZSAoKG4gPT09IHZvaWQgMCB8fCBuLS0gPiAwKSAmJiAhKHIgPSBpLm5leHQoKSkuZG9uZSkgYXIucHVzaChyLnZhbHVlKTtcbiAgfVxuICBjYXRjaCAoZXJyb3IpIHsgZSA9IHsgZXJyb3I6IGVycm9yIH07IH1cbiAgZmluYWxseSB7XG4gICAgICB0cnkge1xuICAgICAgICAgIGlmIChyICYmICFyLmRvbmUgJiYgKG0gPSBpW1wicmV0dXJuXCJdKSkgbS5jYWxsKGkpO1xuICAgICAgfVxuICAgICAgZmluYWxseSB7IGlmIChlKSB0aHJvdyBlLmVycm9yOyB9XG4gIH1cbiAgcmV0dXJuIGFyO1xufVxuXG4vKiogQGRlcHJlY2F0ZWQgKi9cbmV4cG9ydCBmdW5jdGlvbiBfX3NwcmVhZCgpIHtcbiAgZm9yICh2YXIgYXIgPSBbXSwgaSA9IDA7IGkgPCBhcmd1bWVudHMubGVuZ3RoOyBpKyspXG4gICAgICBhciA9IGFyLmNvbmNhdChfX3JlYWQoYXJndW1lbnRzW2ldKSk7XG4gIHJldHVybiBhcjtcbn1cblxuLyoqIEBkZXByZWNhdGVkICovXG5leHBvcnQgZnVuY3Rpb24gX19zcHJlYWRBcnJheXMoKSB7XG4gIGZvciAodmFyIHMgPSAwLCBpID0gMCwgaWwgPSBhcmd1bWVudHMubGVuZ3RoOyBpIDwgaWw7IGkrKykgcyArPSBhcmd1bWVudHNbaV0ubGVuZ3RoO1xuICBmb3IgKHZhciByID0gQXJyYXkocyksIGsgPSAwLCBpID0gMDsgaSA8IGlsOyBpKyspXG4gICAgICBmb3IgKHZhciBhID0gYXJndW1lbnRzW2ldLCBqID0gMCwgamwgPSBhLmxlbmd0aDsgaiA8IGpsOyBqKyssIGsrKylcbiAgICAgICAgICByW2tdID0gYVtqXTtcbiAgcmV0dXJuIHI7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX3NwcmVhZEFycmF5KHRvLCBmcm9tLCBwYWNrKSB7XG4gIGlmIChwYWNrIHx8IGFyZ3VtZW50cy5sZW5ndGggPT09IDIpIGZvciAodmFyIGkgPSAwLCBsID0gZnJvbS5sZW5ndGgsIGFyOyBpIDwgbDsgaSsrKSB7XG4gICAgICBpZiAoYXIgfHwgIShpIGluIGZyb20pKSB7XG4gICAgICAgICAgaWYgKCFhcikgYXIgPSBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChmcm9tLCAwLCBpKTtcbiAgICAgICAgICBhcltpXSA9IGZyb21baV07XG4gICAgICB9XG4gIH1cbiAgcmV0dXJuIHRvLmNvbmNhdChhciB8fCBBcnJheS5wcm90b3R5cGUuc2xpY2UuY2FsbChmcm9tKSk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2F3YWl0KHYpIHtcbiAgcmV0dXJuIHRoaXMgaW5zdGFuY2VvZiBfX2F3YWl0ID8gKHRoaXMudiA9IHYsIHRoaXMpIDogbmV3IF9fYXdhaXQodik7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2FzeW5jR2VuZXJhdG9yKHRoaXNBcmcsIF9hcmd1bWVudHMsIGdlbmVyYXRvcikge1xuICBpZiAoIVN5bWJvbC5hc3luY0l0ZXJhdG9yKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiU3ltYm9sLmFzeW5jSXRlcmF0b3IgaXMgbm90IGRlZmluZWQuXCIpO1xuICB2YXIgZyA9IGdlbmVyYXRvci5hcHBseSh0aGlzQXJnLCBfYXJndW1lbnRzIHx8IFtdKSwgaSwgcSA9IFtdO1xuICByZXR1cm4gaSA9IE9iamVjdC5jcmVhdGUoKHR5cGVvZiBBc3luY0l0ZXJhdG9yID09PSBcImZ1bmN0aW9uXCIgPyBBc3luY0l0ZXJhdG9yIDogT2JqZWN0KS5wcm90b3R5cGUpLCB2ZXJiKFwibmV4dFwiKSwgdmVyYihcInRocm93XCIpLCB2ZXJiKFwicmV0dXJuXCIsIGF3YWl0UmV0dXJuKSwgaVtTeW1ib2wuYXN5bmNJdGVyYXRvcl0gPSBmdW5jdGlvbiAoKSB7IHJldHVybiB0aGlzOyB9LCBpO1xuICBmdW5jdGlvbiBhd2FpdFJldHVybihmKSB7IHJldHVybiBmdW5jdGlvbiAodikgeyByZXR1cm4gUHJvbWlzZS5yZXNvbHZlKHYpLnRoZW4oZiwgcmVqZWN0KTsgfTsgfVxuICBmdW5jdGlvbiB2ZXJiKG4sIGYpIHsgaWYgKGdbbl0pIHsgaVtuXSA9IGZ1bmN0aW9uICh2KSB7IHJldHVybiBuZXcgUHJvbWlzZShmdW5jdGlvbiAoYSwgYikgeyBxLnB1c2goW24sIHYsIGEsIGJdKSA+IDEgfHwgcmVzdW1lKG4sIHYpOyB9KTsgfTsgaWYgKGYpIGlbbl0gPSBmKGlbbl0pOyB9IH1cbiAgZnVuY3Rpb24gcmVzdW1lKG4sIHYpIHsgdHJ5IHsgc3RlcChnW25dKHYpKTsgfSBjYXRjaCAoZSkgeyBzZXR0bGUocVswXVszXSwgZSk7IH0gfVxuICBmdW5jdGlvbiBzdGVwKHIpIHsgci52YWx1ZSBpbnN0YW5jZW9mIF9fYXdhaXQgPyBQcm9taXNlLnJlc29sdmUoci52YWx1ZS52KS50aGVuKGZ1bGZpbGwsIHJlamVjdCkgOiBzZXR0bGUocVswXVsyXSwgcik7IH1cbiAgZnVuY3Rpb24gZnVsZmlsbCh2YWx1ZSkgeyByZXN1bWUoXCJuZXh0XCIsIHZhbHVlKTsgfVxuICBmdW5jdGlvbiByZWplY3QodmFsdWUpIHsgcmVzdW1lKFwidGhyb3dcIiwgdmFsdWUpOyB9XG4gIGZ1bmN0aW9uIHNldHRsZShmLCB2KSB7IGlmIChmKHYpLCBxLnNoaWZ0KCksIHEubGVuZ3RoKSByZXN1bWUocVswXVswXSwgcVswXVsxXSk7IH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fYXN5bmNEZWxlZ2F0b3Iobykge1xuICB2YXIgaSwgcDtcbiAgcmV0dXJuIGkgPSB7fSwgdmVyYihcIm5leHRcIiksIHZlcmIoXCJ0aHJvd1wiLCBmdW5jdGlvbiAoZSkgeyB0aHJvdyBlOyB9KSwgdmVyYihcInJldHVyblwiKSwgaVtTeW1ib2wuaXRlcmF0b3JdID0gZnVuY3Rpb24gKCkgeyByZXR1cm4gdGhpczsgfSwgaTtcbiAgZnVuY3Rpb24gdmVyYihuLCBmKSB7IGlbbl0gPSBvW25dID8gZnVuY3Rpb24gKHYpIHsgcmV0dXJuIChwID0gIXApID8geyB2YWx1ZTogX19hd2FpdChvW25dKHYpKSwgZG9uZTogZmFsc2UgfSA6IGYgPyBmKHYpIDogdjsgfSA6IGY7IH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fYXN5bmNWYWx1ZXMobykge1xuICBpZiAoIVN5bWJvbC5hc3luY0l0ZXJhdG9yKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiU3ltYm9sLmFzeW5jSXRlcmF0b3IgaXMgbm90IGRlZmluZWQuXCIpO1xuICB2YXIgbSA9IG9bU3ltYm9sLmFzeW5jSXRlcmF0b3JdLCBpO1xuICByZXR1cm4gbSA/IG0uY2FsbChvKSA6IChvID0gdHlwZW9mIF9fdmFsdWVzID09PSBcImZ1bmN0aW9uXCIgPyBfX3ZhbHVlcyhvKSA6IG9bU3ltYm9sLml0ZXJhdG9yXSgpLCBpID0ge30sIHZlcmIoXCJuZXh0XCIpLCB2ZXJiKFwidGhyb3dcIiksIHZlcmIoXCJyZXR1cm5cIiksIGlbU3ltYm9sLmFzeW5jSXRlcmF0b3JdID0gZnVuY3Rpb24gKCkgeyByZXR1cm4gdGhpczsgfSwgaSk7XG4gIGZ1bmN0aW9uIHZlcmIobikgeyBpW25dID0gb1tuXSAmJiBmdW5jdGlvbiAodikgeyByZXR1cm4gbmV3IFByb21pc2UoZnVuY3Rpb24gKHJlc29sdmUsIHJlamVjdCkgeyB2ID0gb1tuXSh2KSwgc2V0dGxlKHJlc29sdmUsIHJlamVjdCwgdi5kb25lLCB2LnZhbHVlKTsgfSk7IH07IH1cbiAgZnVuY3Rpb24gc2V0dGxlKHJlc29sdmUsIHJlamVjdCwgZCwgdikgeyBQcm9taXNlLnJlc29sdmUodikudGhlbihmdW5jdGlvbih2KSB7IHJlc29sdmUoeyB2YWx1ZTogdiwgZG9uZTogZCB9KTsgfSwgcmVqZWN0KTsgfVxufVxuXG5leHBvcnQgZnVuY3Rpb24gX19tYWtlVGVtcGxhdGVPYmplY3QoY29va2VkLCByYXcpIHtcbiAgaWYgKE9iamVjdC5kZWZpbmVQcm9wZXJ0eSkgeyBPYmplY3QuZGVmaW5lUHJvcGVydHkoY29va2VkLCBcInJhd1wiLCB7IHZhbHVlOiByYXcgfSk7IH0gZWxzZSB7IGNvb2tlZC5yYXcgPSByYXc7IH1cbiAgcmV0dXJuIGNvb2tlZDtcbn07XG5cbnZhciBfX3NldE1vZHVsZURlZmF1bHQgPSBPYmplY3QuY3JlYXRlID8gKGZ1bmN0aW9uKG8sIHYpIHtcbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG8sIFwiZGVmYXVsdFwiLCB7IGVudW1lcmFibGU6IHRydWUsIHZhbHVlOiB2IH0pO1xufSkgOiBmdW5jdGlvbihvLCB2KSB7XG4gIG9bXCJkZWZhdWx0XCJdID0gdjtcbn07XG5cbnZhciBvd25LZXlzID0gZnVuY3Rpb24obykge1xuICBvd25LZXlzID0gT2JqZWN0LmdldE93blByb3BlcnR5TmFtZXMgfHwgZnVuY3Rpb24gKG8pIHtcbiAgICB2YXIgYXIgPSBbXTtcbiAgICBmb3IgKHZhciBrIGluIG8pIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwobywgaykpIGFyW2FyLmxlbmd0aF0gPSBrO1xuICAgIHJldHVybiBhcjtcbiAgfTtcbiAgcmV0dXJuIG93bktleXMobyk7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gX19pbXBvcnRTdGFyKG1vZCkge1xuICBpZiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSByZXR1cm4gbW9kO1xuICB2YXIgcmVzdWx0ID0ge307XG4gIGlmIChtb2QgIT0gbnVsbCkgZm9yICh2YXIgayA9IG93bktleXMobW9kKSwgaSA9IDA7IGkgPCBrLmxlbmd0aDsgaSsrKSBpZiAoa1tpXSAhPT0gXCJkZWZhdWx0XCIpIF9fY3JlYXRlQmluZGluZyhyZXN1bHQsIG1vZCwga1tpXSk7XG4gIF9fc2V0TW9kdWxlRGVmYXVsdChyZXN1bHQsIG1vZCk7XG4gIHJldHVybiByZXN1bHQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2ltcG9ydERlZmF1bHQobW9kKSB7XG4gIHJldHVybiAobW9kICYmIG1vZC5fX2VzTW9kdWxlKSA/IG1vZCA6IHsgZGVmYXVsdDogbW9kIH07XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2NsYXNzUHJpdmF0ZUZpZWxkR2V0KHJlY2VpdmVyLCBzdGF0ZSwga2luZCwgZikge1xuICBpZiAoa2luZCA9PT0gXCJhXCIgJiYgIWYpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJQcml2YXRlIGFjY2Vzc29yIHdhcyBkZWZpbmVkIHdpdGhvdXQgYSBnZXR0ZXJcIik7XG4gIGlmICh0eXBlb2Ygc3RhdGUgPT09IFwiZnVuY3Rpb25cIiA/IHJlY2VpdmVyICE9PSBzdGF0ZSB8fCAhZiA6ICFzdGF0ZS5oYXMocmVjZWl2ZXIpKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IHJlYWQgcHJpdmF0ZSBtZW1iZXIgZnJvbSBhbiBvYmplY3Qgd2hvc2UgY2xhc3MgZGlkIG5vdCBkZWNsYXJlIGl0XCIpO1xuICByZXR1cm4ga2luZCA9PT0gXCJtXCIgPyBmIDoga2luZCA9PT0gXCJhXCIgPyBmLmNhbGwocmVjZWl2ZXIpIDogZiA/IGYudmFsdWUgOiBzdGF0ZS5nZXQocmVjZWl2ZXIpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19jbGFzc1ByaXZhdGVGaWVsZFNldChyZWNlaXZlciwgc3RhdGUsIHZhbHVlLCBraW5kLCBmKSB7XG4gIGlmIChraW5kID09PSBcIm1cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlByaXZhdGUgbWV0aG9kIGlzIG5vdCB3cml0YWJsZVwiKTtcbiAgaWYgKGtpbmQgPT09IFwiYVwiICYmICFmKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiUHJpdmF0ZSBhY2Nlc3NvciB3YXMgZGVmaW5lZCB3aXRob3V0IGEgc2V0dGVyXCIpO1xuICBpZiAodHlwZW9mIHN0YXRlID09PSBcImZ1bmN0aW9uXCIgPyByZWNlaXZlciAhPT0gc3RhdGUgfHwgIWYgOiAhc3RhdGUuaGFzKHJlY2VpdmVyKSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIkNhbm5vdCB3cml0ZSBwcml2YXRlIG1lbWJlciB0byBhbiBvYmplY3Qgd2hvc2UgY2xhc3MgZGlkIG5vdCBkZWNsYXJlIGl0XCIpO1xuICByZXR1cm4gKGtpbmQgPT09IFwiYVwiID8gZi5jYWxsKHJlY2VpdmVyLCB2YWx1ZSkgOiBmID8gZi52YWx1ZSA9IHZhbHVlIDogc3RhdGUuc2V0KHJlY2VpdmVyLCB2YWx1ZSkpLCB2YWx1ZTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIF9fY2xhc3NQcml2YXRlRmllbGRJbihzdGF0ZSwgcmVjZWl2ZXIpIHtcbiAgaWYgKHJlY2VpdmVyID09PSBudWxsIHx8ICh0eXBlb2YgcmVjZWl2ZXIgIT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIHJlY2VpdmVyICE9PSBcImZ1bmN0aW9uXCIpKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IHVzZSAnaW4nIG9wZXJhdG9yIG9uIG5vbi1vYmplY3RcIik7XG4gIHJldHVybiB0eXBlb2Ygc3RhdGUgPT09IFwiZnVuY3Rpb25cIiA/IHJlY2VpdmVyID09PSBzdGF0ZSA6IHN0YXRlLmhhcyhyZWNlaXZlcik7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2FkZERpc3Bvc2FibGVSZXNvdXJjZShlbnYsIHZhbHVlLCBhc3luYykge1xuICBpZiAodmFsdWUgIT09IG51bGwgJiYgdmFsdWUgIT09IHZvaWQgMCkge1xuICAgIGlmICh0eXBlb2YgdmFsdWUgIT09IFwib2JqZWN0XCIgJiYgdHlwZW9mIHZhbHVlICE9PSBcImZ1bmN0aW9uXCIpIHRocm93IG5ldyBUeXBlRXJyb3IoXCJPYmplY3QgZXhwZWN0ZWQuXCIpO1xuICAgIHZhciBkaXNwb3NlLCBpbm5lcjtcbiAgICBpZiAoYXN5bmMpIHtcbiAgICAgIGlmICghU3ltYm9sLmFzeW5jRGlzcG9zZSkgdGhyb3cgbmV3IFR5cGVFcnJvcihcIlN5bWJvbC5hc3luY0Rpc3Bvc2UgaXMgbm90IGRlZmluZWQuXCIpO1xuICAgICAgZGlzcG9zZSA9IHZhbHVlW1N5bWJvbC5hc3luY0Rpc3Bvc2VdO1xuICAgIH1cbiAgICBpZiAoZGlzcG9zZSA9PT0gdm9pZCAwKSB7XG4gICAgICBpZiAoIVN5bWJvbC5kaXNwb3NlKSB0aHJvdyBuZXcgVHlwZUVycm9yKFwiU3ltYm9sLmRpc3Bvc2UgaXMgbm90IGRlZmluZWQuXCIpO1xuICAgICAgZGlzcG9zZSA9IHZhbHVlW1N5bWJvbC5kaXNwb3NlXTtcbiAgICAgIGlmIChhc3luYykgaW5uZXIgPSBkaXNwb3NlO1xuICAgIH1cbiAgICBpZiAodHlwZW9mIGRpc3Bvc2UgIT09IFwiZnVuY3Rpb25cIikgdGhyb3cgbmV3IFR5cGVFcnJvcihcIk9iamVjdCBub3QgZGlzcG9zYWJsZS5cIik7XG4gICAgaWYgKGlubmVyKSBkaXNwb3NlID0gZnVuY3Rpb24oKSB7IHRyeSB7IGlubmVyLmNhbGwodGhpcyk7IH0gY2F0Y2ggKGUpIHsgcmV0dXJuIFByb21pc2UucmVqZWN0KGUpOyB9IH07XG4gICAgZW52LnN0YWNrLnB1c2goeyB2YWx1ZTogdmFsdWUsIGRpc3Bvc2U6IGRpc3Bvc2UsIGFzeW5jOiBhc3luYyB9KTtcbiAgfVxuICBlbHNlIGlmIChhc3luYykge1xuICAgIGVudi5zdGFjay5wdXNoKHsgYXN5bmM6IHRydWUgfSk7XG4gIH1cbiAgcmV0dXJuIHZhbHVlO1xufVxuXG52YXIgX1N1cHByZXNzZWRFcnJvciA9IHR5cGVvZiBTdXBwcmVzc2VkRXJyb3IgPT09IFwiZnVuY3Rpb25cIiA/IFN1cHByZXNzZWRFcnJvciA6IGZ1bmN0aW9uIChlcnJvciwgc3VwcHJlc3NlZCwgbWVzc2FnZSkge1xuICB2YXIgZSA9IG5ldyBFcnJvcihtZXNzYWdlKTtcbiAgcmV0dXJuIGUubmFtZSA9IFwiU3VwcHJlc3NlZEVycm9yXCIsIGUuZXJyb3IgPSBlcnJvciwgZS5zdXBwcmVzc2VkID0gc3VwcHJlc3NlZCwgZTtcbn07XG5cbmV4cG9ydCBmdW5jdGlvbiBfX2Rpc3Bvc2VSZXNvdXJjZXMoZW52KSB7XG4gIGZ1bmN0aW9uIGZhaWwoZSkge1xuICAgIGVudi5lcnJvciA9IGVudi5oYXNFcnJvciA/IG5ldyBfU3VwcHJlc3NlZEVycm9yKGUsIGVudi5lcnJvciwgXCJBbiBlcnJvciB3YXMgc3VwcHJlc3NlZCBkdXJpbmcgZGlzcG9zYWwuXCIpIDogZTtcbiAgICBlbnYuaGFzRXJyb3IgPSB0cnVlO1xuICB9XG4gIHZhciByLCBzID0gMDtcbiAgZnVuY3Rpb24gbmV4dCgpIHtcbiAgICB3aGlsZSAociA9IGVudi5zdGFjay5wb3AoKSkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgaWYgKCFyLmFzeW5jICYmIHMgPT09IDEpIHJldHVybiBzID0gMCwgZW52LnN0YWNrLnB1c2gociksIFByb21pc2UucmVzb2x2ZSgpLnRoZW4obmV4dCk7XG4gICAgICAgIGlmIChyLmRpc3Bvc2UpIHtcbiAgICAgICAgICB2YXIgcmVzdWx0ID0gci5kaXNwb3NlLmNhbGwoci52YWx1ZSk7XG4gICAgICAgICAgaWYgKHIuYXN5bmMpIHJldHVybiBzIHw9IDIsIFByb21pc2UucmVzb2x2ZShyZXN1bHQpLnRoZW4obmV4dCwgZnVuY3Rpb24oZSkgeyBmYWlsKGUpOyByZXR1cm4gbmV4dCgpOyB9KTtcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHMgfD0gMTtcbiAgICAgIH1cbiAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgIGZhaWwoZSk7XG4gICAgICB9XG4gICAgfVxuICAgIGlmIChzID09PSAxKSByZXR1cm4gZW52Lmhhc0Vycm9yID8gUHJvbWlzZS5yZWplY3QoZW52LmVycm9yKSA6IFByb21pc2UucmVzb2x2ZSgpO1xuICAgIGlmIChlbnYuaGFzRXJyb3IpIHRocm93IGVudi5lcnJvcjtcbiAgfVxuICByZXR1cm4gbmV4dCgpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gX19yZXdyaXRlUmVsYXRpdmVJbXBvcnRFeHRlbnNpb24ocGF0aCwgcHJlc2VydmVKc3gpIHtcbiAgaWYgKHR5cGVvZiBwYXRoID09PSBcInN0cmluZ1wiICYmIC9eXFwuXFwuP1xcLy8udGVzdChwYXRoKSkge1xuICAgICAgcmV0dXJuIHBhdGgucmVwbGFjZSgvXFwuKHRzeCkkfCgoPzpcXC5kKT8pKCg/OlxcLlteLi9dKz8pPylcXC4oW2NtXT8pdHMkL2ksIGZ1bmN0aW9uIChtLCB0c3gsIGQsIGV4dCwgY20pIHtcbiAgICAgICAgICByZXR1cm4gdHN4ID8gcHJlc2VydmVKc3ggPyBcIi5qc3hcIiA6IFwiLmpzXCIgOiBkICYmICghZXh0IHx8ICFjbSkgPyBtIDogKGQgKyBleHQgKyBcIi5cIiArIGNtLnRvTG93ZXJDYXNlKCkgKyBcImpzXCIpO1xuICAgICAgfSk7XG4gIH1cbiAgcmV0dXJuIHBhdGg7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgX19leHRlbmRzLFxuICBfX2Fzc2lnbixcbiAgX19yZXN0LFxuICBfX2RlY29yYXRlLFxuICBfX3BhcmFtLFxuICBfX2VzRGVjb3JhdGUsXG4gIF9fcnVuSW5pdGlhbGl6ZXJzLFxuICBfX3Byb3BLZXksXG4gIF9fc2V0RnVuY3Rpb25OYW1lLFxuICBfX21ldGFkYXRhLFxuICBfX2F3YWl0ZXIsXG4gIF9fZ2VuZXJhdG9yLFxuICBfX2NyZWF0ZUJpbmRpbmcsXG4gIF9fZXhwb3J0U3RhcixcbiAgX192YWx1ZXMsXG4gIF9fcmVhZCxcbiAgX19zcHJlYWQsXG4gIF9fc3ByZWFkQXJyYXlzLFxuICBfX3NwcmVhZEFycmF5LFxuICBfX2F3YWl0LFxuICBfX2FzeW5jR2VuZXJhdG9yLFxuICBfX2FzeW5jRGVsZWdhdG9yLFxuICBfX2FzeW5jVmFsdWVzLFxuICBfX21ha2VUZW1wbGF0ZU9iamVjdCxcbiAgX19pbXBvcnRTdGFyLFxuICBfX2ltcG9ydERlZmF1bHQsXG4gIF9fY2xhc3NQcml2YXRlRmllbGRHZXQsXG4gIF9fY2xhc3NQcml2YXRlRmllbGRTZXQsXG4gIF9fY2xhc3NQcml2YXRlRmllbGRJbixcbiAgX19hZGREaXNwb3NhYmxlUmVzb3VyY2UsXG4gIF9fZGlzcG9zZVJlc291cmNlcyxcbiAgX19yZXdyaXRlUmVsYXRpdmVJbXBvcnRFeHRlbnNpb24sXG59O1xuIiwiaW1wb3J0IHsgSVNlcnZpY2VBY2NvdW50QVBJLCBJQWNjb3VudERldGFpbHMsIElQYWNrYWdlIH0gZnJvbSBcIi4uL21vZGVsc1wiO1xyXG5pbXBvcnQgeyBWYWx1ZU9mLCBWb2x0IH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBzZXJ2aWNlQWNjb3VudE11dGF0b3JGbihyZXNwb25zZTogSVNlcnZpY2VBY2NvdW50QVBJKTogQXJyYXk8SUFjY291bnREZXRhaWxzPiB7XHJcbiAgcmV0dXJuIFZhbHVlT2Y8QXJyYXk8SUFjY291bnREZXRhaWxzPj4ocmVzcG9uc2UsIFwiUHJvZHVjdE9mZmVyaW5nc1wiLCBbeyBVbmF2YWlsYWJsZTogdHJ1ZSB9XSk7XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBjYXRhbG9nTXV0YXRvckZuKHJlc3BvbnNlOiBWb2x0LklBUElSZXNwb25zZSk6IEFycmF5PElQYWNrYWdlPiB7XHJcbiAgY29uc3QgcHJvZHVjdE9mZmVyaW5nR3JvdXA6IFZvbHQuSVByb2R1Y3RPZmZlcmluZ0dyb3VwID1cclxuICAgICAgICBWYWx1ZU9mKHJlc3BvbnNlLCBcInByb2R1Y3RPZmZlcmluZ0RldGFpbC5wcm9kdWN0T2ZmZXJpbmdHcm91cHNcIiwgW10pXHJcbiAgICAgICAgICAuZmluZCgoZ3JvdXA6IFZvbHQuSVByb2R1Y3RPZmZlcmluZ0dyb3VwKSA9PiBncm91cC5saW5lT2ZCdXNpbmVzcyA9PT0gVm9sdC5FTGluZU9mQnVzaW5lc3MuSW50ZXJuZXQgJiZcclxuICAgICAgICAgICAgICAgIGdyb3VwLnByb2R1Y3RPZmZlcmluZ0dyb3VwVHlwZSA9PT0gVm9sdC5FUHJvZHVjdE9mZmVyaW5nR3JvdXBUeXBlLkRlZmF1bHQpO1xyXG4gIHJldHVybiBWYWx1ZU9mKHByb2R1Y3RPZmZlcmluZ0dyb3VwLCBcInByb2R1Y3RPZmZlcmluZ3NcIiwgW10pO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gb3JkZXJNdXRhdG9yRm4ocmVzcG9uc2U6IFZvbHQuSUFQSVJlc3BvbnNlLCBjYXRhbG9nOiBBcnJheTxJUGFja2FnZT4pOiBBcnJheTxJUGFja2FnZT4ge1xyXG4gIGNvbnN0IHByb2R1Y3RPZmZlcmluZ0dyb3VwOiBWb2x0LklQcm9kdWN0T2ZmZXJpbmdHcm91cCA9XHJcbiAgICAgICAgVmFsdWVPZihyZXNwb25zZSwgXCJwcm9kdWN0T2ZmZXJpbmdEZXRhaWwucHJvZHVjdE9mZmVyaW5nR3JvdXBzXCIsIFtdKVxyXG4gICAgICAgICAgLmZpbmQoKGdyb3VwOiBWb2x0LklQcm9kdWN0T2ZmZXJpbmdHcm91cCkgPT4gZ3JvdXAubGluZU9mQnVzaW5lc3MgPT09IFZvbHQuRUxpbmVPZkJ1c2luZXNzLkludGVybmV0ICYmXHJcbiAgICAgICAgICAgICAgICBncm91cC5wcm9kdWN0T2ZmZXJpbmdHcm91cFR5cGUgPT09IFZvbHQuRVByb2R1Y3RPZmZlcmluZ0dyb3VwVHlwZS5EZWx0YSk7XHJcbiAgY29uc3QgcHJvZHVjdE9mZmVyaW5nczogQXJyYXk8Vm9sdC5JUHJvZHVjdE9mZmVyaW5nPiA9IFZhbHVlT2YocHJvZHVjdE9mZmVyaW5nR3JvdXAsIFwicHJvZHVjdE9mZmVyaW5nc1wiLCBbXSk7XHJcbiAgcHJvZHVjdE9mZmVyaW5ncy5mb3JFYWNoKHByb2R1Y3QgPT4ge1xyXG4gICAgY29uc3QgaW5pdGlhbDogSVBhY2thZ2UgPSBjYXRhbG9nLmZpbmQocGtnID0+IHBrZy5pZCA9PT0gcHJvZHVjdC5pZCkgfHwge30gYXMgSVBhY2thZ2U7XHJcbiAgICBPYmplY3QuYXNzaWduKGluaXRpYWwsIHByb2R1Y3QpO1xyXG4gIH0pO1xyXG4gIHJldHVybiBbLi4uY2F0YWxvZ10gYXMgQXJyYXk8SVBhY2thZ2U+O1xyXG59XHJcbiIsImltcG9ydCB7IGNyZWF0ZUFjdGlvbiwgQWN0aW9uIH0gZnJvbSBcInJlZHV4LWFjdGlvbnNcIjtcclxuaW1wb3J0IHsgVm9sdCB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcbmltcG9ydCB7IHNlcnZpY2VBY2NvdW50TXV0YXRvckZuLCBjYXRhbG9nTXV0YXRvckZuLCBvcmRlck11dGF0b3JGbiB9IGZyb20gXCIuLi9tdXRhdG9yc1wiO1xyXG5pbXBvcnQgeyBJUGFja2FnZSwgSUFjY291bnREZXRhaWxzLCBJU2VydmljZUFjY291bnRBUEkgfSBmcm9tIFwiLi4vbW9kZWxzXCI7XHJcblxyXG4vLyBXaWRnZXQgYWN0aW9uc1xyXG5leHBvcnQgY29uc3QgZ2V0QWNjb3VudERldGFpbHMgPSBjcmVhdGVBY3Rpb24oXCJHRVRfQUNDT1VOVF9ERVRBSUxTXCIpO1xyXG5leHBvcnQgY29uc3Qgc2V0QWNjb3VudERldGFpbHMgPSBjcmVhdGVBY3Rpb248QXJyYXk8SUFjY291bnREZXRhaWxzPj4oXCJTRVRfQUNDT1VOVF9ERVRBSUxTXCIsIHNlcnZpY2VBY2NvdW50TXV0YXRvckZuIGFzIGFueSkgYXMgKHJlc3BvbnNlOiBJU2VydmljZUFjY291bnRBUEkpID0+IEFjdGlvbjxBcnJheTxJQWNjb3VudERldGFpbHM+PjtcclxuZXhwb3J0IGNvbnN0IGdldEludGVybmV0Q2F0YWxvZyA9IGNyZWF0ZUFjdGlvbihcIkdFVF9JTlRFUk5FVF9DQVRBTE9HXCIpO1xyXG5leHBvcnQgY29uc3Qgc2V0SW50ZXJuZXRDYXRhbG9nID0gY3JlYXRlQWN0aW9uPEFycmF5PElQYWNrYWdlPj4oXCJTRVRfSU5URVJORVRfQ0FUQUxPR1wiLCBjYXRhbG9nTXV0YXRvckZuIGFzIGFueSkgYXMgKHJlc3BvbnNlOiBWb2x0LklBUElSZXNwb25zZSkgPT4gQWN0aW9uPEFycmF5PElQYWNrYWdlPj47XHJcblxyXG5leHBvcnQgY29uc3QgdG9nZ2xlUGFja2FnZVNlbGVjdGlvbiA9IGNyZWF0ZUFjdGlvbjxWb2x0LklIeXBlcm1lZGlhQWN0aW9uPihcIlRPR0dMRV9JTlRFUk5FVF9QQUNLQUdFXCIpO1xyXG5leHBvcnQgY29uc3QgdXBkYXRlSW50ZXJuZXRDYXRhbG9nID0gY3JlYXRlQWN0aW9uPEFycmF5PElQYWNrYWdlPj4oXCJVUERBVEVfSU5URVJORVRfQ0FUQUxPR1wiLCBvcmRlck11dGF0b3JGbiBhcyBhbnkpIGFzIChyZXNwb25zZTogVm9sdC5JQVBJUmVzcG9uc2UsIGNhdGFsb2c6IEFycmF5PElQYWNrYWdlPikgPT4gQWN0aW9uPEFycmF5PElQYWNrYWdlPj47XHJcblxyXG4vLyBQaXBlZCBhY3Rpb25zXHJcblxyXG4iLCJpbXBvcnQgeyBJbmplY3RhYmxlLCBDb21tb25GZWF0dXJlcyB9IGZyb20gXCJid3RrXCI7XHJcbmltcG9ydCB7IE1vZGVscyB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcblxyXG5jb25zdCB7IEJhc2VDb25maWcsIGNvbmZpZ1Byb3BlcnR5IH0gPSBDb21tb25GZWF0dXJlcztcclxuXHJcbmludGVyZmFjZSBJQXBwQ29uZmlnIGV4dGVuZHMgTW9kZWxzLklCYXNlQ29uZmlnIHtcclxufVxyXG5cclxuaW50ZXJmYWNlIElBcHBBUEkgZXh0ZW5kcyBNb2RlbHMuSUJhc2VXaWRnZXRBUEkge1xyXG4gIGNhdGFsb2dBUEk6IHN0cmluZztcclxuICBidW5kbGVDYXRhbG9nQVBJOiBzdHJpbmc7XHJcbiAgc2VydmljZUFjY291bnRBUEk6IHN0cmluZztcclxufVxyXG5cclxuLyoqXHJcbiAqIFdpZGdldCBjb25maWd1cmF0aW9uIHByb3ZpZGVyXHJcbiAqIEFsbG93cyB0aGUgZXh0ZXJuYWwgaW1tdXRhYmxlXHJcbiAqIGNvbmZpZyBzZXR0aW5nXHJcbiAqIEBleHBvcnRcclxuICogQGNsYXNzIENvbmZpZ1xyXG4gKiBAZXh0ZW5kcyB7QmFzZUNvbmZpZzxJQXBwQ29uZmlnPn1cclxuICovXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBDb25maWcgZXh0ZW5kcyBCYXNlQ29uZmlnPElBcHBDb25maWc+IHtcclxuICBAY29uZmlnUHJvcGVydHkoXCJcIikgZmxvd1R5cGU6IHN0cmluZztcclxuICBAY29uZmlnUHJvcGVydHkoe30pIGVudmlyb25tZW50VmFyaWFibGVzOiBhbnk7XHJcbiAgQGNvbmZpZ1Byb3BlcnR5KHt9KSBtb2NrZGF0YTogYW55O1xyXG4gIEBjb25maWdQcm9wZXJ0eSh7fSkgaGVhZGVyczogYW55O1xyXG4gIEBjb25maWdQcm9wZXJ0eSh7YmFzZTogXCJodHRwOi8vMTI3LjAuMC4xOjg4ODFcIn0pIGFwaTogSUFwcEFQSTtcclxufVxyXG4iLCJpbXBvcnQgeyBJbmplY3RhYmxlLCBBamF4U2VydmljZXMgfSBmcm9tIFwiYnd0a1wiO1xyXG5pbXBvcnQgeyBCYXNlQ2xpZW50IH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuXHJcbmltcG9ydCB7IENvbmZpZyB9IGZyb20gXCIuL0NvbmZpZ1wiO1xyXG5cclxuLyoqXHJcbiAqIEJhc2UgY2xpZW50IGltcGxlbWVudGF0aW9uXHJcbiAqIGZvciBBSkFYIGNhbGxzXHJcbiAqIEBleHBvcnRcclxuICogQGNsYXNzIENsaWVudFxyXG4gKiBAZXh0ZW5kcyB7QmFzZUNsaWVudH1cclxuICovXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBDbGllbnQgZXh0ZW5kcyBCYXNlQ2xpZW50IHtcclxuICBjb25zdHJ1Y3RvcihhamF4Q2xpZW50OiBBamF4U2VydmljZXMsIGNvbmZpZzogQ29uZmlnKSB7XHJcbiAgICBzdXBlcihhamF4Q2xpZW50LCBjb25maWcpO1xyXG4gIH1cclxufVxyXG4iLCJpbXBvcnQgeyBJbmplY3RhYmxlIH0gZnJvbSBcImJ3dGtcIjtcclxuaW1wb3J0IHsgRXBpYywgY29tYmluZUVwaWNzIH0gZnJvbSBcInJlZHV4LW9ic2VydmFibGVcIjtcclxuaW1wb3J0IHsgRVdpZGdldFN0YXR1cywgQWN0aW9ucywgTW9kZWxzLCBBamF4UmVzcG9uc2UsIFZvbHQsIEZpbHRlclJlc3RyaWN0aW9uT2JzZXJ2YWJsZSwgRVdpZGdldE5hbWUsIFV0aWxzLCBFRmxvd1R5cGUsIFZhbHVlT2YgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBmaWx0ZXIsIG1lcmdlTWFwLCBjYXRjaEVycm9yICwgY29uY2F0LCBvZiwgT2JzZXJ2YWJsZSB9IGZyb20gJ3J4anMnO1xyXG5cclxuaW1wb3J0IHsgQ2xpZW50IH0gZnJvbSBcIi4uLy4uL0NsaWVudFwiO1xyXG5pbXBvcnQge1xyXG4gIElTdG9yZVN0YXRlXHJcbn0gZnJvbSBcIi4uLy4uL21vZGVsc1wiO1xyXG5pbXBvcnQge1xyXG4gIGdldEludGVybmV0Q2F0YWxvZyxcclxuICBzZXRJbnRlcm5ldENhdGFsb2csXHJcbiAgdG9nZ2xlUGFja2FnZVNlbGVjdGlvbixcclxuICB1cGRhdGVJbnRlcm5ldENhdGFsb2dcclxufSBmcm9tIFwiLi4vQWN0aW9uc1wiO1xyXG5pbXBvcnQgeyBDb25maWcgfSBmcm9tIFwiLi4vLi4vQ29uZmlnXCI7XHJcblxyXG5jb25zdCB7XHJcbiAgZXJyb3JPY2N1cmVkLFxyXG4gIHNldFdpZGdldFN0YXR1cyxcclxuICBjbGVhckNhY2hlZFN0YXRlLFxyXG4gIGZpbmFsaXplUmVzdHJpY3Rpb25cclxufSA9IEFjdGlvbnM7XHJcblxyXG5ASW5qZWN0YWJsZVxyXG5leHBvcnQgY2xhc3MgQ2F0YWxvZ0VwaWNzIHtcclxuICB3aWRnZXRTdGF0ZTogRVdpZGdldFN0YXR1cyA9IEVXaWRnZXRTdGF0dXMuSU5JVDtcclxuXHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBjbGllbnQ6IENsaWVudCwgcHJpdmF0ZSBjb25maWc6IENvbmZpZykgeyB9XHJcblxyXG4gIGNvbWJpbmVFcGljcygpIHtcclxuICAgIHJldHVybiBjb21iaW5lRXBpY3MoXHJcbiAgICAgIHRoaXMucmVxdWVzdENhdGFsb2dFcGljLFxyXG4gICAgICB0aGlzLnRvZ2dsZVBsYW5TZWxlY3Rpb25FcGljLFxyXG4gICAgICB0aGlzLmZpbmFsaXplUmVzdHJpY3Rpb25FcGljXHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBnZXQgcmVxdWVzdENhdGFsb2dFcGljKCk6IENhdGFsb2dFcGljIHtcclxuICAgIHJldHVybiAoYWN0aW9uJDogT2JzZXJ2YWJsZTxJbnB1dEFjdGlvbj4pID0+XHJcbiAgICAgIGFjdGlvbiQucGlwZShcclxuICAgICAgICBmaWx0ZXIoKGFjdGlvbik6IGFjdGlvbiBpcyBJbnB1dEFjdGlvbiA9PiBhY3Rpb24udHlwZSA9PT0gZ2V0SW50ZXJuZXRDYXRhbG9nLnRvU3RyaW5nKCkpLFxyXG4gICAgICAgIGZpbHRlcigoKSA9PiB0aGlzLndpZGdldFN0YXRlICE9PSBFV2lkZ2V0U3RhdHVzLlVQREFUSU5HKSxcclxuICAgICAgICBtZXJnZU1hcCgoKSA9PiBjb25jYXQoXHJcbiAgICAgICAgICBvZihzZXRXaWRnZXRTdGF0dXModGhpcy53aWRnZXRTdGF0ZSA9IEVXaWRnZXRTdGF0dXMuVVBEQVRJTkcpKSxcclxuICAgICAgICAgIHRoaXMuY2xpZW50LmdldDxBamF4UmVzcG9uc2U8Vm9sdC5JQVBJUmVzcG9uc2U+PihVdGlscy5hcHBlbmRSZWZyZXNoT25jZShcclxuICAgICAgICAgICAgVXRpbHMuZ2V0VVJMQnlGbG93VHlwZSh7XHJcbiAgICAgICAgICAgICAgW0VGbG93VHlwZS5UVl06IHRoaXMuY29uZmlnLmFwaS5jYXRhbG9nQVBJLFxyXG4gICAgICAgICAgICAgIFtFRmxvd1R5cGUuSU5URVJORVRdOiB0aGlzLmNvbmZpZy5hcGkuY2F0YWxvZ0FQSSxcclxuICAgICAgICAgICAgICBbRUZsb3dUeXBlLkJVTkRMRV06IHRoaXMuY29uZmlnLmFwaS5idW5kbGVDYXRhbG9nQVBJXHJcbiAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICApKS5waXBlKFxyXG4gICAgICAgICAgICBtZXJnZU1hcCgocmVzcG9uc2U6IEFqYXhSZXNwb25zZTxWb2x0LklBUElSZXNwb25zZT4pID0+IFxyXG4gICAgICAgICAgICAgIEZpbHRlclJlc3RyaWN0aW9uT2JzZXJ2YWJsZShyZXNwb25zZSwgW1xyXG4gICAgICAgICAgICAgICAgc2V0SW50ZXJuZXRDYXRhbG9nKHJlc3BvbnNlLmRhdGEpLFxyXG4gICAgICAgICAgICAgICAgQWN0aW9ucy5vbW5pUGFnZUxvYWRlZCgpLFxyXG4gICAgICAgICAgICAgICAgc2V0V2lkZ2V0U3RhdHVzKHRoaXMud2lkZ2V0U3RhdGUgPSBFV2lkZ2V0U3RhdHVzLlJFTkRFUkVEKVxyXG4gICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgIClcclxuICAgICAgICApKSxcclxuICAgICAgICBjYXRjaEVycm9yKChlcnJvcjogUmVzcG9uc2UpID0+IG9mKFxyXG4gICAgICAgICAgZXJyb3JPY2N1cmVkKG5ldyBNb2RlbHMuRXJyb3JIYW5kbGVyKFwiZ2V0SW50ZXJuZXRDYXRhbG9nXCIsIGVycm9yKSlcclxuICAgICAgICApKVxyXG4gICAgICApIGFzIE9ic2VydmFibGU8T3V0cHV0QWN0aW9uPjtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgZ2V0IHRvZ2dsZVBsYW5TZWxlY3Rpb25FcGljKCk6IENhdGFsb2dFcGljIHtcclxuICAgIHJldHVybiAoYWN0aW9uJDogT2JzZXJ2YWJsZTxJbnB1dEFjdGlvbj4sIHN0YXRlJCkgPT5cclxuICAgICAgYWN0aW9uJC5waXBlKFxyXG4gICAgICAgIGZpbHRlcigoYWN0aW9uKTogYWN0aW9uIGlzIFJlZHV4QWN0aW9ucy5BY3Rpb248Vm9sdC5JSHlwZXJtZWRpYUFjdGlvbj4gPT4gXHJcbiAgICAgICAgICBhY3Rpb24udHlwZSA9PT0gdG9nZ2xlUGFja2FnZVNlbGVjdGlvbi50b1N0cmluZygpXHJcbiAgICAgICAgKSxcclxuICAgICAgICBmaWx0ZXIoKCkgPT4gdGhpcy53aWRnZXRTdGF0ZSAhPT0gRVdpZGdldFN0YXR1cy5VUERBVElORyksXHJcbiAgICAgICAgbWVyZ2VNYXAoKHsgcGF5bG9hZCB9KSA9PlxyXG4gICAgICAgICAgY29uY2F0KFxyXG4gICAgICAgICAgICBvZihzZXRXaWRnZXRTdGF0dXModGhpcy53aWRnZXRTdGF0ZSA9IEVXaWRnZXRTdGF0dXMuVVBEQVRJTkcpKSxcclxuICAgICAgICAgICAgdGhpcy5jbGllbnQuYWN0aW9uPEFqYXhSZXNwb25zZTxWb2x0LklBUElSZXNwb25zZT4+KHBheWxvYWQpLnBpcGUoXHJcbiAgICAgICAgICAgICAgbWVyZ2VNYXAoKHJlc3BvbnNlOiBBamF4UmVzcG9uc2U8Vm9sdC5JQVBJUmVzcG9uc2U+KSA9PiBcclxuICAgICAgICAgICAgICAgIEZpbHRlclJlc3RyaWN0aW9uT2JzZXJ2YWJsZShyZXNwb25zZSwgW1xyXG4gICAgICAgICAgICAgICAgICB1cGRhdGVJbnRlcm5ldENhdGFsb2cocmVzcG9uc2UuZGF0YSwgKHN0YXRlJCBhcyBhbnkpLnZhbHVlLmNhdGFsb2cpLFxyXG4gICAgICAgICAgICAgICAgICBjbGVhckNhY2hlZFN0YXRlKFtFV2lkZ2V0TmFtZS5QUkVWSUVXXSksXHJcbiAgICAgICAgICAgICAgICAgIHNldFdpZGdldFN0YXR1cyh0aGlzLndpZGdldFN0YXRlID0gRVdpZGdldFN0YXR1cy5SRU5ERVJFRClcclxuICAgICAgICAgICAgICAgIF0pXHJcbiAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICApXHJcbiAgICAgICAgKSxcclxuICAgICAgICBjYXRjaEVycm9yKChlcnJvcjogUmVzcG9uc2UpID0+IG9mKGVycm9yT2NjdXJlZChuZXcgTW9kZWxzLkVycm9ySGFuZGxlcihcInRvZ2dsZVBhY2thZ2VTZWxlY3Rpb25cIiwgZXJyb3IpKSkpXHJcbiAgICAgICkgYXMgT2JzZXJ2YWJsZTxPdXRwdXRBY3Rpb24+O1xyXG4gIH1cclxuXHJcbiAgcHJpdmF0ZSBnZXQgZmluYWxpemVSZXN0cmljdGlvbkVwaWMoKTogQ2F0YWxvZ0VwaWMge1xyXG4gICAgcmV0dXJuIChhY3Rpb24kOiBPYnNlcnZhYmxlPElucHV0QWN0aW9uPiwgc3RhdGUkKSA9PlxyXG4gICAgICBhY3Rpb24kLnBpcGUoXHJcbiAgICAgICAgZmlsdGVyKChhY3Rpb24pOiBhY3Rpb24gaXMgUmVkdXhBY3Rpb25zLkFjdGlvbjxWb2x0LklBUElSZXNwb25zZT4gPT4gXHJcbiAgICAgICAgICBhY3Rpb24udHlwZSA9PT0gZmluYWxpemVSZXN0cmljdGlvbi50b1N0cmluZygpXHJcbiAgICAgICAgKSxcclxuICAgICAgICBmaWx0ZXIoKHsgcGF5bG9hZCB9KSA9PiBcclxuICAgICAgICAgIEJvb2xlYW4ocGF5bG9hZCkgJiYgXHJcbiAgICAgICAgICBCb29sZWFuKHBheWxvYWQucHJvZHVjdE9mZmVyaW5nRGV0YWlsKSAmJiBcclxuICAgICAgICAgIHRoaXMud2lkZ2V0U3RhdGUgIT09IEVXaWRnZXRTdGF0dXMuVVBEQVRJTkdcclxuICAgICAgICApLFxyXG4gICAgICAgIG1lcmdlTWFwKCh7IHBheWxvYWQgfSkgPT4gb2YoXHJcbiAgICAgICAgICBBY3Rpb25zLmJyb2FkY2FzdFVwZGF0ZShBY3Rpb25zLnNldFByb2R1Y3RDb25maWd1cmF0aW9uVG90YWwoVmFsdWVPZihwYXlsb2FkLCBcInByb2R1Y3RPZmZlcmluZ0RldGFpbC5wcm9kdWN0Q29uZmlndXJhdGlvblRvdGFsXCIpKSksXHJcbiAgICAgICAgICB1cGRhdGVJbnRlcm5ldENhdGFsb2cocGF5bG9hZCwgKHN0YXRlJCBhcyBhbnkpLnZhbHVlLmNhdGFsb2cpLFxyXG4gICAgICAgICAgY2xlYXJDYWNoZWRTdGF0ZShbRVdpZGdldE5hbWUuUFJFVklFV10pLFxyXG4gICAgICAgICAgc2V0V2lkZ2V0U3RhdHVzKHRoaXMud2lkZ2V0U3RhdGUgPSBFV2lkZ2V0U3RhdHVzLlJFTkRFUkVEKVxyXG4gICAgICAgICkpXHJcbiAgICAgICkgYXMgT2JzZXJ2YWJsZTxPdXRwdXRBY3Rpb24+O1xyXG4gIH1cclxufVxyXG5cclxudHlwZSBJbnB1dEFjdGlvbiA9IFJlZHV4QWN0aW9ucy5BY3Rpb248YW55PjtcclxudHlwZSBPdXRwdXRBY3Rpb24gPSBSZWR1eEFjdGlvbnMuQWN0aW9uPGFueT47XHJcbnR5cGUgQ2F0YWxvZ0VwaWMgPSBFcGljPElucHV0QWN0aW9uLCBPdXRwdXRBY3Rpb24sIHZvaWQsIElTdG9yZVN0YXRlPjtcclxuIiwiaW1wb3J0IHsgSW5qZWN0YWJsZSB9IGZyb20gXCJid3RrXCI7XHJcbmltcG9ydCB7IEVwaWMsIGNvbWJpbmVFcGljcywgb2ZUeXBlIH0gZnJvbSBcInJlZHV4LW9ic2VydmFibGVcIjtcclxuaW1wb3J0IHsgRVdpZGdldFN0YXR1cywgQWpheFJlc3BvbnNlIH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuaW1wb3J0IHsgZmlsdGVyLCBtZXJnZU1hcCwgY2F0Y2hFcnJvciAsIG9mIH0gZnJvbSAncnhqcyc7XHJcblxyXG5pbXBvcnQgeyBDbGllbnQgfSBmcm9tIFwiLi4vLi4vQ2xpZW50XCI7XHJcbmltcG9ydCB7XHJcbiAgSVN0b3JlU3RhdGUsIElTZXJ2aWNlQWNjb3VudEFQSSxcclxufSBmcm9tIFwiLi4vLi4vbW9kZWxzXCI7XHJcbmltcG9ydCB7XHJcbiAgZ2V0QWNjb3VudERldGFpbHMsXHJcbiAgc2V0QWNjb3VudERldGFpbHMsXHJcbiAgZ2V0SW50ZXJuZXRDYXRhbG9nLFxyXG59IGZyb20gXCIuLi9BY3Rpb25zXCI7XHJcbmltcG9ydCB7IENvbmZpZyB9IGZyb20gXCIuLi8uLi9Db25maWdcIjtcclxuaW1wb3J0IHsgQWN0aW9uIH0gZnJvbSBcInJlZHV4LWFjdGlvbnNcIjtcclxuXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBVc2VyQWNjb3VudEVwaWNzIHtcclxuICB3aWRnZXRTdGF0ZTogRVdpZGdldFN0YXR1cyA9IEVXaWRnZXRTdGF0dXMuSU5JVDtcclxuXHJcbiAgY29uc3RydWN0b3IocHJpdmF0ZSBjbGllbnQ6IENsaWVudCwgcHJpdmF0ZSBjb25maWc6IENvbmZpZykgeyB9XHJcblxyXG4gIGNvbWJpbmVFcGljcygpIHtcclxuICAgIHJldHVybiBjb21iaW5lRXBpY3MoXHJcbiAgICAgIHRoaXMucmVxdWVzdERhdGFFcGljLFxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgZ2V0IHJlcXVlc3REYXRhRXBpYygpOiBFcGljPEFjdGlvbjxhbnk+LCBBY3Rpb248YW55PiwgYW55LCBJU3RvcmVTdGF0ZT4ge1xyXG4gICAgcmV0dXJuIChhY3Rpb24kLCBzdGF0ZSQpID0+XHJcbiAgICAgIGFjdGlvbiQucGlwZShcclxuICAgICAgICBvZlR5cGUoZ2V0QWNjb3VudERldGFpbHMudG9TdHJpbmcoKSksXHJcbiAgICAgICAgZmlsdGVyKCgpID0+IHRoaXMud2lkZ2V0U3RhdGUgIT09IEVXaWRnZXRTdGF0dXMuVVBEQVRJTkcpLFxyXG4gICAgICAgIG1lcmdlTWFwKCgpID0+IHRoaXMuY2xpZW50LmdldDxBamF4UmVzcG9uc2U8SVNlcnZpY2VBY2NvdW50QVBJPj4odGhpcy5jb25maWcuYXBpLnNlcnZpY2VBY2NvdW50QVBJKS5waXBlKFxyXG4gICAgICAgICAgbWVyZ2VNYXAoKHsgZGF0YSB9OiB7IGRhdGE6IElTZXJ2aWNlQWNjb3VudEFQSSB9KSA9PiBvZihcclxuICAgICAgICAgICAgc2V0QWNjb3VudERldGFpbHMoZGF0YSksXHJcbiAgICAgICAgICAgIGdldEludGVybmV0Q2F0YWxvZygpXHJcbiAgICAgICAgICApKSxcclxuICAgICAgICAgIGNhdGNoRXJyb3IoKGVycm9yOiBSZXNwb25zZSkgPT4gb2Yoc2V0QWNjb3VudERldGFpbHMoe30gYXMgSVNlcnZpY2VBY2NvdW50QVBJKSkpXHJcbiAgICAgICAgKSlcclxuICAgICAgKTtcclxuICB9XHJcbn1cclxuIiwiaW1wb3J0IHsgSW5qZWN0YWJsZSB9IGZyb20gXCJid3RrXCI7XHJcbmltcG9ydCB7IEVwaWMsIGNvbWJpbmVFcGljcywgb2ZUeXBlIH0gZnJvbSBcInJlZHV4LW9ic2VydmFibGVcIjtcclxuaW1wb3J0IHsgRVdpZGdldFN0YXR1cywgT21uaXR1cmUsIEFjdGlvbnMsIFZhbHVlT2YgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBmaWx0ZXIsIG1lcmdlTWFwLCBjYXRjaEVycm9yICwgb2YsIE9ic2VydmFibGUgfSBmcm9tICdyeGpzJztcclxuXHJcbmltcG9ydCB7XHJcbiAgSVN0b3JlU3RhdGVcclxufSBmcm9tIFwiLi4vLi4vbW9kZWxzXCI7XHJcblxyXG5jb25zdCB7XHJcbiAgb21uaVBhZ2VMb2FkZWQsXHJcbiAgb21uaVBhZ2VTdWJtaXRcclxufSA9IEFjdGlvbnM7XHJcblxyXG50eXBlIElucHV0QWN0aW9uID0gUmVkdXhBY3Rpb25zLkFjdGlvbjxhbnk+O1xyXG50eXBlIE91dHB1dEFjdGlvbiA9IFJlZHV4QWN0aW9ucy5BY3Rpb248YW55PjtcclxudHlwZSBPbW5pdHVyZUVwaWMgPSBFcGljPElucHV0QWN0aW9uLCBPdXRwdXRBY3Rpb24sIHZvaWQsIElTdG9yZVN0YXRlPjtcclxuXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBPbW5pdHVyZUVwaWNzIHtcclxuICB3aWRnZXRTdGF0ZTogRVdpZGdldFN0YXR1cyA9IEVXaWRnZXRTdGF0dXMuSU5JVDtcclxuXHJcbiAgY29tYmluZUVwaWNzKCkge1xyXG4gICAgcmV0dXJuIGNvbWJpbmVFcGljcyhcclxuICAgICAgdGhpcy5wYWdlTG9hZGVkRXBpYyxcclxuICAgICAgdGhpcy5wYWdlU3VibWl0RXBpY1xyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAgICogVGhlIG9ubHkgT21uaXR1cmUgZGVwZW5kZWN5IGZvciBwYWdlbG9hZFxyXG4gICAgICogb24gSW50ZXJuZXQgY2hhbmdlcGFja2FnZSBwYWdlIGlzIGFjY291bnQgZGV0YWlsc1xyXG4gICAgICogc28gd2Ugd2FpdCBmb3IgdGhvc2UgdG8gY29tZSBmcm9tIEFQSSBhbmQgdGhlblxyXG4gICAgICogZmlyZSB0aGUgYmVha29uXHJcbiAgICAgKiBAcmVhZG9ubHlcclxuICAgICAqIEBwcml2YXRlXHJcbiAgICAgKiBAdHlwZSB7VXNlckFjY291bnRFcGljfVxyXG4gICAgICogQG1lbWJlcm9mIE9tbml0dXJlRXBpY3NcclxuICAgICAqL1xyXG4gIHByaXZhdGUgZ2V0IHBhZ2VMb2FkZWRFcGljKCk6IE9tbml0dXJlRXBpYyB7XHJcbiAgICByZXR1cm4gKGFjdGlvbiQ6IE9ic2VydmFibGU8SW5wdXRBY3Rpb24+LCBzdGF0ZSQpID0+XHJcbiAgICAgIGFjdGlvbiQucGlwZShcclxuICAgICAgICBvZlR5cGUob21uaVBhZ2VMb2FkZWQudG9TdHJpbmcoKSksXHJcbiAgICAgICAgbWVyZ2VNYXAoKCkgPT4ge1xyXG4gICAgICAgICAgY29uc3QgeyBhY2NvdW50RGV0YWlscyB9ID0gKHN0YXRlJCBhcyBhbnkpLnZhbHVlO1xyXG4gICAgICAgICAgY29uc3Qgb21uaXR1cmUgPSBPbW5pdHVyZS51c2VPbW5pdHVyZSgpO1xyXG4gICAgICAgICAgb21uaXR1cmUudHJhY2tGcmFnbWVudCh7XHJcbiAgICAgICAgICAgIGlkOiBcIkludGVybmV0UGFnZVwiLFxyXG4gICAgICAgICAgICBzX29TUzE6IFwiflwiLFxyXG4gICAgICAgICAgICBzX29TUzI6IFwiflwiLFxyXG4gICAgICAgICAgICBzX29TUzM6IFwiflwiLFxyXG4gICAgICAgICAgICBzX29QR046IFwiflwiLFxyXG4gICAgICAgICAgICBzX29BUFQ6IHtcclxuICAgICAgICAgICAgICBhY3Rpb25yZXN1bHQ6IDFcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgc19vUExFOiB7XHJcbiAgICAgICAgICAgICAgdHlwZTogT21uaXR1cmUuRU1lc3NhZ2VUeXBlLkluZm9ybWF0aW9uLFxyXG4gICAgICAgICAgICAgIGNvbnRlbnQ6IFZhbHVlT2YoYWNjb3VudERldGFpbHMsIFwiMC5OYW1lXCIsIFwiXCIpXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0pO1xyXG4gICAgICAgICAgcmV0dXJuIG9mKCk7XHJcbiAgICAgICAgfSksXHJcbiAgICAgICAgY2F0Y2hFcnJvcigoZXJyb3I6IFJlc3BvbnNlKSA9PiBvZigpKVxyXG4gICAgICApIGFzIE9ic2VydmFibGU8T3V0cHV0QWN0aW9uPjtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgZ2V0IHBhZ2VTdWJtaXRFcGljKCk6IE9tbml0dXJlRXBpYyB7XHJcbiAgICByZXR1cm4gKGFjdGlvbiQ6IE9ic2VydmFibGU8SW5wdXRBY3Rpb24+LCBzdGF0ZSQpID0+XHJcbiAgICAgIGFjdGlvbiQucGlwZShcclxuICAgICAgICBmaWx0ZXIoKGFjdGlvbikgPT4gYWN0aW9uLnR5cGUgPT09IG9tbmlQYWdlU3VibWl0LnRvU3RyaW5nKCkpLFxyXG4gICAgICAgIG1lcmdlTWFwKCgpID0+IHtcclxuICAgICAgICAgIGNvbnN0IHsgY2F0YWxvZyB9ID0gKHN0YXRlJCBhcyBhbnkpLnZhbHVlO1xyXG4gICAgICAgICAgY29uc3Qgb21uaXR1cmUgPSBPbW5pdHVyZS51c2VPbW5pdHVyZSgpO1xyXG4gICAgICAgICAgb21uaXR1cmUudHJhY2tBY3Rpb24oe1xyXG4gICAgICAgICAgICBpZDogXCJpbnRlcm5ldFBhZ2VTdWJtaXRcIixcclxuICAgICAgICAgICAgc19vQVBUOiB7XHJcbiAgICAgICAgICAgICAgYWN0aW9uSWQ6IDY0N1xyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICBzX29CVE46IFwiQ29udGludWVcIixcclxuICAgICAgICAgICAgc19vUFJEOiBjYXRhbG9nXHJcbiAgICAgICAgICAgICAgLmZpbHRlcigocGtnOiB7IGlzU2VsZWN0ZWQ6IGJvb2xlYW47IGlzQ3VycmVudDogYm9vbGVhbiB9KSA9PiAocGtnLmlzU2VsZWN0ZWQgJiYgIXBrZy5pc0N1cnJlbnQpKVxyXG4gICAgICAgICAgICAgIC5tYXAoXHJcbiAgICAgICAgICAgICAgICAocGtnOiB7IG5hbWU6IHN0cmluZyB9KSA9PiAoe1xyXG4gICAgICAgICAgICAgICAgICBjYXRlZ29yeTogXCJJbnRlcm5ldFwiLFxyXG4gICAgICAgICAgICAgICAgICBuYW1lOiBwa2cubmFtZSxcclxuICAgICAgICAgICAgICAgICAgc2t1OiBcIlwiLFxyXG4gICAgICAgICAgICAgICAgICBxdWFudGl0eTogXCIxXCIsXHJcbiAgICAgICAgICAgICAgICAgIHByaWNlOiBWYWx1ZU9mPHN0cmluZz4ocGtnLCBcInJlZ3VsYXJQcmljZS5wcmljZVwiLCBcIjBcIiksXHJcbiAgICAgICAgICAgICAgICAgIHByb21vOiBWYWx1ZU9mPHN0cmluZz4ocGtnLCBcInByb21vdGlvbkRldGFpbHMucHJvbW90aW9uYWxQcmljZS5wcmljZVwiLCBcIlwiKVxyXG4gICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICApXHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICAgIHJldHVybiBvZigpO1xyXG4gICAgICAgIH0pLFxyXG4gICAgICAgIGNhdGNoRXJyb3IoKGVycm9yOiBSZXNwb25zZSkgPT4gb2YoKSlcclxuICAgICAgKSBhcyBPYnNlcnZhYmxlPE91dHB1dEFjdGlvbj47XHJcbiAgfVxyXG59XHJcbiIsImltcG9ydCB7IEluamVjdGFibGUgfSBmcm9tIFwiYnd0a1wiO1xyXG5pbXBvcnQgeyBFcGljLCBjb21iaW5lRXBpY3MsIG9mVHlwZSB9IGZyb20gXCJyZWR1eC1vYnNlcnZhYmxlXCI7XHJcbmltcG9ydCB7IGZpbHRlciwgbWVyZ2VNYXAgfSBmcm9tIFwicnhqc1wiO1xyXG5pbXBvcnQgeyBFV2lkZ2V0U3RhdHVzLCBBY3Rpb25zLCBPbW5pdHVyZSwgVXRpbHMsIEVGbG93VHlwZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcblxyXG5pbXBvcnQgeyBJU3RvcmVTdGF0ZSB9IGZyb20gXCIuLi9tb2RlbHNcIjtcclxuaW1wb3J0IHsgZ2V0QWNjb3VudERldGFpbHMgfSBmcm9tIFwiLi9BY3Rpb25zXCI7XHJcbmltcG9ydCB7IENhdGFsb2dFcGljcyB9IGZyb20gXCIuL0VwaWNzL0NhdGFsb2dcIjtcclxuaW1wb3J0IHsgVXNlckFjY291bnRFcGljcyB9IGZyb20gXCIuL0VwaWNzL1VzZXJBY2NvdW50XCI7XHJcbmltcG9ydCB7IE9tbml0dXJlRXBpY3MgfSBmcm9tIFwiLi9FcGljcy9PbW5pdHVyZVwiO1xyXG5cclxuY29uc3Qge1xyXG4gIHNldFdpZGdldFN0YXR1c1xyXG59ID0gQWN0aW9ucztcclxuXHJcbi8vIGNvbnN0IHsgY29uY2F0IH0gPSBBY3Rpb25zT2JzZXJ2YWJsZTtcclxuXHJcbkBJbmplY3RhYmxlXHJcbmV4cG9ydCBjbGFzcyBFcGljcyB7XHJcbiAgY29uc3RydWN0b3IoXHJcbiAgICBwdWJsaWMgY2F0YWxvZ0VwaWNzOiBDYXRhbG9nRXBpY3MsXHJcbiAgICBwdWJsaWMgdXNlckFjY291bnRFcGljczogVXNlckFjY291bnRFcGljcyxcclxuICAgIHB1YmxpYyBvbW5pdHVyZTogT21uaXR1cmVFcGljc1xyXG4gICkgeyB9XHJcblxyXG4gIGNvbWJpbmVFcGljcygpIHtcclxuICAgIHJldHVybiBjb21iaW5lRXBpY3MoXHJcbiAgICAgIHRoaXMub25XaWRnZXRTdGF0dXNFcGljLFxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHByaXZhdGUgZ2V0IG9uV2lkZ2V0U3RhdHVzRXBpYygpOiBHZW5lcmFsRXBpYyB7XHJcbiAgICByZXR1cm4gKGFjdGlvbiQpID0+XHJcbiAgICAgIGFjdGlvbiQucGlwZShcclxuICAgICAgICBvZlR5cGUoc2V0V2lkZ2V0U3RhdHVzLnRvU3RyaW5nKCkpLFxyXG4gICAgICAgIGZpbHRlcigoYWN0aW9uOiBSZWR1eEFjdGlvbnMuQWN0aW9uPEVXaWRnZXRTdGF0dXM+KSA9PiBhY3Rpb24ucGF5bG9hZCA9PT0gRVdpZGdldFN0YXR1cy5JTklUKSxcclxuICAgICAgICBtZXJnZU1hcCgoKSA9PiB7XHJcbiAgICAgICAgICBsZXQgYWN0aW9uLCBzX29TUzIgPSBcIn5cIjtcclxuICAgICAgICAgIHN3aXRjaCAoVXRpbHMuZ2V0Rmxvd1R5cGUoKSkge1xyXG4gICAgICAgICAgICBjYXNlIEVGbG93VHlwZS5JTlRFUk5FVDpcclxuICAgICAgICAgICAgICBhY3Rpb24gPSA1MjM7XHJcbiAgICAgICAgICAgICAgc19vU1MyID0gXCJJbnRlcm5ldFwiO1xyXG4gICAgICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgICAgICBjYXNlIEVGbG93VHlwZS5CVU5ETEU6XHJcbiAgICAgICAgICAgICAgc19vU1MyID0gXCJCdW5kbGVcIjtcclxuICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgICAgICAvLyBVc2UgZGVmYXVsdCB2YWx1ZXMgZm9yIG90aGVyIGZsb3cgdHlwZXNcclxuICAgICAgICAgICAgICBicmVhaztcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIE9tbml0dXJlLnVzZU9tbml0dXJlKCkudXBkYXRlQ29udGV4dCh7XHJcbiAgICAgICAgICAgIHNfb1NTMTogXCJ+XCIsIHNfb1NTMixcclxuICAgICAgICAgICAgc19vU1MzOiBcIkNoYW5nZSBwYWNrYWdlXCIsXHJcbiAgICAgICAgICAgIHNfb1BHTjogXCJTZXR1cCB5b3VyIHNlcnZpY2VcIixcclxuICAgICAgICAgICAgc19vQVBUOiB7XHJcbiAgICAgICAgICAgICAgYWN0aW9uSWQ6IGFjdGlvblxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICB9KTtcclxuICAgICAgICAgIHJldHVybiBbXHJcbiAgICAgICAgICAgIGdldEFjY291bnREZXRhaWxzKClcclxuICAgICAgICAgIF07XHJcbiAgICAgICAgfSkpO1xyXG4gIH1cclxuXHJcbn1cclxuXHJcbnR5cGUgR2VuZXJhbEVwaWMgPSBFcGljPFJlZHV4QWN0aW9ucy5BY3Rpb248YW55PiwgUmVkdXhBY3Rpb25zLkFjdGlvbjxhbnk+LCB2b2lkLCBJU3RvcmVTdGF0ZT47XHJcbiIsImltcG9ydCB7IEluamVjdGFibGUsIENvbW1vbkZlYXR1cmVzLCBTZXJ2aWNlTG9jYXRvciwgQ29tbW9uU2VydmljZXMgfSBmcm9tIFwiYnd0a1wiO1xyXG5pbXBvcnQgeyBFV2lkZ2V0TmFtZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcblxyXG5jb25zdCB7IEJhc2VMb2NhbGl6YXRpb24gfSA9IENvbW1vbkZlYXR1cmVzO1xyXG5cclxuQEluamVjdGFibGVcclxuZXhwb3J0IGNsYXNzIExvY2FsaXphdGlvbiBleHRlbmRzIEJhc2VMb2NhbGl6YXRpb24ge1xyXG4gIHN0YXRpYyBJbnN0YW5jZSA9IG51bGw7XHJcbiAgc3RhdGljIGdldExvY2FsaXplZFN0cmluZyhpZDogc3RyaW5nKTogc3RyaW5nIHtcclxuICAgIExvY2FsaXphdGlvbi5JbnN0YW5jZSA9IExvY2FsaXphdGlvbi5JbnN0YW5jZSB8fCBTZXJ2aWNlTG9jYXRvci5pbnN0YW5jZS5nZXRTZXJ2aWNlKENvbW1vblNlcnZpY2VzLkxvY2FsaXphdGlvbik7XHJcbiAgICBjb25zdCBpbnN0YW5jZTogYW55ID0gTG9jYWxpemF0aW9uLkluc3RhbmNlO1xyXG4gICAgcmV0dXJuIGluc3RhbmNlID8gaW5zdGFuY2UuZ2V0TG9jYWxpemVkU3RyaW5nKEVXaWRnZXROYW1lLklOVEVSTkVULCBpZCwgaW5zdGFuY2UubG9jYWxlKSA6IGlkO1xyXG4gIH1cclxufVxyXG4iLCJpbXBvcnQgeyBjb21iaW5lUmVkdWNlcnMgfSBmcm9tIFwicmVkdXhcIjtcclxuaW1wb3J0IHsgQWN0aW9uLCBoYW5kbGVBY3Rpb25zIH0gZnJvbSBcInJlZHV4LWFjdGlvbnNcIjtcclxuaW1wb3J0IHsgY29tYmluZUVwaWNzIH0gZnJvbSBcInJlZHV4LW9ic2VydmFibGVcIjtcclxuaW1wb3J0IHsgUmVkdWNlcnMsIExpZmVjeWNsZUVwaWNzLCBSZXN0cmljaXRvbnNFcGljcywgTW9kYWxFcGljcyB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcblxyXG5pbXBvcnQgeyBTdG9yZSBhcyBCd3RrU3RvcmUsIEluamVjdGFibGUsIENvbW1vbkZlYXR1cmVzIH0gZnJvbSBcImJ3dGtcIjtcclxuXHJcbmltcG9ydCAqIGFzIGFjdGlvbnMgZnJvbSBcIi4vQWN0aW9uc1wiO1xyXG5cclxuaW1wb3J0IHsgSVN0b3JlU3RhdGUsIElBY2NvdW50RGV0YWlscywgSVBhY2thZ2UgfSBmcm9tIFwiLi4vbW9kZWxzXCI7XHJcbmltcG9ydCB7IEVwaWNzIH0gZnJvbSBcIi4vRXBpY3NcIjtcclxuaW1wb3J0IHsgTG9jYWxpemF0aW9uIH0gZnJvbSBcIi4uL0xvY2FsaXphdGlvblwiO1xyXG5pbXBvcnQgeyBDbGllbnQgfSBmcm9tIFwiLi4vQ2xpZW50XCI7XHJcblxyXG5jb25zdCB7IEJhc2VTdG9yZSwgYWN0aW9uc1RvQ29tcHV0ZWRQcm9wZXJ0eU5hbWUgfSA9IENvbW1vbkZlYXR1cmVzO1xyXG5jb25zdCB7XHJcbiAgc2V0QWNjb3VudERldGFpbHMsXHJcbiAgc2V0SW50ZXJuZXRDYXRhbG9nLFxyXG4gIHVwZGF0ZUludGVybmV0Q2F0YWxvZ1xyXG59ID0gYWN0aW9uc1RvQ29tcHV0ZWRQcm9wZXJ0eU5hbWUoYWN0aW9ucyk7XHJcblxyXG5ASW5qZWN0YWJsZVxyXG5leHBvcnQgY2xhc3MgU3RvcmUgZXh0ZW5kcyBCYXNlU3RvcmU8SVN0b3JlU3RhdGU+IHtcclxuICBjb25zdHJ1Y3Rvcihwcml2YXRlIGNsaWVudDogQ2xpZW50LCBzdG9yZTogQnd0a1N0b3JlLCBwcml2YXRlIGVwaWNzOiBFcGljcywgcHJpdmF0ZSBsb2NhbGl6YXRpb246IExvY2FsaXphdGlvbikge1xyXG4gICAgc3VwZXIoc3RvcmUpO1xyXG4gIH1cclxuXHJcbiAgZ2V0IHJlZHVjZXIoKSB7XHJcbiAgICByZXR1cm4gY29tYmluZVJlZHVjZXJzKHtcclxuICAgICAgLy8gPT09PT09PT09PT0gV2lkZ2V0IGxpZmVjeWNsZSBtZXRob2RzID09PT09PT09PT09PT1cclxuICAgICAgLi4uUmVkdWNlcnMuV2lkZ2V0QmFzZUxpZmVjeWNsZSh0aGlzLmxvY2FsaXphdGlvbikgYXMgYW55LFxyXG4gICAgICAuLi5SZWR1Y2Vycy5XaWRnZXRMaWdodGJveGVzKCkgYXMgYW55LFxyXG4gICAgICAuLi5SZWR1Y2Vycy5XaWRnZXRSZXN0cmljdGlvbnMoKSBhcyBhbnksXHJcbiAgICAgIC8vID09PT09PT09PT09IFdpZGdldCBkYXRhID09PT09PT09PT09PT09PVxyXG4gICAgICBhY2NvdW50RGV0YWlsczogaGFuZGxlQWN0aW9uczxBcnJheTxJQWNjb3VudERldGFpbHM+Pih7XHJcbiAgICAgICAgW3NldEFjY291bnREZXRhaWxzXTogKHN0YXRlLCB7IHBheWxvYWQgfTogQWN0aW9uPEFycmF5PElBY2NvdW50RGV0YWlscz4+KSA9PiBwYXlsb2FkIHx8IHN0YXRlLFxyXG4gICAgICB9LCBbe31dIGFzIEFycmF5PElBY2NvdW50RGV0YWlscz4pLFxyXG4gICAgICBjYXRhbG9nOiBoYW5kbGVBY3Rpb25zPEFycmF5PElQYWNrYWdlPj4oe1xyXG4gICAgICAgIFtzZXRJbnRlcm5ldENhdGFsb2ddOiAoc3RhdGUsIHsgcGF5bG9hZCB9OiBBY3Rpb248QXJyYXk8SVBhY2thZ2U+PikgPT4gcGF5bG9hZCB8fCBzdGF0ZSxcclxuICAgICAgICBbdXBkYXRlSW50ZXJuZXRDYXRhbG9nXTogKHN0YXRlLCB7IHBheWxvYWQgfTogQWN0aW9uPEFycmF5PElQYWNrYWdlPj4pID0+IHBheWxvYWQgfHwgc3RhdGUsXHJcbiAgICAgIH0sIFtdKSxcclxuICAgIH0pIGFzIGFueTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIE1pZGRsZXdhcmVzIGFyZSBjb2xsZWN0ZWQgYm90dG9tLXRvLXRvcFxyXG4gICAqIHNvLCB0aGUgYm90dG9tLW1vc3QgZXBpYyB3aWxsIHJlY2VpdmUgdGhlXHJcbiAgICogYWN0aW9uIGZpcnN0LCB3aGlsZSB0aGUgdG9wLW1vc3QgLS0gbGFzdFxyXG4gICAqIEByZWFkb25seVxyXG4gICAqIEBtZW1iZXJvZiBTdG9yZVxyXG4gICAqL1xyXG4gIGdldCBtaWRkbGV3YXJlcygpOiBhbnkge1xyXG4gICAgcmV0dXJuIGNvbWJpbmVFcGljcyh0aGlzLmVwaWNzLm9tbml0dXJlLmNvbWJpbmVFcGljcygpLCB0aGlzLmVwaWNzLnVzZXJBY2NvdW50RXBpY3MuY29tYmluZUVwaWNzKCksXHJcbiAgICAgIHRoaXMuZXBpY3MuY2F0YWxvZ0VwaWNzLmNvbWJpbmVFcGljcygpLCB0aGlzLmVwaWNzLmNvbWJpbmVFcGljcygpLCBuZXcgTW9kYWxFcGljcygpLmNvbWJpbmVFcGljcygpLFxyXG4gICAgICBuZXcgUmVzdHJpY2l0b25zRXBpY3ModGhpcy5jbGllbnQsIFwiSU5URVJORVRfUkVTVFJJQ1RJT05fTU9EQUxcIikuY29tYmluZUVwaWNzKCksIG5ldyBMaWZlY3ljbGVFcGljcygpLmNvbWJpbmVFcGljcygpKTtcclxuICB9XHJcbn1cclxuIiwiZXhwb3J0ICogZnJvbSBcIi4vU3RvcmVcIjtcclxuZXhwb3J0ICogZnJvbSBcIi4vQWN0aW9uc1wiO1xyXG4iLCJpbXBvcnQgeyBDb21tb25GZWF0dXJlcyB9IGZyb20gXCJid3RrXCI7XHJcbi8vIGltcG9ydCB7IEFjdGlvbiB9IGZyb20gXCJyZWR1eC1hY3Rpb25zXCI7XHJcbmltcG9ydCB7IEFjdGlvbnMgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgeyBTdG9yZSB9IGZyb20gXCIuL3N0b3JlXCI7XHJcblxyXG5jb25zdCB7IEJhc2VQaXBlIH0gPSBDb21tb25GZWF0dXJlcztcclxuXHJcbi8qKlxyXG4gKiByeGpzIHBpcGUgcHJvdmlkZXJcclxuICogdGhpcyBmYXNjaWxpdGF0ZXMgdGhlIGRpcmVjdCBjb25uZWN0aW9uXHJcbiAqIGJldHdlZW4gd2lkZ2V0cyB0aHJvdWdoIHJ4anMgT2JzZXJ2YWJsZVxyXG4gKiBAZXhwb3J0XHJcbiAqIEBjbGFzcyBQaXBlXHJcbiAqIEBleHRlbmRzIHtCYXNlUGlwZX1cclxuICovXHJcbmV4cG9ydCBjbGFzcyBQaXBlIGV4dGVuZHMgQmFzZVBpcGUge1xyXG4gIHN0YXRpYyBTdWJzY3JpcHRpb25zKHN0b3JlOiBTdG9yZSkge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgW0FjdGlvbnMub25Db250aW51ZS50b1N0cmluZygpXTogKCkgPT4ge1xyXG4gICAgICAgIHN0b3JlLmRpc3BhdGNoKEFjdGlvbnMub21uaVBhZ2VTdWJtaXQoKSk7XHJcbiAgICAgICAgQWN0aW9ucy5icm9hZGNhc3RVcGRhdGUoQWN0aW9ucy5oaXN0b3J5Rm9yd2FyZCgpKTtcclxuICAgICAgfSxcclxuICAgICAgW0FjdGlvbnMub21uaVBhZ2VTdWJtaXQudG9TdHJpbmcoKV06ICgpID0+IHtcclxuICAgICAgICBBY3Rpb25zLmJyb2FkY2FzdFVwZGF0ZShBY3Rpb25zLm9tbmlQYWdlU3VibWl0KCkpO1xyXG4gICAgICB9XHJcbiAgICB9O1xyXG4gIH1cclxuICAvKipcclxuICAgICAqQ3JlYXRlcyBhIHN0YXRpYyBpbnN0YW5jZSBvZiBQaXBlLlxyXG4gICAgICogQHBhcmFtIHsqfSBhcmdcclxuICAgICAqIEBtZW1iZXJvZiBQaXBlXHJcbiAgICAgKi9cclxuICBzdGF0aWMgaW5zdGFuY2U6IFBpcGU7XHJcbiAgY29uc3RydWN0b3IoYXJnOiBhbnkpIHtcclxuICAgIHN1cGVyKGFyZyk7XHJcbiAgICBQaXBlLmluc3RhbmNlID0gdGhpcztcclxuICB9XHJcbn1cclxuIiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IGNvbm5lY3QgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcclxuaW1wb3J0IHsgRm9ybWF0dGVkTWVzc2FnZSwgRm9ybWF0dGVkRGF0ZSB9IGZyb20gXCJyZWFjdC1pbnRsXCI7XHJcbmltcG9ydCB7IFZhbHVlT2YsIENvbXBvbmVudHMsIE9tbml0dXJlIH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuaW1wb3J0IHsgSVN0b3JlU3RhdGUsIElBY2NvdW50RGV0YWlscyB9IGZyb20gXCIuLi8uLi9tb2RlbHNcIjtcclxuXHJcbmludGVyZmFjZSBJQ29tcG9uZW50UHJvcHMge1xyXG4gIGFjY291bnREZXRhaWxzOiBBcnJheTxJQWNjb3VudERldGFpbHM+O1xyXG59XHJcblxyXG5jb25zdCBDb21wb25lbnQ6IFJlYWN0LkZDPElDb21wb25lbnRQcm9wcz4gPSAoeyBhY2NvdW50RGV0YWlscyB9KSA9PiB7XHJcbiAgY29uc3QgW2V4cGFuZGVkLCB0b2dnbGVTdGF0ZV0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XHJcbiAgLy8gT21uaXR1cmUgdHJhY2tpbmcgZm9yIGV4cGFuZCBJbnRlcmVudCBwYWNrYWdlXHJcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcclxuICAgIC8vIHdlIG9ubHkgY2FyZSBhYm91dCB3aGVuIHRoZSBtZW51IGV4cGFuZHNcclxuICAgIGlmIChleHBhbmRlZCkge1xyXG4gICAgICBPbW5pdHVyZS51c2VPbW5pdHVyZSgpLnRyYWNrQWN0aW9uKHtcclxuICAgICAgICBpZDogXCJteUN1cnJlbnRQYWNrYWdlQ2xpY2tcIixcclxuICAgICAgICBzX29BUFQ6IHtcclxuICAgICAgICAgIGFjdGlvbklkOiA2NDhcclxuICAgICAgICB9LFxyXG4gICAgICAgIHNfb0VQTjogXCJNeSBjdXJyZW50IEhvbWUgSW50ZXJuZXQgcGFja2FnZVwiXHJcbiAgICAgIH0pO1xyXG4gICAgfVxyXG4gIH0sIFtleHBhbmRlZF0pO1xyXG4gIC8vIC0tLVxyXG4gIGNvbnN0IGNvbGxhcHNlSWNvbiA9IGV4cGFuZGVkID8gXCJpY29uLUNvbGxhcHNlXCIgOiBcImljb24tRXhwYW5kXCI7XHJcbiAgcmV0dXJuIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJnVmlyZ2luR3JhZGlhbnQgYWNjc3MtZm9jdXMtb3V0bGluZS1vdmVycmlkZS1wYWRcIj5cclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFpbmVyIGxpcXVpZC1jb250YWluZXIgc2Fucy1zZXJpZlwiPlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFjY29yZGlvbi1ncm91cCBpbnRlcm5ldC1jdXJyZW50LXBhY2thZ2UgZmxleENvbFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWNjb3JkaW9uLWhlYWRpbmcgY29sLXhzLTEyIG5vUGFkZGluZ0ltcFwiPlxyXG4gICAgICAgICAgPGEgaWQ9XCJhY2NvcmRpb25fZXhwYW5kX2xpbmtcIiBocmVmPVwiamF2YXNjcmlwdDp2b2lkKDApXCIgb25DbGljaz17KCkgPT4gdG9nZ2xlU3RhdGUoIWV4cGFuZGVkKX0gYXJpYS1jb250cm9scz1cImRpdjEtYWNjZXNzaWJsZVwiXHJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImFjY29yZGlvbi1hY2Nlc3NpYmxlLXRvZ2dsZSB0eHRTaXplMTggdHh0RGVjb3JhdGlvbk5vbmVIb3ZlciB0eHRXaGl0ZSBmbGV4Um93IGFsaWduLWl0ZW1zLWNlbnRlciBhY2Nzcy13aWR0aC1maXQtY29udGVudFwiXHJcbiAgICAgICAgICAgIGFyaWEtZXhwYW5kZWQ9e2V4cGFuZGVkfSByb2xlPVwiYnV0dG9uXCI+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNyLW9ubHkgYWNjb3JkaW9uLWxhYmVsXCIgYXJpYS1saXZlPVwicG9saXRlXCIgYXJpYS1hdG9taWM9XCJ0cnVlXCJcclxuICAgICAgICAgICAgICBhcmlhLWhpZGRlbj1cInRydWVcIj48Rm9ybWF0dGVkTWVzc2FnZSBpZD17ZXhwYW5kZWQgPyBcIkNvbGxhcHNlXCIgOiBcIkV4cGFuZFwifSAvPjwvc3Bhbj5cclxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPXtgJHtjb2xsYXBzZUljb259IHZpcmdpbi1pY29uIHR4dFNpemUyNCB2aXJnaW5SZWRJY29uYH1cclxuICAgICAgICAgICAgICBhcmlhLWhpZGRlbj1cInRydWVcIj5cclxuICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B2aXJnaW4taWNvbiBwYXRoMSAke2NvbGxhcHNlSWNvbn1gfSAvPlxyXG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHZpcmdpbi1pY29uIHBhdGgyICR7Y29sbGFwc2VJY29ufWB9IC8+XHJcbiAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXJnaW4tMTUtbGVmdCBmbGV4Q29sXCI+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHh0V2hpdGUgdHh0Qm9sZCB0eHRTaXplMThcIj48Rm9ybWF0dGVkTWVzc2FnZSBpZD1cIk15IGN1cnJlbnQgSG9tZSBJbnRlcm5ldCBwYWNrYWdlXCIgLz48L3NwYW4+XHJcbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZXhwYW5kIHR4dFdoaXRlIHR4dFNpemUxMiBuby1tYXJnaW4tdG9wXCIgc3R5bGU9e3sgZGlzcGxheTogZXhwYW5kZWQgPyBcIm5vbmVcIiA6IHVuZGVmaW5lZCB9fT48Rm9ybWF0dGVkTWVzc2FnZSBpZD1cIkV4cGFuZCB0byB2aWV3IGRldGFpbHNcIiAvPjwvc3Bhbj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2E+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGRpdiBpZD1cImRpdjEtYWNjZXNzaWJsZVwiXHJcbiAgICAgICAgICBjbGFzc05hbWU9XCJjb2xsYXBzZS1hY2NvcmRpb24tYWNjZXNzaWJsZS10b2dnbGUgYWNjb3JkaW9uLWJvZHkgdHh0V2hpdGUgY29sLXhzLTEyIG1hcmdpbi01LXRvcCBtYXJnaW4tNDAtbGVmdFwiXHJcbiAgICAgICAgICBzdHlsZT17eyBkaXNwbGF5OiBleHBhbmRlZCA/IFwiYmxvY2tcIiA6IFwibm9uZVwiIH19PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhY2NvcmRpb24taW5uZXJcIj5cclxuICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgIGFjY291bnREZXRhaWxzLm1hcCgoe1xyXG4gICAgICAgICAgICAgICAgTmFtZSxcclxuICAgICAgICAgICAgICAgIFJlZ3VsYXJQcmljZSxcclxuICAgICAgICAgICAgICAgIFByb21vdGlvbkRldGFpbHNcclxuICAgICAgICAgICAgICB9KSA9PlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc20tNVwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlcjEwXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4Um93IGZsZXhFbmRcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhHcm93XCI+e05hbWV9PC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBzdHlsZT17eyB3aGl0ZVNwYWNlOiBcIm5vd3JhcFwiIH19PjxDb21wb25lbnRzLkJlbGxDdXJyZW5jeSB2YWx1ZT17VmFsdWVPZihSZWd1bGFyUHJpY2UsIFwiUHJpY2VcIiwgMCl9IC8+PEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJQRVJfTU9cIiAvPjwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgPENvbXBvbmVudHMuVmlzaWJsZSB3aGVuPXshIVByb21vdGlvbkRldGFpbHN9PlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2VyNVwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPENvbXBvbmVudHMuVmlzaWJsZSB3aGVuPXtWYWx1ZU9mKFByb21vdGlvbkRldGFpbHMsIFwiRGVzY3JpcHRpb25cIiwgZmFsc2UpfT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFJvd1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXhHcm93XCI+e1ZhbHVlT2YoUHJvbW90aW9uRGV0YWlscywgXCJEZXNjcmlwdGlvblwiLCBcIlwiKX08L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdj48Q29tcG9uZW50cy5CZWxsQ3VycmVuY3kgdmFsdWU9e1ZhbHVlT2YoUHJvbW90aW9uRGV0YWlscywgXCJQcm9tb3Rpb25hbFByaWNlLlByaWNlXCIsIDApfSAvPjxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiUEVSX01PXCIgLz48L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvQ29tcG9uZW50cy5WaXNpYmxlPlxyXG4gICAgICAgICAgICAgICAgICAgIDxDb21wb25lbnRzLlZpc2libGUgd2hlbj17VmFsdWVPZihQcm9tb3Rpb25EZXRhaWxzLCBcIkV4cGlyeURhdGVcIiwgZmFsc2UpfT5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxGb3JtYXR0ZWREYXRlIHZhbHVlPXtWYWx1ZU9mKFByb21vdGlvbkRldGFpbHMsIFwiRXhwaXJ5RGF0ZVwiLCBcIlwiKX0gZm9ybWF0PVwieU1NTU1kXCIgdGltZVpvbmU9XCJVVENcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICB7KGV4cGlyeURhdGUpID0+IDxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiUHJvbW90aW9uRXhwaXJlc1wiIHZhbHVlcz17eyBleHBpcnlEYXRlIH19IC8+fVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8L0Zvcm1hdHRlZERhdGU+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8L0NvbXBvbmVudHMuVmlzaWJsZT5cclxuICAgICAgICAgICAgICAgICAgPC9Db21wb25lbnRzLlZpc2libGU+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj4pXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gIDwvc2VjdGlvbj47XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgSGVhZGVyID0gY29ubmVjdDxJQ29tcG9uZW50UHJvcHM+KFxyXG4gICh7IGFjY291bnREZXRhaWxzIH06IElTdG9yZVN0YXRlKSA9PiAoeyBhY2NvdW50RGV0YWlsczogYWNjb3VudERldGFpbHMgfHwgW10gfSlcclxuKShDb21wb25lbnQpO1xyXG4iLCJpbXBvcnQgeyBDb21wb25lbnRzLCBWYWx1ZU9mLCBWb2x0IH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IEZvcm1hdHRlZERhdGUsIEZvcm1hdHRlZE1lc3NhZ2UgfSBmcm9tIFwicmVhY3QtaW50bFwiO1xyXG5pbXBvcnQgeyBjb25uZWN0IH0gZnJvbSBcInJlYWN0LXJlZHV4XCI7XHJcbmltcG9ydCB7IElQYWNrYWdlLCBJU3RvcmVTdGF0ZSB9IGZyb20gXCIuLi8uLi9tb2RlbHNcIjtcclxuaW1wb3J0IHsgdG9nZ2xlUGFja2FnZVNlbGVjdGlvbiB9IGZyb20gXCIuLi8uLi9zdG9yZVwiO1xyXG4vLyBpbXBvcnQgeyBMb2NhbGl6YXRpb24gfSBmcm9tIFwiLi4vLi4vTG9jYWxpemF0aW9uXCI7XHJcblxyXG5kZWNsYXJlIGZ1bmN0aW9uICQocTogYW55KTogYW55O1xyXG5cclxuY29uc3Qge1xyXG4gIFZpc2libGUsXHJcbiAgQ3VycmVuY3lcclxufSA9IENvbXBvbmVudHM7XHJcblxyXG4vLyBpbnRlcmZhY2UgSVBhcnNlQ2hhcmFjdGVyaXN0aWNzIHtcclxuLy8gICAgIGRvd25sb2FkOiBWb2x0LklDaGFyYWN0ZXJpc3RpYyB8IHVuZGVmaW5lZDtcclxuLy8gICAgIHVwbG9hZDogVm9sdC5JQ2hhcmFjdGVyaXN0aWMgfCB1bmRlZmluZWQ7XHJcbi8vICAgICB1c2FnZTogVm9sdC5JQ2hhcmFjdGVyaXN0aWMgfCB1bmRlZmluZWQ7XHJcbi8vIH1cclxuXHJcbi8vIGZ1bmN0aW9uIFBhcnNlQ2hhcmFjdGVyaXN0aWNzKGNoYXJhY3RlcmlzdGljczogQXJyYXk8Vm9sdC5JQ2hhcmFjdGVyaXN0aWM+KTogSVBhcnNlQ2hhcmFjdGVyaXN0aWNzIHtcclxuLy8gICAgIHJldHVybiB7XHJcbi8vICAgICAgICAgZG93bmxvYWQ6IGNoYXJhY3RlcmlzdGljcy5maW5kKGNociA9PiBjaHIuY2hhcmFjdGVyaXN0aWNOYW1lID09PSBcIkRvd25sb2FkXCIpLFxyXG4vLyAgICAgICAgIHVwbG9hZDogY2hhcmFjdGVyaXN0aWNzLmZpbmQoY2hyID0+IGNoci5jaGFyYWN0ZXJpc3RpY05hbWUgPT09IFwiVXBsb2FkXCIpLFxyXG4vLyAgICAgICAgIHVzYWdlOiBjaGFyYWN0ZXJpc3RpY3MuZmluZChjaHIgPT4gY2hyLmNoYXJhY3RlcmlzdGljTmFtZSA9PT0gXCJVc2FnZVwiKSxcclxuLy8gICAgIH07XHJcbi8vIH1cclxuXHJcbmludGVyZmFjZSBJQ29tcG9uZW50RGlzcGF0Y2hlcyB7XHJcbiAgb25QYWNrYWdlQ2xpY2tlZDogKGFjdGlvbjogVm9sdC5JSHlwZXJtZWRpYUFjdGlvbikgPT4gdm9pZDtcclxufVxyXG5cclxuY29uc3QgQ29tcG9uZW50OiBSZWFjdC5GQzxJUGFja2FnZSAmIElDb21wb25lbnREaXNwYXRjaGVzPiA9ICh7XHJcbiAgaWQsXHJcbiAgbmFtZSxcclxuICBzaG9ydERlc2NyaXB0aW9uLFxyXG4gIHVzYWdlUGxhbixcclxuICAvLyBzdGF0ZSxcclxuICAvLyB0eXBlLFxyXG4gIC8vIGlzU2VsZWN0YWJsZSxcclxuICAvLyBpc0N1cnJlbnQsXHJcbiAgaXNTZWxlY3RhYmxlLFxyXG4gIGlzU2VsZWN0ZWQsXHJcbiAgLy8gY2hhcmFjdGVyaXN0aWNzLFxyXG4gIHJlZ3VsYXJQcmljZSxcclxuICBwcm9tb3Rpb25EZXRhaWxzLFxyXG4gIG9mZmVyaW5nQWN0aW9uLFxyXG4gIG9uUGFja2FnZUNsaWNrZWRcclxufSkgPT4ge1xyXG4gIGNvbnN0IG9uUGFja2FnZUFjdGlvbiA9IChlOiBhbnkpID0+IHtcclxuICAgIGUuc3RvcFByb3BhZ2F0aW9uKCk7XHJcbiAgICBlLnByZXZlbnREZWZhdWx0KCk7XHJcbiAgICBpZiAoaXNTZWxlY3RlZCkgcmV0dXJuO1xyXG4gICAgaWYgKGUua2V5Q29kZSA9PT0gdW5kZWZpbmVkIHx8IGUua2V5Q29kZSA9PT0gMzIgfHwgZS5rZXlDb2RlID09PSAxMykge1xyXG4gICAgICBvblBhY2thZ2VDbGlja2VkKG9mZmVyaW5nQWN0aW9uKTtcclxuICAgIH1cclxuICB9O1xyXG4gIGNvbnN0IFt1cGxvYWRFeHBhbmRlZCwgRXhwYW5kVXBsb2FkXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBvblVwbG9hZENsaWNrID0gKGU6IGFueSkgPT4ge1xyXG4gICAgaWYgKChlLmtleUNvZGUgPT09IHVuZGVmaW5lZCB8fCBlLmtleUNvZGUgPT09IDEzKSAmJiBlLnRhcmdldC5jbGFzc0xpc3QuY29udGFpbnMoXCJ0eHRVbmRlcmxpbmVcIikpIHtcclxuICAgICAgRXhwYW5kVXBsb2FkKCF1cGxvYWRFeHBhbmRlZCk7XHJcbiAgICB9XHJcbiAgfTtcclxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgJChcIiNcIiArIGlkKVxyXG4gICAgICAuZmluZChcIltkYXRhLXRvZ2dsZV1cIilcclxuICAgICAgLmFkZENsYXNzKFwidHh0VW5kZXJsaW5lIHR4dEJsdWUgcG9pbnRlciBhY2Nzcy10ZXh0LWJsdWUtb24tYmctd2hpdGUgYWNjc3Mtd2lkdGgtZml0LWNvbnRlbnRcIilcclxuICAgICAgLmF0dHIoXCJ0YWJpbmRleFwiLCBcIjBcIilcclxuICAgICAgLm5leHQoKVxyXG4gICAgICAuYWRkQ2xhc3MoXCJkb3dubG9hZFRyYXlcIilcclxuICAgICAgLnJlbW92ZUF0dHIoXCJpZFwiKTtcclxuICB9KTtcclxuICAvLyBjb25zdCBwYXJzZWRDaGFyYWN0ZXJpc3RpY3MgPSBQYXJzZUNoYXJhY3RlcmlzdGljcyhWYWx1ZU9mKGNoYXJhY3RlcmlzdGljcywgdW5kZWZpbmVkLCBbXSkpO1xyXG4gIHJldHVybiA8ZGl2IGlkPXtpZH0gY2xhc3NOYW1lPXtgdmlyZ2luLWludGVybmV0LWJveCB0eHRHcmF5IG1hcmdpbi0xNS1ib3R0b20gJHtpc1NlbGVjdGVkID8gXCJzZWxlY3RlZFwiIDogXCJcIn1gfT5cclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFJvdyBiZ1doaXRlIGJvcmRlci1yYWRpdXMtMyB2aXJnaW4tdGl0bGUtYmxvY2sgcGFkLTMwIHBhZC0xNS1sZWZ0LXJpZ2h0LXNtIGFjY3NzLWZvY3VzLW91dGxpbmUtb3ZlcnJpZGUtd2hpdGUtYmdcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwYWNrYWdlX2N0cmxcIj5cclxuICAgICAgICA8c3BhbiBpZD17YENUQV8ke2lkfWB9IGNsYXNzTmFtZT1cImdyYXBoaWNhbF9jdHJsIGN0cmxfcmFkaW9CdG4gcG9pbnRlclwiIG9uQ2xpY2s9e29uUGFja2FnZUFjdGlvbn0+XHJcbiAgICAgICAgICA8aW5wdXQgaWQ9e2BPUFRfJHtpZH1gfSBuYW1lPVwiaW50ZXJuZXRwYWNrYWdlXCIgY2hlY2tlZD17aXNTZWxlY3RlZH0gdHlwZT1cInJhZGlvXCIgYXJpYS1sYWJlbGxlZGJ5PXtgUEFDS0FHRV9DVEFfJHtpZH1gfSBhcmlhLWRlc2NyaWJlZGJ5PXtgUEFDS0FHRV9DVEFfREVTQ18ke2lkfWB9IGFyaWEtY2hlY2tlZD17aXNTZWxlY3RlZH0gY2xhc3NOYW1lPVwicmFkaW9CdG4tYWN0aXZlIGRhdGEtZmVhdHVyZVwiIC8+XHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJjdHJsX2VsZW1lbnQgcG9pbnRlciBkYXRhLWFkZG9uLWFjdGl2ZSBkYXRhLWFkZG9uLWJvcmRlclwiIC8+XHJcbiAgICAgICAgPC9zcGFuPlxyXG4gICAgICA8L2Rpdj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwYWNrYWdlLWRlc2MgZmlsbFwiPlxyXG4gICAgICAgIDxkaXYgaWQ9e2BQQUNLQUdFX0NUQV8ke2lkfWB9IGNsYXNzTmFtZT1cImZpbGwgcGFkLTE1LWxlZnQgY29udGVudC13aWR0aCB2YWxpZ24tdG9wIHBhZC0wLXhzIHBvaW50ZXJcIiBvbkNsaWNrPXtvblBhY2thZ2VBY3Rpb259PlxyXG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInZpcmdpblVsdHJhUmVnIHR4dFNpemUxNiBmbG9hdEwgdHh0VXBwZXJjYXNlIG5vLW1hcmdpblwiPntuYW1lfTwvaDI+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZXIxMCBkLW5vbmUgZC1zbS1ibG9jayBkLW1kLW5vbmUgY2xlYXJcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2VyMTUgY2xlYXJcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2VyMSBiZ0dyYXlMaWdodDYgY2xlYXIgbWFyZ2luLTMwLXJpZ2h0XCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlcjE1IGhpZGRlbi1tXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInBrZy1wdWxsLWxlZnQgbmVnLW1hcmdpbi1sZWZ0LTQwLXNtIGZsZXhCbG9ja1wiIGlkPXtgUEFDS0FHRV9DVEFfREVTQ18ke2lkfWB9PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4Um93IGZpbGwgZmxleENvbC14c1wiPlxyXG4gICAgICAgICAgICA8dWwgaWQ9e2BVUExPQURfQ1RBXyR7aWR9YH0gY2xhc3NOYW1lPXtgc3BlZWQtYm94MSBmbGV4Um93IGZsZXhDb2wteHMgbWItMCBwbC0wIGxpc3QtdW5zdHlsZWQgJHt1cGxvYWRFeHBhbmRlZCA/IFwiZXhwYW5kZWRcIiA6IFwiXCJ9YH0gb25LZXlVcD17b25VcGxvYWRDbGlja30gb25DbGljaz17b25VcGxvYWRDbGlja30gZGFuZ2Vyb3VzbHlTZXRJbm5lckhUTUw9e3sgX19odG1sOiBzaG9ydERlc2NyaXB0aW9uIH19IC8+XHJcbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJzcGVlZC1ib3gyIG1iLTAgbGlzdC11bnN0eWxlZFwiIGRhbmdlcm91c2x5U2V0SW5uZXJIVE1MPXt7IF9faHRtbDogdXNhZ2VQbGFuIH19IC8+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BlZWQtYm94M1wiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicGFkLTMwLWxlZnQgbm8tcGFkLXhzIG1hcmdpbi0xMC1sZWZ0LXhzXCI+XHJcbiAgICAgICAgICAgICAgICA8VmlzaWJsZSB3aGVuPXtWYWx1ZU9mKHByb21vdGlvbkRldGFpbHMsIFwiZXhwaXJ5RGF0ZVwiLCBmYWxzZSl9PlxyXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0eHRTaXplMTIgdHh0QmxhY2sgYmxvY2sgdHh0Qm9sZCBiZ0dyYXkyIHBhZC01IGJvcmRlci1yYWRpdXMtM1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxGb3JtYXR0ZWREYXRlIHZhbHVlPXtWYWx1ZU9mKHByb21vdGlvbkRldGFpbHMsIFwiZXhwaXJ5RGF0ZVwiLCBcIlwiKX0gZm9ybWF0PVwieU1NTU1kXCIgdGltZVpvbmU9XCJVVENcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgKGV4cGlyeURhdGUpID0+IDxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiWW91ciBtb250aGx5IGNyZWRpdCBleHBpcmVzXCIgdmFsdWVzPXt7IGV4cGlyeURhdGUgfX0gLz5cclxuICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICA8L0Zvcm1hdHRlZERhdGU+XHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvVmlzaWJsZT5cclxuICAgICAgICAgICAgICAgIDxWaXNpYmxlIHdoZW49e1ZhbHVlT2YocHJvbW90aW9uRGV0YWlscywgXCJkaXNjb3VudER1cmF0aW9uXCIsIGZhbHNlKX0+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInR4dFNpemUxMiB0eHRCbGFjayBibG9jayB0eHRCb2xkIGJnR3JheTIgcGFkLTUgYm9yZGVyLXJhZGl1cy0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJHZXQgYSBjcmVkaXQgZm9yIG1vbnRoc1wiIHZhbHVlcz17eyBjcmVkaXQ6IE1hdGguYWJzKFZhbHVlT2YocHJvbW90aW9uRGV0YWlscywgXCJkaXNjb3VudFByaWNlLnByaWNlXCIsIDApKSwgZHVyYXRpb246IFZhbHVlT2YocHJvbW90aW9uRGV0YWlscywgXCJkaXNjb3VudER1cmF0aW9uXCIsIDApIH19IC8+XHJcbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cclxuICAgICAgICAgICAgICAgIDwvVmlzaWJsZT5cclxuICAgICAgICAgICAgICAgIDxWaXNpYmxlIHdoZW49e1ZhbHVlT2YoIXByb21vdGlvbkRldGFpbHMsIHVuZGVmaW5lZCwgZmFsc2UpfT5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZXIxNSBjbGVhciBoaWRkZW4tbVwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L1Zpc2libGU+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByaWNlIHZpcmdpblVsdHJhUmVnIHR4dFNpemU0MCBsaW5lLWhlaWdodC0xIG1hcmdpbi0xMC10b3BcIj5cclxuICAgICAgICAgICAgICAgICAgPFZpc2libGUgd2hlbj17VmFsdWVPZihwcm9tb3Rpb25EZXRhaWxzLCB1bmRlZmluZWQsIGZhbHNlKX0+XHJcbiAgICAgICAgICAgICAgICAgICAgPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJOb3dcIiAvPiZuYnNwO1xyXG4gICAgICAgICAgICAgICAgICA8L1Zpc2libGU+XHJcbiAgICAgICAgICAgICAgICAgIDxDdXJyZW5jeSB2YWx1ZT17XHJcbiAgICAgICAgICAgICAgICAgICAgVmFsdWVPZihwcm9tb3Rpb25EZXRhaWxzLCBcIj9wcm9tb3Rpb25hbFByaWNlLnByaWNlXCIsIGZhbHNlKSA9PT0gZmFsc2VcclxuICAgICAgICAgICAgICAgICAgICAgID8gVmFsdWVPZihyZWd1bGFyUHJpY2UsIFwicHJpY2VcIiwgMClcclxuICAgICAgICAgICAgICAgICAgICAgIDogVmFsdWVPZihwcm9tb3Rpb25EZXRhaWxzLCBcInByb21vdGlvbmFsUHJpY2UucHJpY2VcIiwgMClcclxuICAgICAgICAgICAgICAgICAgfSBtb250aGx5PXt0cnVlfSAvPlxyXG4gICAgICAgICAgICAgICAgICA8VmlzaWJsZSB3aGVuPXtWYWx1ZU9mKHByb21vdGlvbkRldGFpbHMsIHVuZGVmaW5lZCwgZmFsc2UpfT5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0eHRTaXplMTIgdHh0QmxhY2sgdHh0Qm9sZCBzYW5zLXNlcmlmIG5vLW1hcmdpblwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPEZvcm1hdHRlZE1lc3NhZ2UgaWQ9XCJDdXJyZW50IFByaWNlXCIgdmFsdWVzPXtWYWx1ZU9mKHJlZ3VsYXJQcmljZSwgdW5kZWZpbmVkLCB7fSkgYXMgYW55fSAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgICAgPC9WaXNpYmxlPlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8VmlzaWJsZSB3aGVuPXtWYWx1ZU9mKHByb21vdGlvbkRldGFpbHMsIHVuZGVmaW5lZCwgZmFsc2UpfT5cclxuICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidHh0U2l6ZTEyIHR4dEJsYWNrIHNhbnMtc2VyaWYgbm8tbWFyZ2luIHBhZC0xMC10b3BcIj5cclxuICAgICAgICAgICAgICAgICAgICB7VmFsdWVPZihwcm9tb3Rpb25EZXRhaWxzLCBcImxlZ2FsTWVzc2FnZVwiLCA8Rm9ybWF0dGVkTWVzc2FnZSBpZD1cIlByaWNlcyBtYXkgaW5jcmVhc2UgbGVnYWxcIiAvPil9XHJcbiAgICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDwvVmlzaWJsZT5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICA8L2Rpdj47XHJcbn07XHJcblxyXG5cclxuZXhwb3J0IGNvbnN0IFBhY2thZ2UgPSBjb25uZWN0PHt9LCBJQ29tcG9uZW50RGlzcGF0Y2hlcywgSVBhY2thZ2U+KFxyXG4gICh7IH06IElTdG9yZVN0YXRlKSA9PiAoe30pLFxyXG4gIGRpc3BhdGNoID0+ICh7XHJcbiAgICBvblBhY2thZ2VDbGlja2VkOiAoYWN0aW9uOiBWb2x0LklIeXBlcm1lZGlhQWN0aW9uKSA9PiBkaXNwYXRjaCh0b2dnbGVQYWNrYWdlU2VsZWN0aW9uKGFjdGlvbikpXHJcbiAgfSlcclxuKShDb21wb25lbnQpO1xyXG4iLCJpbXBvcnQgeyBDb21wb25lbnRzLCBGb3JtYXR0ZWRIVE1MTWVzc2FnZSwgT21uaXR1cmUgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgRm9ybWF0dGVkTWVzc2FnZSB9IGZyb20gXCJyZWFjdC1pbnRsXCI7XHJcblxyXG5pbnRlcmZhY2UgSUNvbXBvbmVudFByb3BzIHsgfVxyXG5cclxuZXhwb3J0IGNvbnN0IEZvb3RlcjogUmVhY3QuRkM8SUNvbXBvbmVudFByb3BzPiA9ICgpID0+IHtcclxuICBjb25zdCBbZXhwYW5kZWQsIHRvZ2dsZVN0YXRlXSA9IFJlYWN0LnVzZVN0YXRlKGZhbHNlKTtcclxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgZXhwYW5kZWQgJiZcclxuICAgICAgICAgICAgT21uaXR1cmUudXNlT21uaXR1cmUoKS50cmFja0FjdGlvbih7XHJcbiAgICAgICAgICAgICAgaWQ6IFwibGlnYWxTdHVmZkNsaWNrXCIsXHJcbiAgICAgICAgICAgICAgc19vQVBUOiB7XHJcbiAgICAgICAgICAgICAgICBhY3Rpb25JZDogNjQ4XHJcbiAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICBzX29FUE46IFwiTGVnYWwgU3R1ZmZcIlxyXG4gICAgICAgICAgICB9KTtcclxuICB9LCBbZXhwYW5kZWRdKTtcclxuICByZXR1cm4gPGRpdiBjbGFzc05hbWU9XCJ2aXJnaW5VbHRyYVJlZyBtYXJnaW4tMTUtdG9wXCIgaWQ9XCJtb3JlSW5mb1wiPlxyXG4gICAgPGJ1dHRvbiBpZD1cIkxlZ2FsX3N0dWZmXCIgY2xhc3NOYW1lPVwiYnRuIGJ0bi1saW5rIG5vVW5kZXJsaW5lQWxsIG5vUGFkZGluZ0ltcCBsaW5rcy1ibHVlLW9uLWJnLWdyYXkgYWNjc3MtZm9jdXMtb3V0bGluZS1vdmVycmlkZS1ncmV5LWJnLWVsZW1lbnRcIiBvbkNsaWNrPXsoKSA9PiB0b2dnbGVTdGF0ZSghZXhwYW5kZWQpfSBhcmlhLWV4cGFuZGVkPXtleHBhbmRlZH0+XHJcbiAgICAgIDxzcGFuIGNsYXNzTmFtZT17YHZvbHQtaWNvbiAke2V4cGFuZGVkID8gXCJpY29uLWNvbGxhcHNlX21cIiA6IFwiaWNvbi1leHBhbmRfbVwifWB9IGFyaWEtaGlkZGVuPVwidHJ1ZVwiIC8+Jm5ic3A7Jm5ic3A7XHJcbiAgICAgIDxGb3JtYXR0ZWRNZXNzYWdlIGlkPVwiTGVnYWwgc3R1ZmYgbGFiZWxcIiAvPlxyXG4gICAgPC9idXR0b24+XHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlcjMwXCIgYXJpYS1oaWRkZW49XCJ0cnVlXCIgLz5cclxuICAgIDxDb21wb25lbnRzLlZpc2libGUgd2hlbj17ZXhwYW5kZWR9PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1vcmVJbmZvQm94IGJnV2hpdGUgcGFkMzAgbWFyZ2luLTMwLWJvdHRvbSBhY2Nzcy1saW5rLW92ZXJyaWRlIGFjY3NzLWZvY3VzLW91dGxpbmUtb3ZlcnJpZGUtd2hpdGUtYmdcIj5cclxuICAgICAgICA8YnV0dG9uIGlkPVwiTEVHQUxCT1hfQ0xPU0VcIiB0eXBlPVwiYnV0dG9uXCIgb25DbGljaz17KCkgPT4gdG9nZ2xlU3RhdGUoZmFsc2UpfSBjbGFzc05hbWU9XCJjbG9zZSBtb3JlSW5mb0xpbmsgeC1pbm5lciB0eHREYXJrR3JleSB0eHRTaXplMTggdHh0Qm9sZFwiIGFyaWEtbGFiZWw9XCJjbG9zZVwiPlxyXG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidmlyZ2luLWljb24gaWNvbi1iaWdfWFwiIGFyaWEtaGlkZGVuPVwidHJ1ZVwiIC8+XHJcbiAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgPEZvcm1hdHRlZEhUTUxNZXNzYWdlIGlkPVwiR09PRCBUTyBLTk9XXCIgLz5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L0NvbXBvbmVudHMuVmlzaWJsZT5cclxuICA8L2Rpdj47XHJcbn07XHJcbiIsImltcG9ydCB7IFZvbHQsIFZhbHVlT2YgfSBmcm9tIFwib21mLWNoYW5nZXBhY2thZ2UtY29tcG9uZW50c1wiO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIHRvQ2hhcmFjdGVyaXN0aWNzSlNPTihjaGFyYWN0Z2Vyc3RpY3M6IEFycmF5PFZvbHQuSUNoYXJhY3RlcmlzdGljPik6IHsgW2tleTogc3RyaW5nXTogYW55IH0ge1xyXG4gIHJldHVybiBWYWx1ZU9mPEFycmF5PFZvbHQuSUNoYXJhY3RlcmlzdGljPj4oY2hhcmFjdGdlcnN0aWNzLCB1bmRlZmluZWQsIFtdKS5yZWR1Y2UoXHJcbiAgICAoanNvbiwgY2hhcmFjdGdlcnN0aWMpID0+IHtcclxuICAgICAgaWYgKGNoYXJhY3RnZXJzdGljLm5hbWUpIHtcclxuICAgICAgICBqc29uW2NoYXJhY3RnZXJzdGljLm5hbWVdID0gIGNoYXJhY3RnZXJzdGljLnZhbHVlO1xyXG4gICAgICB9XHJcbiAgICAgIHJldHVybiBqc29uO1xyXG4gICAgfSwge30gYXMgYW55XHJcbiAgKTtcclxufVxyXG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgY29ubmVjdCB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xyXG5pbXBvcnQgeyBWYWx1ZU9mIH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuaW1wb3J0IHsgSVN0b3JlU3RhdGUsIElQYWNrYWdlIH0gZnJvbSBcIi4uLy4uL21vZGVsc1wiO1xyXG5pbXBvcnQgeyBQYWNrYWdlIH0gZnJvbSBcIi4vUGFja2FnZVwiO1xyXG5pbXBvcnQgeyBGb290ZXIgfSBmcm9tIFwiLi9MZWdhbFwiO1xyXG5pbXBvcnQgeyB0b0NoYXJhY3RlcmlzdGljc0pTT04gfSBmcm9tIFwiLi4vLi4vdXRpbHMvQ2hhcmFjdGVyaXN0aWNzXCI7XHJcblxyXG5pbnRlcmZhY2UgSUNvbXBvbmVudFByb3BzIHtcclxuICBjYXRhbG9nOiBBcnJheTxJUGFja2FnZT47XHJcbn1cclxuXHJcbmNvbnN0IENvbXBvbmVudDogUmVhY3QuRkM8SUNvbXBvbmVudFByb3BzPiA9ICh7XHJcbiAgY2F0YWxvZ1xyXG59KSA9PiA8ZGl2IGNsYXNzTmFtZT1cImNvbnRhaW5lciBsaXF1aWQtY29udGFpbmVyIG5vU3BhY2luZ1wiIHJvbGU9XCJyYWRpb2dyb3VwXCI+XHJcbiAgPHN0eWxlPlxyXG4gICAge1xyXG4gICAgICBgLmljb24tdXBsb2FkLWljbzpiZWZvcmUge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6IFwiXFxcXGU5OWRcIjtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC5pY29uLWRvd25sb2FkLWljbzpiZWZvcmUge1xyXG4gICAgICAgICAgICAgICAgICAgIGNvbnRlbnQ6IFwiXFxcXGU5MjlcIjtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIC5wYWNrYWdlLWRlc2MgbGkge1xyXG4gICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICAgICAgICAgIGxpc3Qtc3R5bGU6IG5vbmU7XHJcbiAgICAgICAgICAgICAgICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xyXG4gICAgICAgICAgICAgICAgICAgIHdpZHRoOiBjYWxjKDEwMCUgLyAyKTtcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAucGFja2FnZS1kZXNjIGxpOm5vdCg6bGFzdC1vZi10eXBlKSB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBhZGRpbmctcmlnaHQ6IDE1cHg7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIC5wYWNrYWdlLWRlc2MgbGkgLnZvbHQtaWNvbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTtcclxuICAgICAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbG9yOiAjY2MwMDAwO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDMycHg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHdpZHRoOiA0MnB4O1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBoZWlnaHQ6IDQycHg7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGxlZnQ6IDA7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIC5wYWNrYWdlLWRlc2MgbGkgc3BhbiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGRpc3BsYXk6IGJsb2NrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250LXNpemU6IDEycHg7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIC5wYWNrYWdlLWRlc2MgLnNwZWVkLWJveDIgc3BhbjpmaXJzdC1vZi10eXBlLFxyXG4gICAgICAgICAgICAgICAgICAgIC5wYWNrYWdlLWRlc2MgbGkgc3Bhbi5zcGVlZCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvbnQtc2l6ZTogMjJweDtcclxuICAgICAgICAgICAgICAgICAgICAgICAgY29sb3I6IGJsYWNrO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBmb250LWZhbWlseTogXCJWTVVsdHJhbWFnbmV0aWNOb3JtYWxSZWd1bGFyXCIsIEhlbHZldGljYSwgQXJpYWwsIHNhbnMtc2VyaWY7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAucGFja2FnZS1kZXNjIC5zcGVlZC1ib3gyIHNwYW4udXNhZ2Uge1xyXG4gICAgICAgICAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBub3dyYXA7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAuc3BlZWQtYm94MSBsaSB7XHJcbiAgICAgICAgICAgICAgICAgICAgbWFyZ2luLXRvcDogMTBweDtcclxuICAgICAgICAgICAgICAgICAgICBwYWRkaW5nLWxlZnQ6IDQycHg7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAucGFja2FnZS1kZXNjIGxpIHNwYW4uZG93bmxvYWRUcmF5IHtcclxuICAgICAgICAgICAgICAgICAgICBkaXNwbGF5OiBub25lO1xyXG4gICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgLnBhY2thZ2UtZGVzYyAuc3BlZWQtYm94MS5leHBhbmRlZCBsaSBzcGFuLmRvd25sb2FkVHJheSB7XHJcbiAgICAgICAgICAgICAgICAgICAgZGlzcGxheTogYmxvY2s7XHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogOTkxLjk4cHgpIHtcclxuICAgICAgICAgICAgICAgICAgICAucGFja2FnZS1kZXNjIGxpIHtcclxuICAgICAgICAgICAgICAgICAgICAgICAgd2lkdGg6IDEwMCU7XHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAucGtnLXB1bGwtbGVmdCB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBtYXJnaW4tbGVmdDogLTQwcHg7XHJcbiAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIH1gXHJcbiAgICB9XHJcbiAgPC9zdHlsZT5cclxuICB7XHJcbiAgICAvLyBXZSBkbyBub3Qgd2FudCB0byBzaG93IGN1cnJlbnQgcGFja2FnZXNcclxuICAgIGNhdGFsb2cuZmlsdGVyKHBrZyA9PiAhcGtnLmlzQ3VycmVudClcclxuICAgICAgLnNvcnQoXHJcbiAgICAgICAgKGEsIGIpID0+IChcclxuICAgICAgICAgIFZhbHVlT2Y8bnVtYmVyPih0b0NoYXJhY3RlcmlzdGljc0pTT04oYS5jaGFyYWN0ZXJpc3RpY3MpLCBcInNvcnRQcmlvcml0eVwiLCAwKSAtXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIFZhbHVlT2Y8bnVtYmVyPih0b0NoYXJhY3RlcmlzdGljc0pTT04oYi5jaGFyYWN0ZXJpc3RpY3MpLCBcInNvcnRQcmlvcml0eVwiLCAwKVxyXG4gICAgICAgIClcclxuICAgICAgKVxyXG4gICAgICAubWFwKFxyXG4gICAgICAgIGludGVybmV0UGFja2FnZSA9PiA8UGFja2FnZSB7Li4uaW50ZXJuZXRQYWNrYWdlfSAvPlxyXG4gICAgICApXHJcbiAgfVxyXG4gIDxGb290ZXIgLz5cclxuPC9kaXY+O1xyXG5cclxuXHJcbmV4cG9ydCBjb25zdCBDYXRhbG9nID0gY29ubmVjdDxJQ29tcG9uZW50UHJvcHM+KFxyXG4gICh7IGNhdGFsb2cgfTogSVN0b3JlU3RhdGUpID0+ICh7IGNhdGFsb2cgfSlcclxuKShDb21wb25lbnQpO1xyXG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgY29ubmVjdCB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xyXG5pbXBvcnQgeyBBY3Rpb25zLCBDb21wb25lbnRzLCBFV2lkZ2V0TmFtZSB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcbmltcG9ydCB7IElTdG9yZVN0YXRlIH0gZnJvbSBcIi4uL21vZGVsc1wiO1xyXG5pbXBvcnQgeyBIZWFkZXIgfSBmcm9tIFwiLi9oZWFkZXJcIjtcclxuaW1wb3J0IHsgQ2F0YWxvZyB9IGZyb20gXCIuL2NhdGFsb2dcIjtcclxuXHJcbmNvbnN0IHtcclxuICBSZXN0cmljdGlvbk1vZGFsXHJcbn0gPSBDb21wb25lbnRzO1xyXG5cclxuY29uc3Qge1xyXG4gIGVycm9yT2NjdXJlZCxcclxuICB3aWRnZXRSZW5kZXJDb21wbGV0ZVxyXG59ID0gQWN0aW9ucztcclxuXHJcbmludGVyZmFjZSBJQ29tcG9uZW50UHJvcHMge1xyXG59XHJcblxyXG5pbnRlcmZhY2UgSUNvbXBvbmVudERpc3BhdGNoZXMge1xyXG4gIG9uRXJyb3JFbmNvdW50ZXJlZDogRnVuY3Rpb247XHJcbiAgd2lkZ2V0UmVuZGVyQ29tcGxldGU6IEZ1bmN0aW9uO1xyXG59XHJcblxyXG5jbGFzcyBDb21wb25lbnQgZXh0ZW5kcyBSZWFjdC5Db21wb25lbnQ8SUNvbXBvbmVudFByb3BzICYgSUNvbXBvbmVudERpc3BhdGNoZXM+IHtcclxuICBjb21wb25lbnREaWRDYXRjaChlcnI6IGFueSkge1xyXG4gICAgdGhpcy5wcm9wcy5vbkVycm9yRW5jb3VudGVyZWQoZXJyKTtcclxuICB9XHJcblxyXG4gIGNvbXBvbmVudERpZE1vdW50KCkge1xyXG4gICAgdGhpcy5wcm9wcy53aWRnZXRSZW5kZXJDb21wbGV0ZShFV2lkZ2V0TmFtZS5JTlRFUk5FVCk7XHJcbiAgfVxyXG5cclxuICByZW5kZXIoKSB7XHJcbiAgICByZXR1cm4gPG1haW4gaWQ9XCJtYWluQ29udGVudFwiPlxyXG4gICAgICA8SGVhZGVyIC8+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2VyMzBcIiBhcmlhLWhpZGRlbj1cInRydWVcIiAvPlxyXG4gICAgICA8Q2F0YWxvZyAvPlxyXG4gICAgICA8UmVzdHJpY3Rpb25Nb2RhbCBpZD1cIklOVEVSTkVUX1JFU1RSSUNUSU9OX01PREFMXCIgLz5cclxuICAgIDwvbWFpbj47XHJcbiAgfVxyXG59XHJcblxyXG5leHBvcnQgY29uc3QgQXBwbGljYXRpb24gPSBjb25uZWN0PElDb21wb25lbnRQcm9wcywgSUNvbXBvbmVudERpc3BhdGNoZXM+KFxyXG4gICh7ICB9OiBJU3RvcmVTdGF0ZSkgPT4gKHsgIH0pLFxyXG4gIChkaXNwYXRjaCkgPT4gKHtcclxuICAgIG9uRXJyb3JFbmNvdW50ZXJlZDogKGVycm9yOiBhbnkpID0+IGRpc3BhdGNoKGVycm9yT2NjdXJlZChlcnJvcikpLFxyXG4gICAgd2lkZ2V0UmVuZGVyQ29tcGxldGU6ICgpID0+IGRpc3BhdGNoKHdpZGdldFJlbmRlckNvbXBsZXRlKCkpXHJcbiAgfSlcclxuKShDb21wb25lbnQpO1xyXG4iLCJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgQ29tcG9uZW50cyB9IGZyb20gXCJvbWYtY2hhbmdlcGFja2FnZS1jb21wb25lbnRzXCI7XHJcbmltcG9ydCB7IEFwcGxpY2F0aW9uIH0gZnJvbSBcIi4vdmlld3NcIjtcclxuXHJcbmNvbnN0IHtcclxuICBBcHBsaWNhdGlvblJvb3RcclxufSA9IENvbXBvbmVudHM7XHJcblxyXG5leHBvcnQgY29uc3QgQXBwID0gKCkgPT4gPEFwcGxpY2F0aW9uUm9vdD5cclxuICA8QXBwbGljYXRpb24gLz5cclxuPC9BcHBsaWNhdGlvblJvb3Q+O1xyXG5cclxuIiwiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IFByb3ZpZGVyIGFzIFN0b3JlUHJvdmlkZXIgfSBmcm9tIFwicmVhY3QtcmVkdXhcIjtcclxuaW1wb3J0IHsgRVdpZGdldFN0YXR1cywgQWN0aW9ucywgQ29udGV4dFByb3ZpZGVyIH0gZnJvbSBcIm9tZi1jaGFuZ2VwYWNrYWdlLWNvbXBvbmVudHNcIjtcclxuaW1wb3J0IHsgVmlld1dpZGdldCwgV2lkZ2V0LCBQYXJhbXNQcm92aWRlciB9IGZyb20gXCJid3RrXCI7XHJcbmltcG9ydCB7IFN0b3JlIH0gZnJvbSBcIi4vc3RvcmVcIjtcclxuaW1wb3J0IHsgSVdpZGdldFByb3BzIH0gZnJvbSBcIi4vbW9kZWxzXCI7XHJcbmltcG9ydCB7IFBpcGUgfSBmcm9tIFwiLi9QaXBlXCI7XHJcbmltcG9ydCB7IENvbmZpZyB9IGZyb20gXCIuL0NvbmZpZ1wiO1xyXG5pbXBvcnQgeyBBcHAgfSBmcm9tIFwiLi9BcHBcIjtcclxuaW1wb3J0IHsgUm9vdCB9IGZyb20gXCJyZWFjdC1kb20vY2xpZW50XCI7XHJcblxyXG5jb25zdCB7XHJcbiAgc2V0V2lkZ2V0UHJvcHMsXHJcbiAgc2V0V2lkZ2V0U3RhdHVzXHJcbn0gPSBBY3Rpb25zO1xyXG5cclxuQFdpZGdldCh7IG5hbWVzcGFjZTogXCJPcmRlcmluZ1wiIH0pXHJcbmV4cG9ydCBkZWZhdWx0IGNsYXNzIFdpZGdldENvbnRhaW5lciBleHRlbmRzIFZpZXdXaWRnZXQge1xyXG4gIGNvbnN0cnVjdG9yKHByaXZhdGUgc3RvcmU6IFN0b3JlLCBwcml2YXRlIHBhcmFtczogUGFyYW1zUHJvdmlkZXI8SVdpZGdldFByb3BzLCBhbnk+LCBwcml2YXRlIGNvbmZpZzogQ29uZmlnLCBwcml2YXRlIHBpcGU6IFBpcGUpIHtcclxuICAgIHN1cGVyKCk7XHJcbiAgfVxyXG5cclxuICAvKipcclxuICAgKiBJbml0aWFsaXplIHdpZGdldCBmbG93XHJcbiAgICogcGxlYXNlIGRvIG5vdCBwbGFjZSBhbnkgc3RhcnR1cCBsb2dpbiBpbiBoZXJlXHJcbiAgICogYWxsIGxvZ2ljIHNob3VsZCByZXNpZGUgaW4gRXBpY3Mub25XaWRnZXRTdGF0dXNFcGljXHJcbiAgICogQG1lbWJlcm9mIFdpZGdldENvbnRhaW5lclxyXG4gICAqL1xyXG4gIGluaXQoKSB7XHJcbiAgICB0aGlzLnBpcGUuc3Vic2NyaWJlKFBpcGUuU3Vic2NyaXB0aW9ucyh0aGlzLnN0b3JlKSk7XHJcbiAgICB0aGlzLnN0b3JlLmRpc3BhdGNoKHNldFdpZGdldFByb3BzKHRoaXMuY29uZmlnKSk7XHJcbiAgICB0aGlzLnN0b3JlLmRpc3BhdGNoKHNldFdpZGdldFByb3BzKHRoaXMucGFyYW1zLnByb3BzKSk7XHJcbiAgICB0aGlzLnN0b3JlLmRpc3BhdGNoKHNldFdpZGdldFN0YXR1cyhFV2lkZ2V0U3RhdHVzLklOSVQpKTtcclxuICB9XHJcblxyXG4gIC8qKlxyXG4gICAqIERlaW5pdGlhbGl6ZSB3aWRnZXQgZmxvd1xyXG4gICAqIERlc3Ryb3kgYWxsIGxpc3RlbmVyZXMgYW5kIGNvbm5lY3Rpb25zXHJcbiAgICogQG1lbWJlcm9mIFdpZGdldENvbnRhaW5lclxyXG4gICAqL1xyXG4gIGRlc3Ryb3koKSB7XHJcbiAgICB0aGlzLnBpcGUudW5zdWJzY3JpYmUoKTtcclxuICAgIHRoaXMuc3RvcmUuZGVzdHJveSgpO1xyXG4gIH1cclxuXHJcbiAgLyoqXHJcbiAgICogUmVuZGVyIHdpZGdldFxyXG4gICAqIFNldCBhbGwgY29udGV4dHVhbCBwcm92aWRlcnM6XHJcbiAgICogKiBDb250ZXh0UHJvdmlkZXI6IHRvcC1tb3N0IHdyYXBwZXIgdXNlZCB0byBwcm9wYWdhdGUgYWxsICppbW11dGFibGUqIHN0YXRlIHBhcmFtc1xyXG4gICAqICogU3RvcmVQcm92aWRlcjogcmVkdXggc3RvcmUgd3JhcHBlciB1c2VkIHRvIHByb3BhZ2F0ZSBhbGwgKm11dGFibGUqIHN0YXRlIHBhcmFtc1xyXG4gICAqIEBwYXJhbSB7RWxlbWVudH0gcm9vdFxyXG4gICAqIEBtZW1iZXJvZiBXaWRnZXRDb250YWluZXJcclxuICAgKi9cclxuICByZW5kZXIocm9vdDogUm9vdCkge1xyXG4gICAgY29uc3QgeyBzdG9yZSB9ID0gdGhpcztcclxuICAgIHJvb3QucmVuZGVyKFxyXG4gICAgICA8Q29udGV4dFByb3ZpZGVyIHZhbHVlPXt7IGNvbmZpZzogdGhpcy5jb25maWcgfX0+XHJcbiAgICAgICAgPFN0b3JlUHJvdmlkZXIgey4uLnsgc3RvcmUgfX0+PEFwcCAvPjwvU3RvcmVQcm92aWRlcj5cclxuICAgICAgPC9Db250ZXh0UHJvdmlkZXI+XHJcbiAgICApO1xyXG4gIH1cclxufVxyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///../src/Widget.tsx\n\n}");

/***/ }),

/***/ "./react/cjs/react-jsx-runtime.development.js":
/*!****************************************************!*\
  !*** ./react/cjs/react-jsx-runtime.development.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("{/**\n * @license React\n * react-jsx-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! react */ \"react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsx = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !1,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n    exports.jsxs = function (type, config, maybeKey, source, self) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        !0,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./react/cjs/react-jsx-runtime.development.js\n\n}");

/***/ }),

/***/ "./react/jsx-runtime.js":
/*!******************************!*\
  !*** ./react/jsx-runtime.js ***!
  \******************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("{\n\nif (false) // removed by dead control flow\n{} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-runtime.development.js */ \"./react/cjs/react-jsx-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9yZWFjdC9qc3gtcnVudGltZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixJQUFJLEtBQXFDLEVBQUU7QUFBQSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSxrSUFBa0U7QUFDcEUiLCJzb3VyY2VzIjpbIm9tZi1jaGFuZ2VwYWNrYWdlLWludGVybmV0Oi8vLy4vcmVhY3QvanN4LXJ1bnRpbWUuanM/Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1ydW50aW1lLmRldmVsb3BtZW50LmpzJyk7XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./react/jsx-runtime.js\n\n}");

/***/ }),

/***/ "bwtk":
/*!**********************************************************************************!*\
  !*** external {"root":"bwtk","commonjs2":"bwtk","commonjs":"bwtk","amd":"bwtk"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_bwtk__;

/***/ }),

/***/ "omf-changepackage-components":
/*!********************************************************************************************************************************************************************************!*\
  !*** external {"root":"OMFChangepackageComponents","commonjs2":"omf-changepackage-components","commonjs":"omf-changepackage-components","amd":"omf-changepackage-components"} ***!
  \********************************************************************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_omf_changepackage_components__;

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

/***/ }),

/***/ "react-intl":
/*!*********************************************************************************************************!*\
  !*** external {"root":"ReactIntl","commonjs2":"react-intl","commonjs":"react-intl","amd":"react-intl"} ***!
  \*********************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_intl__;

/***/ }),

/***/ "react-redux":
/*!*************************************************************************************************************!*\
  !*** external {"root":"ReactRedux","commonjs2":"react-redux","commonjs":"react-redux","amd":"react-redux"} ***!
  \*************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_redux__;

/***/ }),

/***/ "redux":
/*!**************************************************************************************!*\
  !*** external {"root":"Redux","commonjs2":"redux","commonjs":"redux","amd":"redux"} ***!
  \**************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux__;

/***/ }),

/***/ "redux-actions":
/*!*********************************************************************************************************************!*\
  !*** external {"root":"ReduxActions","commonjs2":"redux-actions","commonjs":"redux-actions","amd":"redux-actions"} ***!
  \*********************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_actions__;

/***/ }),

/***/ "redux-observable":
/*!*********************************************************************************************************************************!*\
  !*** external {"root":"ReduxObservable","commonjs2":"redux-observable","commonjs":"redux-observable","amd":"redux-observable"} ***!
  \*********************************************************************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_redux_observable__;

/***/ }),

/***/ "rxjs":
/*!**********************************************************************************!*\
  !*** external {"root":"rxjs","commonjs2":"rxjs","commonjs":"rxjs","amd":"rxjs"} ***!
  \**********************************************************************************/
/***/ (function(module) {

module.exports = __WEBPACK_EXTERNAL_MODULE_rxjs__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval-source-map devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("../src/Widget.tsx");
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});