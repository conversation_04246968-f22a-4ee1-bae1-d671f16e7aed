"use strict";var BwtkLoader=function(){if(void 0===window.bwtkRequireJS)throw Error("BwtkLoader needs RequireJS, which was not found.");var e=this,t={baseUrl:"../node_modules/",registryPath:"",devMode:!1,paths:{},aliases:{},additionalDependencies:[],context:""},r=["redux","react","react-dom","rxjs","redux-actions","react-redux","react-intl","bwtk","redux-observable"],n=window.bwtkRequireJS.require,i=!1;function s(){var r,s,o=(r=t.devMode,s={enforceDefine:!0,deps:["bwtk"],baseUrl:t.baseUrl,paths:{redux:"redux/dist/redux"+(r?"":".min"),react:"react/umd/react."+(r?"development":"production.min"),"react-dom":"react-dom/umd/react-dom."+(r?"development":"production.min"),rxjs:"rxjs/bundles/Rx"+(r?"":".min"),"redux-actions":"redux-actions/dist/redux-actions"+(r?"":".min"),"react-redux":"react-redux/dist/react-redux"+(r?"":".min"),"react-intl":"react-intl/dist/react-intl"+(r?"":".min"),bwtk:"bwtk/dist/bwtk"+(r?"":".min"),"redux-observable":"redux-observable/dist/redux-observable"+(r?"":".min")},onNodeCreated:function(e){e.setAttribute("crossorigin","anonymous")}},t.context&&(s.context=t.context),Object.keys(t.paths).forEach(function(e){s.paths[e]=t.paths[e]}),window.Intl?s.paths.intl&&delete s.paths.intl:s.paths.intl||(s.paths.intl="intl/dist/Intl.min",s.shim={bwtk:{deps:["intl"]}}),Object.keys(t.aliases).length&&(s.map={"*":t.aliases}),s),a=o.context,u=n.config(o);window.define=window.bwtkRequireJS.define,define("rxjs/Observable",["rxjs"],function(e){return e}),define("rxjs/Subject",["rxjs"],function(e){return e}),define("rxjs/operator/filter",["rxjs"],function(e){return e.Observable.prototype}),define("rxjs/operator/map",["rxjs"],function(e){return e.Observable.prototype}),define("rxjs/operator/switch",["rxjs"],function(e){return e.Observable.prototype}),define("rxjs/operator/switchMap",["rxjs"],function(e){return e.Observable.prototype}),define("rxjs/observable/from",["rxjs"],function(e){return e.Observable}),define("rxjs/observable/merge",["rxjs"],function(e){return e.Observable}),define("rxjs/observable/of",["rxjs"],function(e){return e.Observable}),a&&(window.bwtkRequireJS[a]={require:u});var d=["bwtk"];o.paths.bundle&&d.push("bundle"),t.additionalDependencies.forEach(function(e){d.push(e)}),u(d=e.uniqueArray(d),function(){e.onReady(u,a||void 0),i=!1})}return e.reset=function(){for(var t in n.s.contexts){var r=n.s.contexts[t];for(var i in r.defined)r.require.undef(i);delete n.s.contexts[t],delete window.bwtkRequireJS[t]}return e},e.onReady=function(){},e.setPath=function(n,i){var s=i.replace(/\.js$/i,"");return t.paths[n]=s,n in r||t.additionalDependencies.push(n),e},e.setPaths=function(t){return Object.keys(t).forEach(function(r){e.setPath(r,t[r])}),e},e.setBaseUrl=function(r){return t.baseUrl=r,e},e.addWidget=function(r,n){var i=void 0===n?t.registryPath:n;return i=i.replace("{WIDGET}",r),e.setPath(r,i),t.additionalDependencies.push(r),e},e.setBundle=function(t){return this.setPath("bundle",t),e},e.start=function(t){i=!0,"function"==typeof t&&(e.onReady=t),void 0!==window.BwtkPolyfill?window.BwtkPolyfill.ready(s):s()},e.devMode=function(){return t.devMode=!0,e},e.loading=function(){return i},e.setRegistry=function(r){return t.registryPath=r,e},e.addAlias=function(r,n){return t.aliases[n]=r,e},e.multiVersion=function(){return t.context="_"+Math.random().toString(36).substr(2,9),e},e.uniqueArray=function(e){return e.filter(function(e,t,r){return r.indexOf(e)==t})},e};