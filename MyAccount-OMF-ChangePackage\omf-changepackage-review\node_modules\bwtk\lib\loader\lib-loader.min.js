const CURRENT_SCRIPT_SRC=document.currentScript&&document.currentScript.getAttribute("src")||"";class BwtkLoader{fullDomain="";query="";constructor(){if(this.moduleJsFiles=["redux-actions.js",],CURRENT_SCRIPT_SRC)try{let t=new URL(CURRENT_SCRIPT_SRC);this.fullDomain=`${t.protocol}//${t.hostname}`,this.query=t.search}catch(e){console.log("Page not using cached URLs:",e)}this.defaultScripts={polyfill:`${this.fullDomain}/styles/WidgetAssets/bwtk/v2-7/polyfill/polyfill.min.js${this.query}`,redux:"",react:"",reactDom:"",rxjs:"",reduxActions:"",reactRedux:"",reduxObservable:"",inject:`${this.fullDomain}/styles/WidgetAssets/bwtk/inject.min.js${this.query}`,propTypes:`${this.fullDomain}/styles/WidgetAssets/libs/v3/prop-types/prop-types.min.js${this.query}`,reactIntl:`${this.fullDomain}/styles/WidgetAssets/libs/v6/react-intl/react-intl.iife.js${this.query}`,bwtk:`${this.fullDomain}/styles/WidgetAssets/bwtk/v6/bwtk.min.js${this.query}`}}getScriptType(t){return this.moduleJsFiles.some(e=>t.includes(e))||t.endsWith(".mjs")?"module":"text/javascript"}start({mode:t="production",override:e={},bundlePath:s=""},i=()=>{}){let l="development"===t,r=this.fullDomain,d=this.query;this.defaultScripts.redux=`${r}/styles/WidgetAssets/libs/v6/redux/umd/redux.umd.js${d}`,this.defaultScripts.react=l?`${r}/styles/WidgetAssets/libs/v6/react/umd/react.development.js${d}`:`${r}/styles/WidgetAssets/libs/v6/react/umd/react.production.min.js${d}`,this.defaultScripts.reactDom=l?`${r}/styles/WidgetAssets/libs/v6/react-dom/umd/react-dom.development.js${d}`:`${r}/styles/WidgetAssets/libs/v6/react-dom/umd/react-dom.production.min.js${d}`,this.defaultScripts.rxjs=l?`${r}/styles/WidgetAssets/libs/v6/rxjs/bundles/rxjs.umd.js${d}`:`${r}/styles/WidgetAssets/libs/v6/rxjs/bundles/rxjs.umd.min.js${d}`,this.defaultScripts.reduxActions=`${r}/styles/WidgetAssets/libs/v6/redux-actions/redux-actions.umd.js${d}`,this.defaultScripts.reactRedux=`${r}/styles/WidgetAssets/libs/v6/react-redux/umd/react-redux.umd.js${d}`,this.defaultScripts.reduxObservable=`${r}/styles/WidgetAssets/libs/v6/redux-observable/umd/redux-observable.umd.js${d}`,this.defaultScripts.reactIntl=`${r}/styles/WidgetAssets/libs/v6/react-intl/react-intl.iife.js${d}`,this.defaultScripts.bwtk=l?`${r}/styles/WidgetAssets/bwtk/v6/bwtk-6.1.0.js${d}`:`${r}/styles/WidgetAssets/bwtk/v6/bwtk-6.1.0.min.js${d}`;let a=Object.entries({...this.defaultScripts,...e.scripts}).map(([t,e])=>e),u=e.additionalScripts||[];a.push(...u),a.push(`${r}${s}${d}`);let c=[this.defaultScripts.inject,this.defaultScripts.react,this.defaultScripts.redux,this.defaultScripts.rxjs],n=a.filter(t=>!c.includes(t));this.loadScriptsInParallel(c).then(()=>this.loadScriptsSequentially(n)).then(()=>this.onReady(i)).catch(t=>console.error("Error loading scripts:",t))}loadScriptsInParallel(t){return Promise.all(t.map(t=>new Promise((e,s)=>{let i=document.createElement("script");i.src=t,i.type=this.getScriptType(t),i.onload=e,i.onerror=()=>s(Error(`Failed to load script: ${t}`)),document.head.appendChild(i)})))}loadScriptsSequentially(t){return t.reduce((t,e)=>t.then(()=>new Promise((t,s)=>{let i=document.createElement("script");i.src=e,i.type=this.getScriptType(e),i.onload=t,i.onerror=()=>s(Error(`Failed to load script: ${e}`)),document.head.appendChild(i)})),Promise.resolve())}onReady(t){if(void 0===window.Bundle&&(window.Bundle=window.bundle),void 0!==window.Bundle&&"function"==typeof window.Bundle.initialize){let e=window.Bundle;t(e)}else console.error("Bundle or Bundle.initialize is not defined");console.log("All scripts loaded")}}