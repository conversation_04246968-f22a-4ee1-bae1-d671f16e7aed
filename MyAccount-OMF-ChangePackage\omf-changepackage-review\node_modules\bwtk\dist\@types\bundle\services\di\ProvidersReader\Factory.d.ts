import { DependenciesMetadataReader } from "./DependenciesMetadataReader";
export declare class Factory {
    private ctor;
    private _dependencies?;
    constructor(ctor: any, _dependencies?: any[] | undefined);
    get factory(): Function;
    private _factory;
    private readFactoryFromProvideMetadata;
    get dependencies(): any[];
    private readDependenciesFromMetadata;
    get dependenciesMetadataReader(): DependenciesMetadataReader;
    private static createFactoryFunctionFromConstructor;
    private static createFactoryFunctionFromFactory;
    private static createFactoryFunction;
}
