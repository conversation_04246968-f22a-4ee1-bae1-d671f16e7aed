import { combineReducers } from "redux";
import { Action, handleActions } from "redux-actions";
import { combineEpics } from "redux-observable";
import { Reducers, LifecycleEpics, RestricitonsEpics, ModalEpics } from "omf-changepackage-components";

import { Store as BwtkStore, Injectable, CommonFeatures } from "bwtk";


import * as actions from "./Actions";

import { IStoreState, IAvailableDates, IInstallationAddress, IContactInformation, IAdditionalDetails } from "../models";
import { Epics } from "./Epics";
import { Localization } from "../Localization";
import { EDuration } from "../models/Enums";
import { Client } from "../Client";

const { BaseStore, actionsToComputedPropertyName } = CommonFeatures;
const {
  setAvailableDates,
  // setPreferredDate,
  setDuration,
  setInstallationAddress,
  contactInformation,
  setAdditionalDetails,
  setIsInstallationRequired
} = actionsToComputedPropertyName(actions);

@Injectable
export class Store extends BaseStore<IStoreState> {
  constructor(private client: Client, store: BwtkStore, private epics: Epics, private localization: Localization) {
    super(store);
  }

  get reducer() {
    return combineReducers({
      // =========== Widget lifecycle methods =============
      ...Reducers.WidgetBaseLifecycle(this.localization),
      ...Reducers.WidgetLightboxes(),
      ...Reducers.WidgetRestrictions(),
      // =========== Widget data ===============
      availableDates: handleActions<Array<IAvailableDates> | null>({
        [setAvailableDates]: (state, { payload }: Action<Array<IAvailableDates>>) => payload || state,
      }, null),
      // preferredDate: handleActions<IAvailableDates | null>({
      //   [setPreferredDate]: (state, { payload }: Action<IAvailableDates>) => payload || state,
      // }, null),
      duration: handleActions<EDuration | null>({
        [setDuration]: (state, { payload }: Action<EDuration>) => payload || state,
      }, null),
      installationAddress: handleActions<IInstallationAddress | null>({
        [setInstallationAddress]: (state, { payload }: Action<IInstallationAddress>) => payload || state,
      }, null),
      contactInformation: handleActions<IContactInformation | null>({
        [contactInformation]: (state, { payload }: Action<IContactInformation>) => payload || state,
      }, null),
      additionalDetails: handleActions<IAdditionalDetails | null>({
        [setAdditionalDetails]: (state, { payload }: Action<IAdditionalDetails>) => payload || state,
      }, null),
      isInstallationRequired: handleActions<boolean>({
        [setIsInstallationRequired]: (state, { payload }: Action<boolean>) => payload || state,
      }, false),
    } as any) as any;
  }

  /**
   * Middlewares are collected bottom-to-top
   * so, the bottom-most epic will receive the
   * action first, while the top-most -- last
   * @readonly
   * @memberof Store
   */
  get middlewares(): any {
    return combineEpics(this.epics.omnitureEpics.combineEpics(), this.epics.appointmentEpics.combineEpics(), 
      this.epics.combineEpics(), new ModalEpics().combineEpics(), new RestricitonsEpics(this.client, "APPOINTMENT_RESTRICTION_MODAL").combineEpics(),
      new LifecycleEpics().combineEpics());
  }
}
