import { FormattedMessage } from "react-intl";
import { useFormContext } from "react-hook-form";

interface ComponentProps {
  label: string;
  value: string;
  handleChange: Function;
  checked?: boolean;
  requiredInput?: boolean;
}

const defaultProps = {
  checked: false,
  requiredInput: false
};

export const RadioBtn = (props: ComponentProps) => {
  const privateProps: ComponentProps = { ...defaultProps, ...props};
  const { label, value, handleChange, checked, requiredInput } = privateProps;
  const { register }: any = useFormContext();

  return  label ? <label className="graphical_ctrl pointer ctrl_radioBtn margin-10-bottom">
    <label className="txtBold block" htmlFor={`option_${value}`}><FormattedMessage id={value} /></label>
    <input
      type="radio"
      id={`option_${value}`}
      ref={register({ required: requiredInput})}
      name={label}
      value={value}
      checked={checked}
      onChange={(e) => handleChange(e)}
    />
    <span className="ctrl_element"></span>

    {/* Show top arrow above the calendar, when other is selected*/}
    { value === "OTHER" && checked === true ? <span className="topArrow text-left otherOption" aria-hidden="true"></span> : null }
  </label> : null;
};
