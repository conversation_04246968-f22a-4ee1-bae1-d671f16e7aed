/*! BWTK 6.1.0 | 2025-06-26T18:50:51.128Z */
var root,factory;root=self,factory=(e,t,r,n,i,o,a,s)=>(()=>{function c(e){var t,r=l[e];return void 0!==r?r.exports:(t=l[e]={exports:{}},f[e](t,t.exports,c),t.exports)}var u,f={3:e=>{"use strict";e.exports=o},406:e=>{"use strict";function t(){t.init.call(this)}function r(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function n(e){return void 0===e._maxListeners?t.defaultMaxListeners:e._maxListeners}function i(e,t,i,o){var a,s,c,u;return r(i),void 0===(s=e._events)?(s=e._events=Object.create(null),e._eventsCount=0):(void 0!==s.newListener&&(e.emit("newListener",t,i.listener?i.listener:i),s=e._events),c=s[t]),void 0===c?(c=s[t]=i,++e._eventsCount):("function"==typeof c?c=s[t]=o?[i,c]:[c,i]:o?c.unshift(i):c.push(i),(a=n(e))>0&&c.length>a&&!c.warned&&(c.warned=!0,(u=new Error("Possible EventEmitter memory leak detected. "+c.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit")).name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=c.length,console&&console.warn)),e}function o(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function a(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=o.bind(n);return i.listener=r,n.wrapFn=i,i}function s(e,t,r){var n,i=e._events;return void 0===i||void 0===(n=i[t])?[]:"function"==typeof n?r?[n.listener||n]:[n]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(n):u(n,n.length)}function c(e){var t,r=this._events;if(void 0!==r){if("function"==typeof(t=r[e]))return 1;if(void 0!==t)return t.length}return 0}function u(e,t){var r,n=new Array(t);for(r=0;r<t;++r)n[r]=e[r];return n}function f(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function i(o){n.once&&e.removeEventListener(t,i),r(o)}))}}var l,d,p,h="object"==typeof Reflect?Reflect:null,y=h&&"function"==typeof h.apply?h.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};l=h&&"function"==typeof h.ownKeys?h.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)},d=Number.isNaN||function(e){return e!=e},e.exports=t,e.exports.once=function(e,t){return new Promise((function(r,n){function i(r){e.removeListener(t,o),n(r)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}f(e,t,o,{once:!0}),"error"!==t&&function(e,t){"function"==typeof e.on&&f(e,"error",t,{once:!0})}(e,i)}))},t.EventEmitter=t,t.prototype._events=void 0,t.prototype._eventsCount=0,t.prototype._maxListeners=void 0,p=10,Object.defineProperty(t,"defaultMaxListeners",{enumerable:!0,get:function(){return p},set:function(e){if("number"!=typeof e||e<0||d(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");p=e}}),t.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},t.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||d(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},t.prototype.getMaxListeners=function(){return n(this)},t.prototype.emit=function(e){var t,r,n,i,o,a,s,c,f=[];for(t=1;t<arguments.length;t++)f.push(arguments[t]);if(r="error"===e,void 0!==(n=this._events))r=r&&void 0===n.error;else if(!r)return!1;if(r){if(f.length>0&&(i=f[0]),i instanceof Error)throw i;throw(o=new Error("Unhandled error."+(i?" ("+i.message+")":""))).context=i,o}if(void 0===(a=n[e]))return!1;if("function"==typeof a)y(a,this,f);else for(c=u(a,s=a.length),t=0;t<s;++t)y(c[t],this,f);return!0},t.prototype.addListener=function(e,t){return i(this,e,t,!1)},t.prototype.on=t.prototype.addListener,t.prototype.prependListener=function(e,t){return i(this,e,t,!0)},t.prototype.once=function(e,t){return r(t),this.on(e,a(this,e,t)),this},t.prototype.prependOnceListener=function(e,t){return r(t),this.prependListener(e,a(this,e,t)),this},t.prototype.removeListener=function(e,t){var n,i,o,a,s;if(r(t),void 0===(i=this._events))return this;if(void 0===(n=i[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(o=-1,a=n.length-1;a>=0;a--)if(n[a]===t||n[a].listener===t){s=n[a].listener,o=a;break}if(o<0)return this;0===o?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,o),1===n.length&&(i[e]=n[0]),void 0!==i.removeListener&&this.emit("removeListener",e,s||t)}return this},t.prototype.off=t.prototype.removeListener,t.prototype.removeAllListeners=function(e){var t,r,n,i,o=this._events;if(void 0===o)return this;if(void 0===o.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==o[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete o[e]),this;if(0===arguments.length){for(n=Object.keys(o),r=0;r<n.length;++r)"removeListener"!==(i=n[r])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=o[e]))this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},t.prototype.listeners=function(e){return s(this,e,!0)},t.prototype.rawListeners=function(e){return s(this,e,!1)},t.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):c.call(e,t)},t.prototype.listenerCount=c,t.prototype.eventNames=function(){return this._eventsCount>0?l(this._events):[]}},418:e=>{"use strict";e.exports=a},419:e=>{"use strict";e.exports=t},442:e=>{"use strict";e.exports=i},489:(e,t,r)=>{"use strict";var n=r(3);t.H=n.createRoot,n.hydrateRoot},541:e=>{"use strict";e.exports=n},663:e=>{e.exports=function(e){switch(void 0===e&&(e=""),e.toLowerCase()){case"bwtk":return"bwtk";case"jquery":return"$";case"rxjs":return"rxjs";default:return e.split("-").map((function(e){return e.length>3?e[0].toUpperCase()+e.slice(1):e.toUpperCase()})).join("")}}},750:e=>{"use strict";e.exports=s},769:t=>{"use strict";t.exports=e},873:e=>{e.exports=function(e){function t(n){if(r[n])return r[n].exports;var i=r[n]={exports:{},id:n,loaded:!1};return e[n].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}var r={};return t.m=e,t.c=r,t.p="",t(0)}([function(e,t,r){"use strict";var n,i;Object.defineProperty(t,"__esModule",{value:!0}),n=r(1),Object.defineProperty(t,"Injector",{enumerable:!0,get:function(){return n.Injector}}),i=r(2),Object.defineProperty(t,"annotate",{enumerable:!0,get:function(){return i.annotate}}),Object.defineProperty(t,"Inject",{enumerable:!0,get:function(){return i.Inject}}),Object.defineProperty(t,"InjectLazy",{enumerable:!0,get:function(){return i.InjectLazy}}),Object.defineProperty(t,"InjectPromise",{enumerable:!0,get:function(){return i.InjectPromise}}),Object.defineProperty(t,"Provide",{enumerable:!0,get:function(){return i.Provide}}),Object.defineProperty(t,"ProvidePromise",{enumerable:!0,get:function(){return i.ProvidePromise}}),Object.defineProperty(t,"SuperConstructor",{enumerable:!0,get:function(){return i.SuperConstructor}}),Object.defineProperty(t,"TransientScope",{enumerable:!0,get:function(){return i.TransientScope}}),Object.defineProperty(t,"ClassProvider",{enumerable:!0,get:function(){return i.ClassProvider}}),Object.defineProperty(t,"FactoryProvider",{enumerable:!0,get:function(){return i.FactoryProvider}})},function(e,t,r){"use strict";function n(e,t){return arguments.length>1&&e.push(t),e.length>1?" ("+e.map(a.toString).join(" -> ")+")":""}var i,o,a,s,c,u;Object.defineProperty(t,"__esModule",{value:!0}),t.Injector=void 0,i=function(){function e(e,t){var r,n;for(r=0;r<t.length;r++)(n=t[r]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),o=r(2),a=r(3),s=r(4),c=r(6),u=function(){function e(){var t=arguments.length<=0||void 0===arguments[0]?[]:arguments[0],r=arguments.length<=1||void 0===arguments[1]?null:arguments[1],n=arguments.length<=2||void 0===arguments[2]?new Map:arguments[2],i=arguments.length<=3||void 0===arguments[3]?[]:arguments[3];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this._cache=new Map,this._providers=n,this._parent=r,this._scopes=i,this._loadModules(t),(0,s.profileInjector)(this,e)}return i(e,[{key:"_collectProvidersWithAnnotation",value:function(e,t){this._providers.forEach((function(r,n){!t.has(n)&&(0,o.hasAnnotation)(r.provider,e)&&t.set(n,r)})),this._parent&&this._parent._collectProvidersWithAnnotation(e,t)}},{key:"_loadModules",value:function(e){var t,r,n,i=!0,o=!1,s=void 0;try{for(t=e[Symbol.iterator]();!(i=(r=t.next()).done);i=!0){if(n=r.value,!(0,a.isFunction)(n))throw new Error("Invalid module!");this._loadFnOrClass(n)}}catch(c){o=!0,s=c}finally{try{!i&&t.return&&t.return()}finally{if(o)throw s}}}},{key:"_loadFnOrClass",value:function(e){var t=(0,o.readAnnotations)(e),r=t.provide.token||e,n=(0,c.createProviderFromFnOrClass)(e,t);this._providers.set(r,n)}},{key:"_hasProviderFor",value:function(e){return!!this._providers.has(e)||!!this._parent&&this._parent._hasProviderFor(e)}},{key:"_instantiateDefaultProvider",value:function(e,t,r,n,i){var a,s,c,u,f,l;if(!this._parent)return this._providers.set(t,e),this.get(t,r,n,i);a=!0,s=!1,c=void 0;try{for(u=this._scopes[Symbol.iterator]();!(a=(f=u.next()).done);a=!0)if(l=f.value,(0,o.hasAnnotation)(e.provider,l))return this._providers.set(t,e),this.get(t,r,n,i)}catch(d){s=!0,c=d}finally{try{!a&&u.return&&u.return()}finally{if(s)throw c}}return this._parent._instantiateDefaultProvider(e,t,r,n,i)}},{key:"get",value:function(t){var r,i,s,u,f,l,d=arguments.length<=1||void 0===arguments[1]?[]:arguments[1],p=this,h=!(arguments.length<=2||void 0===arguments[2])&&arguments[2],y=!(arguments.length<=3||void 0===arguments[3])&&arguments[3],g="",v=this;if(null==t)throw g=n(d,t),new Error('Invalid token "'+t+'" requested!'+g);if(t===e)return h?Promise.resolve(this):this;if(y)return function(){var e,r,n,i=v;if(arguments.length){for(e=[],r=arguments,n=0;n<r.length;n+=2)e.push(function(e){var t=function(){return r[e+1]};return(0,o.annotate)(t,new o.Provide(r[e])),t}(n));i=v.createChild(e)}return i.get(t,d,h,!1)};if(this._cache.has(t)){if(i=this._cache.get(t),(r=this._providers.get(t)).isPromise&&!h)throw g=n(d,t),new Error("Cannot instantiate "+(0,a.toString)(t)+" synchronously. It is provided as a promise!"+g);return!r.isPromise&&h?Promise.resolve(i):i}if(!(r=this._providers.get(t))&&(0,a.isFunction)(t)&&!this._hasProviderFor(t))return r=(0,c.createProviderFromFnOrClass)(t,(0,o.readAnnotations)(t)),this._instantiateDefaultProvider(r,t,d,h,y);if(!r){if(!this._parent)throw g=n(d,t),new Error("No provider for "+(0,a.toString)(t)+"!"+g);return this._parent.get(t,d,h,y)}if(-1!==d.indexOf(t))throw g=n(d,t),new Error("Cannot instantiate cyclic dependency!"+g);if(d.push(t),s=h&&r.params.some((function(e){return!e.isPromise})),u=r.params.map((function(e){return s?p.get(e.token,d,!0,e.isLazy):p.get(e.token,d,e.isPromise,e.isLazy)})),s)return f=d.slice(),d.pop(),Promise.all(u).then((function(e){try{i=r.create(e)}catch(c){g=n(f);var s="ORIGINAL ERROR: "+c.message;throw c.message="Error during instantiation of "+(0,a.toString)(t)+"!"+g+"\n"+s,c}return(0,o.hasAnnotation)(r.provider,o.TransientScope)||v._cache.set(t,i),i}));try{i=r.create(u)}catch(b){throw g=n(d),l="ORIGINAL ERROR: "+b.message,b.message="Error during instantiation of "+(0,a.toString)(t)+"!"+g+"\n"+l,b}if((0,o.hasAnnotation)(r.provider,o.TransientScope)||this._cache.set(t,i),!h&&r.isPromise)throw g=n(d),new Error("Cannot instantiate "+(0,a.toString)(t)+" synchronously. It is provided as a promise!"+g);return h&&!r.isPromise&&(i=Promise.resolve(i)),d.pop(),i}},{key:"getPromise",value:function(e){return this.get(e,[],!0)}},{key:"createChild",value:function(){var t,r,n,i,a,s,c=arguments.length<=0||void 0===arguments[0]?[]:arguments[0],u=arguments.length<=1||void 0===arguments[1]?[]:arguments[1],f=new Map;u.push(o.TransientScope),t=!0,r=!1,n=void 0;try{for(i=u[Symbol.iterator]();!(t=(a=i.next()).done);t=!0)s=a.value,this._collectProvidersWithAnnotation(s,f)}catch(l){r=!0,n=l}finally{try{!t&&i.return&&i.return()}finally{if(r)throw n}}return new e(c,this,f,u)}}]),e}(),t.Injector=u},function(e,t,r){"use strict";function n(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function i(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var a,s,c,u,f,l,d,p,h,y;Object.defineProperty(t,"__esModule",{value:!0}),t.FactoryProvider=t.ClassProvider=t.ProvidePromise=t.Provide=t.InjectLazy=t.InjectPromise=t.Inject=t.TransientScope=t.SuperConstructor=t.readAnnotations=t.hasAnnotation=t.annotate=void 0,a=r(3),s=function e(){o(this,e)},c=function e(){o(this,e)},u=function e(){o(this,e);for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];this.tokens=r,this.isPromise=!1,this.isLazy=!1},f=function(e){function t(){var e,r,i,a;for(o(this,t),e=n(this,Object.getPrototypeOf(t).call(this)),r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return e.tokens=i,e.isPromise=!0,e.isLazy=!1,e}return i(t,e),t}(u),l=function(e){function t(){var e,r,i,a;for(o(this,t),e=n(this,Object.getPrototypeOf(t).call(this)),r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return e.tokens=i,e.isPromise=!1,e.isLazy=!0,e}return i(t,e),t}(u),p=function(e){function t(e){o(this,t);var r=n(this,Object.getPrototypeOf(t).call(this));return r.token=e,r.isPromise=!0,r}return i(t,e),t}(d=function e(t){o(this,e),this.token=t,this.isPromise=!1}),h=function e(){o(this,e)},y=function e(){o(this,e)},t.annotate=function(e,t){e.annotations=e.annotations||[],e.annotations.push(t)},t.hasAnnotation=function(e,t){var r,n,i,o,a;if(!e.annotations||0===e.annotations.length)return!1;r=!0,n=!1,i=void 0;try{for(o=e.annotations[Symbol.iterator]();!(r=(a=o.next()).done);r=!0)if(a.value instanceof t)return!0}catch(s){n=!0,i=s}finally{try{!r&&o.return&&o.return()}finally{if(n)throw i}}return!1},t.readAnnotations=function(e){var t,r,n,i,o,s,c={provide:{token:null,isPromise:!1},params:[]};if(e.annotations&&e.annotations.length){t=!0,r=!1,n=void 0;try{for(i=e.annotations[Symbol.iterator]();!(t=(o=i.next()).done);t=!0)(s=o.value)instanceof u&&s.tokens.forEach((function(e){c.params.push({token:e,isPromise:s.isPromise,isLazy:s.isLazy})})),s instanceof d&&(c.provide.token=s.token,c.provide.isPromise=s.isPromise)}catch(f){r=!0,n=f}finally{try{!t&&i.return&&i.return()}finally{if(r)throw n}}}return e.parameters&&e.parameters.forEach((function(e,t){var r,n,i,o=!0,s=!1,l=void 0;try{for(r=e[Symbol.iterator]();!(o=(n=r.next()).done);o=!0)i=n.value,(0,a.isFunction)(i)&&!c.params[t]?c.params[t]={token:i,isPromise:!1,isLazy:!1}:i instanceof u&&(c.params[t]={token:i.tokens[0],isPromise:i.isPromise,isLazy:i.isLazy})}catch(f){s=!0,l=f}finally{try{!o&&r.return&&r.return()}finally{if(s)throw l}}})),c},t.SuperConstructor=s,t.TransientScope=c,t.Inject=u,t.InjectPromise=f,t.InjectLazy=l,t.Provide=d,t.ProvidePromise=p,t.ClassProvider=h,t.FactoryProvider=y},function(e,t){(function(e){"use strict";var r,n;Object.defineProperty(t,"__esModule",{value:!0}),r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},n=e.Reflect&&Reflect.ownKeys?Reflect.ownKeys:function(e){var t=Object.getOwnPropertyNames(e);return Object.getOwnPropertySymbols?t.concat(Object.getOwnPropertySymbols(e)):t},t.isUpperCase=function(e){return e.toUpperCase()===e},t.isFunction=function(e){return"function"==typeof e},t.isObject=function(e){return"object"===(void 0===e?"undefined":r(e))},t.toString=function(e){return"string"==typeof e?e:null==e?""+e:e.name?e.name:e.toString()},t.ownKeys=n}).call(t,function(){return this}())},function(e,t,r){(function(e,n){"use strict";function i(e,t){return t.has(e)||t.set(e,(++u).toString()),t.get(e)}var o,a,s,c,u;Object.defineProperty(t,"__esModule",{value:!0}),o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol?"symbol":typeof e},t.profileInjector=function(e,t){s&&(c.__di_dump__||(c.__di_dump__={injectors:[],tokens:new Map}),c.__di_dump__.injectors.push(function(e,t,r){var n={id:i(e,t),parent_id:e._parent?i(e._parent,t):null,providers:{}},o=i(r,t);return n.providers[o]={id:o,name:(0,a.toString)(r),isPromise:!1,dependencies:[]},e._providers.forEach((function(e,r){var o=function(e,t,r){return{id:i(t,r),name:(0,a.toString)(t),isPromise:e.isPromise,dependencies:e.params.map((function(e){return{token:i(e.token,r),isPromise:e.isPromise,isLazy:e.isLazy}}))}}(e,r,t);n.providers[o.id]=o})),n}(e,c.__di_dump__.tokens,t)))},a=r(3),s=!1,c=null,"object"===(void 0===e?"undefined":o(e))&&e.env?(s=!!e.env.DEBUG,c=n):"object"===("undefined"==typeof location?"undefined":o(location))&&location.search&&(s=/di_debug/.test(location.search),c=window),u=0}).call(t,r(5),function(){return this}())},function(e,t){function r(){u=!1,a.length?c=a.concat(c):f=-1,c.length&&n()}function n(){var e,t;if(!u){for(e=setTimeout(r),u=!0,t=c.length;t;){for(a=c,c=[];++f<t;)a&&a[f].run();f=-1,t=c.length}a=null,u=!1,clearTimeout(e)}}function i(e,t){this.fun=e,this.array=t}function o(){}var a,s=e.exports={},c=[],u=!1,f=-1;s.nextTick=function(e){var t,r=new Array(arguments.length-1);if(arguments.length>1)for(t=1;t<arguments.length;t++)r[t-1]=arguments[t];c.push(new i(e,r)),1!==c.length||u||setTimeout(n,0)},i.prototype.run=function(){this.fun.apply(null,this.array)},s.title="browser",s.browser=!0,s.env={},s.argv=[],s.version="",s.versions={},s.on=o,s.addListener=o,s.once=o,s.off=o,s.removeListener=o,s.removeAllListeners=o,s.emit=o,s.binding=function(e){throw new Error("process.binding is not supported")},s.cwd=function(){return"/"},s.chdir=function(e){throw new Error("process.chdir is not supported")},s.umask=function(){return 0}},function(e,t,r){"use strict";function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var i,o,a,s,c,u;Object.defineProperty(t,"__esModule",{value:!0}),i=function(){function e(e,t){var r,n;for(r=0;r<t.length;r++)(n=t[r]).enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}return function(t,r,n){return r&&e(t.prototype,r),n&&e(t,n),t}}(),t.createProviderFromFnOrClass=function(e,t){return r=e,(0,o.hasAnnotation)(r,o.ClassProvider)||!(0,o.hasAnnotation)(r,o.FactoryProvider)&&(r.name?(0,a.isUpperCase)(r.name.charAt(0)):(0,a.ownKeys)(r.prototype).length>0)?new c(e,t.params,t.provide.isPromise):new u(e,t.params,t.provide.isPromise);var r},o=r(2),a=r(3),s=Object.getPrototypeOf(Function),c=function(){function e(t,r,i){n(this,e),this.provider=t,this.isPromise=i,this.params=[],this._constructors=[],this._flattenParams(t,r),this._constructors.unshift([t,0,this.params.length-1])}return i(e,[{key:"_flattenParams",value:function(e,t){var r,n,i,c,u,f=!0,l=!1,d=void 0;try{for(i=t[Symbol.iterator]();!(f=(c=i.next()).done);f=!0)if((u=c.value).token===o.SuperConstructor){if((r=Object.getPrototypeOf(e))===s)throw new Error((0,a.toString)(e)+" does not have a parent constructor. Only classes with a parent can ask for SuperConstructor!");n=[r,this.params.length],this._constructors.push(n),this._flattenParams(r,(0,o.readAnnotations)(r).params),n.push(this.params.length-1)}else this.params.push(u)}catch(p){l=!0,d=p}finally{try{!f&&i.return&&i.return()}finally{if(l)throw d}}}},{key:"_createConstructor",value:function(e,t,r){var n,i=this._constructors[e],o=this._constructors[e+1];return n=o?r.slice(i[1],o[1]).concat([this._createConstructor(e+1,t,r)]).concat(r.slice(o[2]+1,i[2]+1)):r.slice(i[1],i[2]+1),function(){return i[0].apply(t,n)}}},{key:"create",value:function(e){var t=Object.create(this.provider.prototype),r=this._createConstructor(0,t,e)();return(0,a.isFunction)(r)||(0,a.isObject)(r)?r:t}}]),e}(),u=function(){function e(t,r,i){var s,c,u,f,l;n(this,e),this.provider=t,this.params=r,this.isPromise=i,s=!0,c=!1,u=void 0;try{for(f=r[Symbol.iterator]();!(s=(l=f.next()).done);s=!0)if(l.value.token===o.SuperConstructor)throw new Error((0,a.toString)(t)+" is not a class. Only classes with a parent can ask for SuperConstructor!")}catch(d){c=!0,u=d}finally{try{!s&&f.return&&f.return()}finally{if(c)throw u}}}return i(e,[{key:"create",value:function(e){return this.provider.apply(void 0,e)}}]),e}()}])},999:e=>{"use strict";e.exports=r}},l={};return c.d=(e,t)=>{for(var r in t)c.o(t,r)&&!c.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},c.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),c.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},u={},(()=>{"use strict";function e(e,t){function r(){this.constructor=e}if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");P(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}function t(e,t,r,n){var i,o,a=arguments.length,s=a<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,r):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,r,n);else for(o=e.length-1;o>=0;o--)(i=e[o])&&(s=(a<3?i(s):a>3?i(t,r,s):i(t,r))||s);return a>3&&s&&Object.defineProperty(t,r,s),s}function r(e,t){return function(r,n){t(r,n,e)}}function n(e,t){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(e,t)}function i(e,t){var r,n,i,o,a="function"==typeof Symbol&&e[Symbol.iterator];if(!a)return e;r=a.call(e),i=[];try{for(;(void 0===t||t-- >0)&&!(n=r.next()).done;)i.push(n.value)}catch(s){o={error:s}}finally{try{n&&!n.done&&(a=r.return)&&a.call(r)}finally{if(o)throw o.error}}return i}function o(e,t,r){if(r||2===arguments.length)for(var n,i=0,o=t.length;i<o;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))}function a(e){}function s(e,t,r){try{if(lt.has(t))try{l(t),setTimeout((function(){f(e,t,r)}),0)}catch(n){f(e,t,r)}else f(e,t,r)}catch(n){}}function f(e,t,r){var n,i;try{n=(0,Oe.H)(t),i=we.createElement(Pe,j(j({},r),{widget:e})),n.render(i),lt.set(t,n)}catch(o){}}function l(e){if(lt.has(e))try{lt.get(e).unmount(),lt.delete(e)}catch(t){lt.delete(e)}}function d(e){return"function"==typeof e}function p(e){return void 0!==e.factory}function h(e){return"string"==typeof e}function y(e){return g(Z.Di,new Qe((function(){return new kt(e)})).factory)}function g(e,t){return[et.provide(e,t,!0),et.provide(v(e),new Qe((function(e){return e}),[e]).factory,!0)]}function v(e){switch(e){case Z.Ajax:return N;case Z.Store:return Y;case Z.Config:return z;case Z.Localization:return G;case Z.Loader:return K;case Z.Di:return q;case Z.Logger:return U;case Z.EventStream:return M;default:return null}}var b,m,w,O,P,j,E,S,_,C,L,R,I,k,M,A,D,x,F,T,N,z,W,q,K,B,G,U,V,X,J,Y,H,Z,Q,$,ee,te,re,ne,ie,oe,ae,se,ce,ue,fe,le,de,pe,he,ye,ge,ve,be,me,we,Oe,Pe,je,Ee,Se,_e,Ce,Le,Re,Ie,ke,Me,Ae,De,xe,Fe,Te,Ne,ze,We,qe,Ke,Be,Ge,Ue,Ve,Xe,Je,Ye,He,Ze,Qe,$e,et,tt,rt,nt,it,ot,at,st,ct,ut,ft,lt,dt,pt,ht,yt,gt,vt,bt,mt,wt,Ot,Pt,jt,Et,St,_t,Ct,Lt,Rt,It,kt,Mt,At,Dt,xt,Ft,Tt,Nt,zt,Wt;c.r(u),c.d(u,{AjaxServices:()=>N,ApplicationError:()=>de,CallbackExecutionError:()=>F,CommonFeatures:()=>Ne,CommonServices:()=>Z,ConfigActions:()=>W,ConfigServices:()=>z,DestroyAll:()=>zt,DestroyWidget:()=>l,DiServices:()=>q,ErrorBoundary:()=>ze,ErrorStreamErrors:()=>D,EventStream:()=>A,EventStreamServices:()=>M,Id:()=>ye,Init:()=>Ft,Injectable:()=>a,InternalError:()=>le,LoaderConfigKeys:()=>be,Localization:()=>B,LocalizationConfigKeys:()=>me,LocalizationServices:()=>G,Logger:()=>V,LoggerConfigKeys:()=>J,LoggerServices:()=>U,LoggerSeverityLevel:()=>X,Name:()=>ge,Namespace:()=>ve,ParamsProvider:()=>Ee,ParamsServices:()=>je,Provide:()=>ae,ReduxError:()=>he,RenderWidget:()=>s,ServiceLocator:()=>x,Store:()=>H,StoreServices:()=>Y,ValidationError:()=>pe,ViewWidget:()=>fe,Widget:()=>se,WidgetLoader:()=>Pe,WidgetLoaderServices:()=>K,factory:()=>ee,injectWithFactory:()=>ne,loadMessages:()=>I,localeChanged:()=>R,messagesLoaded:()=>k,raiseError:()=>C,setLocale:()=>L}),b=c(769),m=c(419),w=c(999),O={window:"undefined"!=typeof window?window:{}},P=function(e,t){return P=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])},P(e,t)},j=function(){return j=Object.assign||function(e){var t,r,n,i;for(r=1,n=arguments.length;r<n;r++)for(i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},j.apply(this,arguments)},Object.create,Object.create,E=function(e){return E=Object.getOwnPropertyNames||function(e){var t,r=[];for(t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[r.length]=t);return r},E(e)},"function"==typeof SuppressedError&&SuppressedError,S=c(406),_=c(541),C=(0,_.createAction)("INTERNAL_ERROR"),L=(0,_.createAction)("SET_LOCALE"),R=(0,_.createAction)("LOCALE_CHANGED"),I=(0,_.createAction)("LOAD_MESSAGES"),k=(0,_.createAction)("MESSAGES_LOADED"),M=function(){},A=function(){},function(e){e.GENERIC="ERROR_GENERIC",e.STREAM="ERROR_EVENT_STREAM",e.AJAX="ERROR_AJAX",e.REDUX="ERROR_REDUX",e.LOCALIZATION="ERROR_LOCALIZATION",e.REACT_BOUNDARY="ERROR_REACT_BOUNDARY"}(D||(D={})),x=function(){function e(){}return Object.defineProperty(e.prototype,"di",{get:function(){return this.getService("di")},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"loader",{get:function(){return this.getService("loader")},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"store",{get:function(){return this.getService("store")},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"stream",{get:function(){return this.getService("stream")},enumerable:!1,configurable:!0}),e.prototype.getWidget=function(e,t,r){var n=this;this.createWidgetInstances(e).then((function(r){return new Promise((function(i,o){try{t.apply(null,r),i(void 0)}catch(a){n.stream.sendError(D.GENERIC,a,"string"==typeof e?e:void 0),n.store.dispatch(C(a)),o(new F(a))}}))})).catch((function(e){r&&r.apply(null,[e])}))},e.prototype.createWidgetInstances=function(e){var t="string"==typeof e?[e]:e;return Array.isArray(t)?this.doCreateWidgetInstances(t):Promise.resolve([])},e.prototype.doCreateWidgetInstances=function(e){var t=this;return this.loadWidgets(e).then((function(e){return Promise.resolve(e.map((function(e){return t.di.createInstance(e.id,e.type)})))}))},e.prototype.loadWidgets=function(e){return Promise.all(this.getLoadWidgetPromises(e))},e.prototype.getLoadWidgetPromises=function(e){var t=this;return e.map((function(e){return new Promise((function(r,n){t.loader.load(e).then((function(t){return r({id:e,type:t})})).catch(n)}))}))},Object.defineProperty(e,"instance",{get:function(){return e.instanceProvider()},enumerable:!1,configurable:!0}),e.setInstanceProvider=function(t){e.instanceProvider=t,this.emitOnReady()},e.emitOnReady=function(){this.events.emit("ready")},e.onReady=function(e){this.events.once("ready",e),this.instance&&this.emitOnReady()},Object.defineProperty(e,"instanceProvider",{get:function(){return this._instanceProvider},set:function(e){this._instanceProvider=e},enumerable:!1,configurable:!0}),e.events=new S.EventEmitter,e._instanceProvider=function(){return null},e}(),F=function(t){function r(e){var r=t.call(this)||this;return r.innerException=e,r.name="CallbackExecutionError",r.message="Error trying to execute callback function.",r}return e(r,t),r}(Error),T=function(){function e(){this._value=function(){function e(){return Math.floor(65536*(1+Math.random())).toString(16).substring(1)}return"".concat(e()).concat(e(),"-").concat(e(),"-").concat(e(),"-").concat(e(),"-").concat(e()).concat(e()).concat(e())}()}return Object.defineProperty(e.prototype,"value",{get:function(){return this._value},enumerable:!1,configurable:!0}),e}(),N=function(){},z=function(){},W=function(){function e(){}return Object.defineProperty(e,"SET_DEFAULT_CONFIG",{get:function(){return"SET_DEFAULT_CONFIG"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"SET_CONFIG",{get:function(){return"SET_CONFIG"},enumerable:!1,configurable:!0}),e}(),q=function(){},K=function(){},B=function(){},G=function(){},U=function(){},V=function(){},function(e){e[e.None=0]="None",e[e.Debug=2]="Debug",e[e.Errors=4]="Errors",e[e.Info=8]="Info",e[e.Logs=16]="Logs",e[e.Warnings=32]="Warnings",e[e.All=62]="All"}(X||(X={})),J=function(){function e(){}return Object.defineProperty(e,"SeverityLevel",{get:function(){return"bwtk/services/logger/severityLevel"},enumerable:!1,configurable:!0}),e}(),Y=function(){function e(){}return Object.defineProperty(e.prototype,"id",{get:function(){return this._id},set:function(e){this._id=e},enumerable:!1,configurable:!0}),e}(),H=function(){},Z=function(){function e(){}return Object.defineProperty(e,"Ajax",{get:function(){return"AJAX"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Config",{get:function(){return"CONFIG"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Di",{get:function(){return"DI"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Loader",{get:function(){return"LOADER"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Localization",{get:function(){return"LOCALIZATION"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Logger",{get:function(){return"LOGGER"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Store",{get:function(){return"STORE"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"EventStream",{get:function(){return"EVENT_STREAM"},enumerable:!1,configurable:!0}),e.AsServiceName=function(t){return t===N?e.Ajax:t===z?e.Config:t===q?e.Di:t===K?e.Loader:t===G?e.Localization:t===U?e.Logger:t===Y?e.Store:t===M?e.EventStream:(new T).value},e}(),Q="design:factory",$=function(e,t,r){var n=r.value;"function"==typeof n&&(r.value=n.bind(e),Reflect.defineMetadata(Q,{target:e,propertyKey:t},r.value))},Object.defineProperty($,"MetadataKey",{value:Q}),ee=$,re=function(e){return function(t,r,n){var a=Reflect.getMetadata(te,t)||[];Reflect.defineMetadata(te,o(o([],i(a),!1),[{propertyKey:r,parameterIndex:n,factory:e}],!1),t)}},re.MetadataKey=te="design:injectableFactories",ne=re,(oe=function(e,t){return function(r){Reflect.defineMetadata(ie,j({provides:e},t||{}),r.prototype)}}).MetadataKey=ie="provide:options",ae=oe,se=function(e){return function(e){return"function"==typeof e}(e)?ce(e):ue(e)},ce=function(e){return e},ue=function(e){return function(t){return t.prototype.namespace=e.namespace,t}},fe=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return e(r,t),r}((function(){})),le=function(t){function r(e){var r=t.call(this,e)||this;return r.message=e,r}return e(r,t),r}(Error),de=function(t){function r(e){var r=t.call(this,e)||this;return r.message=e,r}return e(r,t),r}(le),pe=function(t){function r(e){var r=t.call(this,e)||this;return r.message=e,r}return e(r,t),r}(le),he=function(t){function r(e){void 0===e&&(e={});var r=t.call(this,"Redux Error")||this;return Object.keys(e).forEach((function(t){return r[t]=e[t]})),r}return e(r,t),r}(Error),ye=function(){function e(e){this._value=e}return e.create=function(){return(new T).value},Object.defineProperty(e.prototype,"value",{get:function(){return(this._widget?this._widget+":":"")+this._value},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"widget",{get:function(){return this._widget},set:function(e){this._widget=e},enumerable:!1,configurable:!0}),t([ee,n("design:type",Function),n("design:paramtypes",[]),n("design:returntype",void 0)],e,"create",null),t([ae(e),r(0,ne(e.create)),n("design:paramtypes",[String])],e)}(),ge=function(e){this.value=e},ve=function(){function e(e){this.widget=e}return Object.defineProperty(e.prototype,"value",{get:function(){var e=this.widget;return e&&e.prototype&&e.prototype.namespace},enumerable:!1,configurable:!0}),t([ae(e),n("design:paramtypes",[fe])],e)}(),be=function(){function e(){}return Object.defineProperty(e,"RequireContext",{get:function(){return"bwtk/services/widgetLoader/RequireContext"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"RegistryPath",{get:function(){return"bwtk/services/widgetLoader/RegistryPath"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"VersionDelimiter",{get:function(){return"bwtk/services/widgetLoader/VersionDelimiter"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"WidgetBundleName",{get:function(){return"bwtk/services/widgetLoader/WidgetBundleName"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"StaticWidgetMappings",{get:function(){return"bwtk/services/widgetLoader/StaticWidgetMappings"},enumerable:!1,configurable:!0}),e}(),me=function(){function e(){}return Object.defineProperty(e,"DefaultLocale",{get:function(){return"bwtk/services/localization/defaultLocale"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"LocalizationServicesPath",{get:function(){return"bwtk/services/localization/Url"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Brand",{get:function(){return"bwtk/services/localization/brand"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Channel",{get:function(){return"bwtk/services/localization/channel"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"Timeout",{get:function(){return"bwtk/services/localization/timeout"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"DebugDisableNetwork",{get:function(){return"bwtk/services/localization/disableNetwork"},enumerable:!1,configurable:!0}),Object.defineProperty(e,"DebugShowKeys",{get:function(){return"bwtk/services/localization/showKeys"},enumerable:!1,configurable:!0}),e}(),we=c(442),Oe=c(489),Pe=function(t){function r(e){var r=t.call(this,e)||this;return r.destroyed=!1,r.delayInit=!1,r._widget=null,r.loaded=!1,r.delayInitCallback=null,r.widgetRoot=null,r.updateWidgetProps=function(e){var t=["onLoad","onLoadError","widget"],n=Object.keys(e).filter((function(e){return t.indexOf(e)<0})).reduce((function(t,r){var n;return j(j({},t),((n={})[r]=e[r],n))}),{});r.widget&&Object.keys(n).forEach((function(e){return r.widget[e]=n[e]}))},r.state={widget:null},r}return e(r,t),Object.defineProperty(r.prototype,"widget",{get:function(){return this.destroyed?null:this.state.widget||this._widget},enumerable:!1,configurable:!0}),r.prototype.componentDidMount=function(){var e=this.props.onLoadError?this.props.onLoadError:function(){};x.instance.getWidget(this.props.widget,this.onWidgetLoaded.bind(this),e.bind(this))},r.prototype.onWidgetLoaded=function(e){var t,r,n=this;this.destroyed||(this._widget=e,(t=e.store||!1)&&("localizationConfig"in this.props&&"object"==typeof this.props.localizationConfig&&"waitBeforeRender"in this.props.localizationConfig&&this.props.localizationConfig.waitBeforeRender&&(this.delayInit=!0),t.store?t.store.id.widget=this.props.widget:t.id.widget=this.props.widget),this.updateWidgetProps(this.props),this.loaded=!0,r=function(t){void 0===t&&(t=[]),t.forEach((function(e){var t,r=Object.keys(e)[0];"props"===r&&(t=j(j({},n.props),e[r]),n.updateWidgetProps(t))})),e.init(),n.setState({widget:e},(function(){"onLoad"in n.props&&"function"==typeof n.props.onLoad&&n.props.onLoad(n.widget)}))},e.delayInit?e.delayInit(r):this.delayInit?this.delayInitCallback=r:r())},r.prototype.componentWillUnmount=function(){this.destroy()},r.prototype.destroy=function(){var e,t,r,n=this;if(!this.destroyed){if(this.destroyed=!0,this.widgetRoot)try{this.widgetRoot.render(we.createElement("div")),setTimeout((function(){n.widgetRoot&&(n.widgetRoot.unmount(),n.widgetRoot=null)}),0)}catch(i){this.widgetRoot=null}this._widget&&((e=this._widget.store)&&(t=e.store?e.store.id:e.id,(r=e.localization)&&("localization"in r&&(r=r.localization),r.cache&&r.cache.remove(t))),this._widget.destroy()),this.setState({widget:null})}},r.prototype.tryDelayedRender=function(e,t,r){var n=this;void 0===e&&(e=0),void 0===t&&(t=-1),this.loaded?(this.delayInit=!1,this.delayInitCallback&&this.delayInitCallback(),r&&r()):(-1===t||t>0)&&window.setTimeout((function(){return n.tryDelayedRender(e,-1===t?-1:t-1,r)}),e)},r.prototype.render=function(){var e=this;return this.destroyed||!this.widget||this.delayInit?null:we.createElement("div",{className:this.props.widget,ref:function(t){if(t&&!e.destroyed&&(e.widgetRoot||(e.widgetRoot=(0,Oe.H)(t)),e.widget)){e.updateWidgetProps(e.props);try{e.widget.render(e.widgetRoot)}catch(r){}}}})},r}(we.Component),je=function(){},Ee=function(){},Se=c(418),_e=function(){function e(e){this.ajax=e}return e.prototype.init=function(){this._client||(this._client=this.ajax.createClient(this.options))},Object.defineProperty(e.prototype,"client",{get:function(){return this._client||this.init(),this._client},enumerable:!1,configurable:!0}),e.prototype.get=function(e,t,r){var n=this;return this.createObservableRequest((function(){return n.client.get(e,t,r)}))},e.prototype.post=function(e,t,r){var n=this;return this.createObservableRequest((function(){return n.client.post(e,t,r)}))},e.prototype.put=function(e,t,r){var n=this;return this.createObservableRequest((function(){return n.client.put(e,t,r)}))},e.prototype.patch=function(e,t,r){var n=this;return this.createObservableRequest((function(){return n.client.patch(e,t,r)}))},e.prototype.del=function(e,t){var r=this;return this.createObservableRequest((function(){return r.client.del(e,t)}))},e.prototype.createObservableRequest=function(e){return new Se.Observable((function(t){e().then((function(e){t.next(e)})).catch((function(e){t.error(e)})).finally((function(){t.complete()}))}))},t([a,n("design:paramtypes",[N])],e)}(),Ce=function(){function e(e,t,r,n,i){var o=this;this.id=e,this.name=t,this.namespace=r,this.config=n,this.paramsProvider=i,this.id.widget=this.name.value,void 0!==this.defaultValues&&Object.keys(this.defaultValues).forEach((function(e){return o.setDefaultConfig(e,o.defaultValues[e])}))}return e.prototype.getConfig=function(e){return void 0!==this.paramsProvider.props[e]?this.paramsProvider.props[e]:this.config.getConfig(this.configKeyOf(e))},e.prototype.configKeyOf=function(e){return this.config.createKey(this.configKeyFormat(e),this.id.value)},e.prototype.configKeyFormat=function(e){return[this.namespace.value,this.name.value,e].filter((function(e){return e&&e.length>0})).join("/")},e.prototype.setConfig=function(e,t){this.config.setConfig(e,t)},e.prototype.setDefaultConfig=function(e,t){void 0===this.config?this.setDelayedDefaultConfig(e,t):this.config.setDefaultConfig(this.configKeyOf(e),t)},e.prototype.setDelayedDefaultConfig=function(e,t){void 0===this.defaultValues&&(this.defaultValues={}),this.defaultValues[e]=t},t([a,n("design:paramtypes",[ye,ge,ve,z,Ee])],e)}(),Le=function(e,t){return"object"==typeof e&&e instanceof Ce?Ie(e,t||""):Re("object"==typeof(r=e)&&1===Object.keys(r).length&&void 0!==r.defaultValue?e:{defaultValue:e});var r},Re=function(e){return function(t,r){return t.setDefaultConfig(r,e.defaultValue),Ie(t,r)}},Ie=function(e,t){return Object.defineProperty(e,t,{get:function(){return this.getConfig(t)},enumerable:!0,configurable:!0}),e},ke=function(){function e(e,t){this.localization=e,this.config=t,this.disableNetwork=!1,this.disableNetworkGlobal=this.config.getConfig(me.DebugDisableNetwork,!1),this.showKeys=this.config.getConfig(me.DebugShowKeys,!1)}return Object.defineProperty(e.prototype,"defaultMessages",{get:function(){return{}},enumerable:!1,configurable:!0}),e.prototype.createReducer=function(){var e,t,r,n=this;return this.localization.disableNetwork(this.disableNetworkGlobal||this.disableNetwork),this.localization.loadMessagesOnce(),t=(e=this.localization.createReducer())(void 0,{}),r=t.loaded?t:j(j({},t),{messages:this.defaultMessages[t.locale]}),this.localization.loadMessages.bind(this.localization)(!0),function(t,i){var o,a;return void 0===t&&(t=r),a=e(t,i),j(j({},a),{messages:n.showKeys?(o={},o[a.locale]={},o):j(j({},n.defaultMessages[a.locale]),a.messages)})}},t([a,n("design:paramtypes",[B,z])],e)}(),Me=Object.prototype.hasOwnProperty,Ae=function(e){return function(t){return function(t){return function(r){var n=De(r);return n.meta=j(j({},n.meta),{source:e.id.value}),e.notifyActionListener(n),t(r)}}}},De=function(e){var t=j({},e);return Me.call(t,"data")&&!Me.call(t,"payload")&&(t.payload=t.data,delete t.data),Me.call(t,"meta")||(t.meta={}),t},xe=c(750),Fe=function(){function e(e){this.store=e,this.store.createStore=this.createStore.bind(this),this.epicMiddleware=(0,b.createEpicMiddleware)()}return e.prototype[Symbol.observable]=function(){throw new Error("Method not implemented.")},e.prototype.init=function(){},e.prototype.destroy=function(){},e.prototype.createStore=function(){var e,t,r=xe.compose,n=this.epicMiddleware;return"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__&&(r=window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({name:this.id.value})),e=(0,xe.legacy_createStore)(this.reducer,r(xe.applyMiddleware.apply(void 0,o(o([],i(this.additionalMiddlewares),!1),[n,Ae(this.store)],!1)))),"function"==typeof(t=this.middlewares)&&this.epicMiddleware.run(t),e},Object.defineProperty(e.prototype,"id",{get:function(){return this.store.id},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"listenToAll",{get:function(){return this.store.listenToAll},set:function(e){this.store.listenToAll=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"middlewares",{get:function(){return function(e,t){return e.pipe()}},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"additionalMiddlewares",{get:function(){return[]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"getState",{get:function(){return this.store.getState.bind(this.store)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dispatch",{get:function(){return this.store.dispatch.bind(this.store)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"subscribe",{get:function(){return this.store.subscribe.bind(this.store)},enumerable:!1,configurable:!0}),e.prototype.notifyActionListener=function(e){},e.prototype.replaceReducer=function(){throw new Error("Use 'reducer' property instead!")},t([a,n("design:paramtypes",[H])],e)}(),Te=function(){function e(e){this.stream=e,this.subscriptions=[]}return e.prototype.subscribe=function(e,t){var r=this.stream.subscribe(e,t);return this.subscriptions.push(r),r},e.prototype.subscribeAll=function(e){var t=this.stream.subscribeAll(e);return this.subscriptions.push(t),t},e.prototype.subscribeError=function(e){var t=this.stream.subscribeError(e);return this.subscriptions.push(t),t},e.prototype.send=function(e,t){this.stream.send(e,t)},e.prototype.sendError=function(e,t,r){this.stream.sendError(e,t,r)},e.prototype.unsubscribe=function(){this.subscriptions.forEach((function(e){return e()})),this.subscriptions=[]},Object.defineProperty(e.prototype,"errors",{get:function(){var e=this;return{subscribe:function(t){return e.subscribeError(t)},send:function(t,r){return e.sendError(t,r)}}},enumerable:!1,configurable:!0}),t([a,n("design:paramtypes",[M])],e)}(),Ne={BaseClient:_e,BaseConfig:Ce,configProperty:Le,BaseLocalization:ke,BaseStore:Fe,BasePipe:Te,actionsToComputedPropertyName:function(e){return Object.keys(e).reduce((function(t,r){var n;return j(j({},t),((n={})[r]=e[r].toString(),n))}),{})}},ze=function(t){function r(e){var n=t.call(this,e)||this;return n.state={hasError:!1},"name"in e&&(r.displayName=e.name),n}return e(r,t),r.prototype.componentDidCatch=function(e,t){this.setState({hasError:!0});var r=x.instance.getService(Z.EventStream);e.componentStack=t.componentStack,r.sendError(D.REACT_BOUNDARY,e)},r.prototype.render=function(){if(this.state.hasError){if(!1===this.props.displayErrorMessage)return null;var e=this.props.errorMessage?this.props.errorMessage:"{component failed}";return we.createElement("div",null,e)}return this.props.children},r.displayName="ErrorBoundary",r.contextTypes={store:Object},r}(we.Component),We=c(873),qe=function(){function e(e){this._metadata=Reflect.getMetadata(ae.MetadataKey,e.prototype)}return Object.defineProperty(e.prototype,"provides",{get:function(){return this.metadata.provides},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"withFactory",{get:function(){return this.metadata.withFactory},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metadata",{get:function(){if(void 0===this._metadata)throw new Ke;return this._metadata},enumerable:!1,configurable:!0}),e}(),Ke=function(t){function r(){return t.call(this,r.Message)||this}return e(r,t),r.Message="Can't find Provide decorator in type.",r}(Error),Be=function(){function e(e){this._metadata=Reflect.getMetadata(ee.MetadataKey,e)}return Object.defineProperty(e.prototype,"target",{get:function(){return this.metadata.target},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"propertyKey",{get:function(){return this.metadata.propertyKey},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metadata",{get:function(){if(void 0===this._metadata)throw new Ge;return this._metadata},enumerable:!1,configurable:!0}),e.isFactory=function(t){try{return void 0!==e.readFactoryMetadata(t).target}catch(r){return!1}},e.readFactoryMetadata=function(t){return new e(t)},e}(),Ge=function(t){function r(){return t.call(this,r.Message)||this}return e(r,t),r.Message="Can't find factory decorator in type.",r}(Error),Ue=function(){function e(e){this._metadata=Reflect.getMetadata(ne.MetadataKey,e)}return Object.defineProperty(e.prototype,"metadata",{get:function(){if(void 0===this._metadata)throw new Ve;return this._metadata},enumerable:!1,configurable:!0}),e}(),Ve=function(t){function r(){return t.call(this,r.Message)||this}return e(r,t),r.Message="Can't find injectWithFactory decorator in type.",r}(Error),Xe=function(){function e(e){this.reader=Be.isFactory(e)?new Ze(e):new Ye(e)}return Object.defineProperty(e.prototype,"dependencies",{get:function(){return this.reader.dependencies},enumerable:!1,configurable:!0}),e}(),Je=function(){function e(){}return Object.defineProperty(e.prototype,"dependencies",{get:function(){var e=this;return this.metadata.map((function(t,r){var n=e.findInjectWithFactoryMetadataAtIndex(r);return void 0!==n?n.factory:t}))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"customFactories",{get:function(){return this._customFactories||(this._customFactories=this.readCustomFactories())},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metadata",{get:function(){return this._metadata||(this._metadata=this.readMetadata())},enumerable:!1,configurable:!0}),e}(),Ye=function(t){function r(e){var r=t.call(this)||this;return r.ctor=e,r}return e(r,t),r.prototype.readMetadata=function(){var e=Reflect.getMetadata("design:paramtypes",this.ctor);if(void 0===e)throw new He;return e},r.prototype.findInjectWithFactoryMetadataAtIndex=function(e){return this.customFactories.find((function(t){return t.parameterIndex===e}))},r.prototype.readCustomFactories=function(){var e=new Ue(this.ctor);try{return e.metadata}catch(t){return[]}},r}(Je),He=function(t){function r(){return t.call(this,r.Message)||this}return e(r,t),r.Message="Can't find metadata information.\nPossible cause: missing class decorators",r}(Error),Ze=function(t){function r(e){var r=t.call(this)||this;return r.ctor=e,r}return e(r,t),r.prototype.readMetadata=function(){return Reflect.getMetadata("design:paramtypes",this.factoryMetadata.target,this.factoryMetadata.propertyKey)},r.prototype.findInjectWithFactoryMetadataAtIndex=function(e){var t=this;return this.customFactories.find((function(r){return r.propertyKey===t.factoryMetadata.propertyKey&&r.parameterIndex===e}))},Object.defineProperty(r.prototype,"factoryMetadata",{get:function(){return this._factoryMetadata||(this._factoryMetadata=new Be(this.ctor))},enumerable:!1,configurable:!0}),r.prototype.readCustomFactories=function(){var e=new Ue(this.factoryMetadata.target);try{return e.metadata}catch(t){return[]}},r}(Je),Qe=function(){function e(e,t){this.ctor=e,this._dependencies=t;var r=new We.Inject;r.tokens=this.dependencies,r.tokens.length>0&&(0,We.annotate)(this.factory,r),(0,We.annotate)(this.factory,new We.FactoryProvider)}return Object.defineProperty(e.prototype,"factory",{get:function(){return this._factory||(this._factory=Be.isFactory(this.ctor)?e.createFactoryFunctionFromFactory(this.ctor):void 0!==this.readFactoryFromProvideMetadata()?e.createFactoryFunctionFromFactory(this.readFactoryFromProvideMetadata()):e.createFactoryFunctionFromConstructor(this.ctor))},enumerable:!1,configurable:!0}),e.prototype.readFactoryFromProvideMetadata=function(){try{return new qe(this.ctor).withFactory}catch(e){return}},Object.defineProperty(e.prototype,"dependencies",{get:function(){return this._dependencies||(this._dependencies=this.readDependenciesFromMetadata())},enumerable:!1,configurable:!0}),e.prototype.readDependenciesFromMetadata=function(){try{return this.dependenciesMetadataReader.dependencies}catch(e){return[]}},Object.defineProperty(e.prototype,"dependenciesMetadataReader",{get:function(){return new Xe(this.readFactoryFromProvideMetadata()||this.ctor)},enumerable:!1,configurable:!0}),e.createFactoryFunctionFromConstructor=function(t){return e.createFactoryFunction(t,(function(e){return new e}))},e.createFactoryFunctionFromFactory=function(t){return e.createFactoryFunction(t,(function(e){return e()}))},e.createFactoryFunction=function(e,t){return function(){var r,n=Array.prototype.slice.call(arguments),a=Function.prototype.bind.apply(e,o([null],i(n),!1)),s=t(a);return"object"!=typeof s||Array.isArray(s)||((r=new $e(s,n)).overrideInit(),r.overrideDestroy()),s}},e}(),$e=function(){function e(e,t){void 0===t&&(t=[]),this.instance=e,this.dependencies=t}return e.prototype.overrideInit=function(){this.overrideInstaceMethod("init")},e.prototype.overrideDestroy=function(){this.overrideInstaceMethod("destroy")},e.prototype.overrideInstaceMethod=function(e){var t,r,n,i=this.instance[e]||new Function,o=(t=this.dependencies,function(e){t.forEach((function(t){var r=t[e];if(void 0!==r)try{r()}catch(n){}}))});"function"==typeof i?this.instance[e]=(r=i.bind(this.instance),n=!1,function(){n||(n=!0,o(e),r())}):o(e)},e}(),et=function(){function e(e){this.ctor=e,this.defaultFactory=new Qe(this.ctor)}return Object.defineProperty(e.prototype,"providers",{get:function(){return o(o([e.provide(this.ctor,this.defaultFactory.factory)],i(this.tryReadFromProvideMetadata()),!1),i(this.dependenciesProviders),!1)},enumerable:!1,configurable:!0}),e.prototype.tryReadFromProvideMetadata=function(){try{var t=this.provideMetadataReader.provides;return[e.provide(t,new Qe(this.ctor).factory,!0),e.provide(Z.AsServiceName(t),new Qe((function(e){return e}),[t]).factory,!0)]}catch(r){return[]}},Object.defineProperty(e.prototype,"provideMetadataReader",{get:function(){return new qe(this.ctor)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"dependenciesProviders",{get:function(){var t=this.defaultFactory.dependencies.map((function(t){return new e(t).providers}));return Array.prototype.concat.apply([],t)},enumerable:!1,configurable:!0}),e.provide=function(e,t,r){var n,i;return void 0===r&&(r=!1),n=t,(i=new We.Provide(e)).hasPriority=r,(0,We.annotate)(n,i),n},e}(),tt=function(){function e(){this.providers=new Map}return e.prototype.appendProviders=function(e){var t=this;return e.map((function(e){return new rt(e)})).forEach((function(e){t.providers.has(e.provides)&&!e.hasPriority||t.providers.set(e.provides,e.provider)})),this},e.prototype.removeLowPriorityProvidersFrom=function(e){var t=this;return e.map((function(e){return new rt(e)})).forEach((function(e){t.providers.has(e.provides)&&(new rt(t.providers.get(e.provides)).hasPriority||t.providers.delete(e.provides))})),this},e.prototype.toArray=function(){var e=[];return this.providers.forEach((function(t){return e.push(t)})),e},e}(),rt=function(){function e(e){this.provider=e,this.provideAnnotation=this.provider.annotations.find((function(e){return void 0!==e.token}))}return Object.defineProperty(e.prototype,"provides",{get:function(){return this.provideAnnotation.token},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"hasPriority",{get:function(){return Boolean(this.provideAnnotation.hasPriority)},enumerable:!1,configurable:!0}),e}(),nt=function(){function e(e){this.defaultOptions={method:"GET",headers:{},body:"",mode:"cors",credentials:"omit",cache:"default",redirect:"follow",referrer:"about:client",referrerPolicy:"",integrity:""},this.defaultCustomOptions={url:"",dataType:"json"},this.options=j({},this.defaultOptions),this.customOptions=j({},this.defaultCustomOptions);var t=this.processOptions(e),r=t.initOptions,n=t.customOptions;this.options=r,this.customOptions=n,this.options.body=""}return e.prototype.processOptions=function(e,t){var r,n,i,o=j({},this.options);return Object.keys(o).forEach((function(t){e[t]&&(o[t]=e[t])})),"boolean"==typeof e.cache&&(o.cache=e.cache?"default":"reload"),"reload"===o.cache&&(o.headers=j(j({},o.headers),{"Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache",Expires:0})),"boolean"==typeof e.credentials&&(o.credentials=e.credentials?"include":"omit"),void 0===e.body&&delete o.body,r=j({},this.customOptions),Object.keys(r).forEach((function(t){e[t]&&(r[t]=e[t])})),O.window&&O.window.CsrfToken&&O.window.CsrfToken.length>0&&(n=t||"",(i=r.url?r.url:"").length>0&&(n=i.concat(n)),0===n.toLowerCase().indexOf("https://api")&&(o.headers=j(j({},o.headers),{"X-CSRF-TOKEN":O.window.CsrfToken}))),{initOptions:o,customOptions:r}},e.prototype.mergeOptions=function(){var e,t,r,n=[];for(e=0;e<arguments.length;e++)n[e]=arguments[e];return r=(t=o([this.options],i(n),!1)).map((function(e){return e.headers})).reduce((function(e,t){return j(j({},e),t)}),{}),j(j({},t.reduce((function(e,t){return j(j({},e),t)}),{})),{headers:r})},e.prototype.buildRequestConfig=function(e,t){return this.processOptions(e,t)},e.prototype.buildRequestPath=function(e,t){return t.url?t.url.concat(e):e},e.prototype.buildQueryParameters=function(e,t,r){var n,i;return void 0!==r&&r instanceof Object?(n=e+"/"+t,i=Object.keys(r).map((function(e){return encodeURIComponent(e)+"="+encodeURIComponent(r[e])})).join("&"),n.indexOf("?")>-1?n.indexOf("?")===n.length-1?i:"&"+i:"?"+i):""},e.prototype.getOptions=function(){return j(j({},this.options),this.customOptions)},e.prototype.setOptions=function(e){var t=this.mergeOptions(e),r=this.processOptions(t),n=r.initOptions,i=r.customOptions;this.options=n,this.customOptions=i},e}(),it=function(){function e(e){this.stream=e}return e.prototype.get=function(e,t,r){return this.createClient(r).get(e,t)},e.prototype.post=function(e,t,r){return this.createClient(r).post(e,t)},e.prototype.head=function(e,t){return this.createClient(t).head(e)},e.prototype.put=function(e,t,r){return this.createClient(r).put(e,t)},e.prototype.patch=function(e,t,r){return this.createClient(r).patch(e,t)},e.prototype.del=function(e,t){return this.createClient(t).del(e)},e.prototype.createClient=function(e){return void 0===e&&(e={}),new ot(e,this.stream)},t([ae(N),n("design:paramtypes",[M])],e)}(),ot=function(){function e(e,t){var r=this;this.stream=t,this.buildRequest=function(e,t){var n=r.config.buildRequestConfig(t,e),i=n.initOptions,o=n.customOptions;return new Promise((function(n,a){var s,c=!1;t.timeout&&t.timeout>0&&(s=setTimeout((function(){c=!0;var e="Request timed out after "+Math.floor(t.timeout/1e3)+"s";r.stream.sendError(D.AJAX,e),a({error:e})}),t.timeout)),fetch(r.config.buildRequestPath(e,o),i).then((function(e){clearTimeout(s),c||(e.ok?r.processResponse(e,{resolve:n,reject:a},"resolve",o):(r.stream.sendError(D.AJAX,e),r.processResponse(e,{resolve:n,reject:a},"reject",o)))}),(function(e){c||a({error:e})}))}))},this.config=new nt(e),this.stream||(this.stream={sendError:function(){}})}return e.prototype.get=function(e,t,r){void 0===r&&(r={});var n=this.config.mergeOptions(r,{method:"GET",body:void 0});return this.buildRequest(e+this.config.buildQueryParameters(n.url||"",e,t),n)},e.prototype.post=function(e,t,r){void 0===r&&(r={});var n=this.config.mergeOptions(r,{method:"POST"});return n.body=this.prepBodyData(n,t),this.buildRequest(e,n)},e.prototype.head=function(e,t){void 0===t&&(t={});var r=this.config.mergeOptions(t,{method:"HEAD",body:void 0});return this.buildRequest(e,r)},e.prototype.put=function(e,t,r){void 0===r&&(r={});var n=this.config.mergeOptions(r,{method:"PUT"});return n.body=this.prepBodyData(n,t),this.buildRequest(e,n)},e.prototype.patch=function(e,t,r){void 0===r&&(r={});var n=this.config.mergeOptions(r,{method:"PATCH"});return n.body=this.prepBodyData(n,t),this.buildRequest(e,n)},e.prototype.del=function(e,t){void 0===t&&(t={});var r=this.config.mergeOptions(t,{method:"DELETE",body:void 0});return this.buildRequest(e,r)},e.prototype.toAjaxResponse=function(e){var t=e,r=t.headers,n=t.redirected,i=t.status,o=t.statusText,a=t.type,s=t.url,c={headers:Object.keys(r).reduce((function(e,t){var n;return j(j({},e),((n={})[t]=r[t],n))}),{}),redirected:n,status:i,statusText:o,type:a,url:s,dataType:"",data:void 0};return c},e.prototype.getResponseType=function(e,t,r){void 0===e&&(e={}),void 0===t&&(t={}),void 0===r&&(r=!1);var n="";switch(n=(n=!r&&t.dataType?t.dataType:e["Content-Type"]||e["content-type"]||"").toLowerCase()){case"application/json":case"json":n="json";break;case"text/plain":case"text":default:n="text";break;case"blob":n="blob";break;case"binary":n="binary";break;case"stream":n="stream"}return n},e.prototype.processResponse=function(e,t,r,n){var i=this.toAjaxResponse(e),o=t[r],a=this.getResponseType(i.headers,n,"reject"===r);if(1223!==e.status)if("reject"!==r)if(i.dataType=a,204!==e.status)switch(a){case"stream":o(j(j({},i),{data:e.body}));break;case"json":e.json().then((function(e){return o(j(j({},i),{data:e}))}),(function(r){e.text().then((function(e){return t.reject(j(j({},i),{dataType:"text",data:e}))}),(function(e){return t.reject(j(j({},i),{data:e}))}))}));break;case"blob":e.blob().then((function(e){return o(j(j({},i),{data:e}))}),(function(e){return t.reject(j(j({},i),{dataType:"error",data:e}))}));break;case"binary":e.arrayBuffer().then((function(e){return o(j(j({},i),{data:e}))}),(function(e){return t.reject(j(j({},i),{dataType:"error",data:e}))}));break;default:e.text().then((function(e){return o(j(j({},i),{data:e}))}),(function(e){return t.reject(j(j({},i),{dataType:"error",data:e}))}))}else o(j(j({},i),{data:""}));else e.text().then((function(e){return o(j(j({},i),{dataType:"error",data:e}))}),(function(e){return o(j(j({},i),{dataType:"error",data:e}))}));else t.resolve(j(j({},i),{data:"",status:204,statusText:"No Content",dataType:"text"}))},e.prototype.prepBodyData=function(e,t){var r=e.headers||{};return"application/json"===Object.keys(r).reduce((function(e,t){return/^content-type$/i.test(t)?r[t]:e}),"")?JSON.stringify(t):t},e.prototype.setOptions=function(e){this.config.setOptions(e)},e.prototype.getOptions=function(){return this.config.getOptions()},e}(),at=function(){function e(e){this.emitter=new S.EventEmitter,this.store=e.createStore(this.id)}return Object.defineProperty(e.prototype,"id",{get:function(){return"DefaultConfigServices"},enumerable:!1,configurable:!0}),e.prototype.init=function(){var e=this;this.store.init(),this.store.replaceReducer((function(t,r){var n,i;switch(void 0===t&&(t={configs:{},defaultConfigs:{}}),r.type){case W.SET_CONFIG:return e.validateConfig(r.payload),j(j({},t),{configs:j(j({},t.configs),e.emitChangesAsync(t.configs,(n={},n[r.payload.key]=r.payload.value,n)))});case W.SET_DEFAULT_CONFIG:return e.validateConfig(r.payload),j(j({},t),{defaultConfigs:j(j({},t.defaultConfigs),(i={},i[r.payload.key]=r.payload.value,i))});default:return t}}))},e.prototype.validateConfig=function(e){if(e.key===J.SeverityLevel&&!(e.value in X))throw new Error("Invalid log level.")},e.prototype.splitKey=function(e){var t=e.split("@");return{fullKey:e,key:t[0],target:t[1]}},e.prototype.emitChangesAsync=function(e,t){var r=this;return setTimeout((function(){r.emitChanges(e,t)}),0),t},e.prototype.emitChanges=function(e,t){for(var r in t)t.hasOwnProperty(r)&&this.emitter.emit(r,{oldValue:e[r],newValue:t[r]});return t},e.prototype.createKey=function(e,t){return t?e.concat("@",t):e},e.prototype.setDefaultConfig=function(e,t){this.dispatch(e,t,W.SET_DEFAULT_CONFIG)},e.prototype.setConfig=function(e,t){this.dispatch(e,t,W.SET_CONFIG)},e.prototype.dispatch=function(e,t,r){this.store.dispatch({type:r,payload:{key:e,value:t}})},e.prototype.getConfig=function(e,t){var r,n;return void 0===t&&(t=void 0),r=this.splitKey(e),void 0===(n=this.getConfigState()[r.fullKey]||this.getConfigState()[r.key]||this.getDefaultConfigState()[r.fullKey]||this.getDefaultConfigState()[r.key])?t:n},e.prototype.getConfigState=function(){return this.store.getState().configs},e.prototype.getDefaultConfigState=function(){return this.store.getState().defaultConfigs},e.prototype.onChange=function(e,t){var r=this;return this.emitter.on(e,(function(e){return t(e.oldValue,e.newValue)})),{dispose:function(){r.emitter.removeListener(e,t)}}},t([ae(z),n("design:paramtypes",[Y])],e)}(),st=function(){function e(){}return e.initLocaleData=function(e){e in this.cache?this.cache[e]=j(j({},this.cache[e]),{state:ct.PENGING}):this.cache[e]={locale:{},state:ct.PENGING,usedBy:[]}},e.setLocaleData=function(e,t){var r=j(j({},this.cache[e].locale),t);this.cache[e]=j(j({},this.cache[e]),{locale:r,state:ct.READY})},e.tryUseLocaleData=function(e){var t=this.getLocaleData(e);t&&t.state===ct.READY&&this.useLocaleData(e)},e.useLocaleData=function(e){var t=this.normalizeId(e),r=this.getLocaleData(e);r&&(r.usedBy=ut(o(o([],i(r.usedBy),!1),[t.id],!1)),this.cache[t.widget]=r,this.runOnReadyCallbacks(t.widget))},e.addCallback=function(e,t){var r,n,i,o=this.normalizeId(e);this.callbackExists(e)||(n=o.widget,(r={})[o.id]=t,i=r,n in this.callbacks?this.callbacks[n].push(i):this.callbacks[n]=[i])},e.removeCallback=function(e){var t=this.normalizeId(e),r=t.widget;r in this.callbacks&&(this.callbacks[r]=this.callbacks[r].filter((function(e){return Object.keys(e)[0]!==t.id})))},e.runOnReadyCallbacks=function(e){var t,r,n=this;e in this.callbacks&&(t=[],this.callbacks[e].forEach((function(e){e[Object.keys(e)[0]]()||t.push(e[Object.keys(e)[0]])})),0===t.length?delete this.callbacks[e]:this.callbacks[e]=t,r=Object.keys(this.mappings).reduce((function(t,r){return n.mappings[r].indexOf(e)>-1?o(o([],i(t),!1),[r],!1):[]}),[]),r.forEach((function(e){return n.runOnReadyCallbacks(e)})))},e.callbackExists=function(e){var t,r,n,i=this.normalizeId(e).id,o=Object.keys(this.callbacks);for(t=0;t<o.length;t++)for(r=this.callbacks[o[t]],n=0;n<r.length;n++)if(Object.keys(r[n])[0]===i)return!0;return!1},e.getLocaleData=function(e){var t=this,r=this.normalizeId(e);return ut(o([r.widget],i(this.getMappings(r.widget)),!1)).reduce((function(e,r){return null!==e&&e.state===ct.READY||!(r in t.cache)?e:t.cache[r]}),null)},e.isLocaleDataReady=function(e,t){var r=this.getLocaleData(e);return!!r&&t in r.locale},e.isFinished=function(e,t){var r=this.getLocaleData(e);return null!==r&&r.state===ct.READY&&t in r.locale&&!this.callbackExists(e)},e.isPending=function(e){var t=this.getLocaleData(e);return null!==t&&t.state===ct.PENGING},e.setPermanentlyFailed=function(e){var t=this,r=this.normalizeId(e);ut(o([r.widget],i(this.getMappings(r.widget)),!1)).forEach((function(e){e in t.cache&&(t.cache[e]=j(j({},t.cache[e]),{locale:{},state:ct.FAILED}))}))},e.loadingFailed=function(e){var t=this,r=this.normalizeId(e);return ut(o([r.widget],i(this.getMappings(r.widget)),!1)).reduce((function(e,r){return r in t.cache&&t.cache[r].state===ct.FAILED||e}),!1)},e.remove=function(e,t){var r,n,i=this;void 0===t&&(t=!1),r=this.normalizeId(e),""!==(n=r.id)&&(Object.keys(this.cache).forEach((function(e){i.cache[e].usedBy=i.cache[e].usedBy.filter((function(e){return n!==e}))})),t&&this.removeCacheData(r))},e.removeCacheData=function(e){var t=this,r=Object.keys(this.cache),n=r.filter((function(e){return t.cache[e].usedBy.length>0}));r.filter((function(e){return n.indexOf(e)<0})).forEach((function(e){return delete t.cache[e]}))},e.addMapping=function(e){var t=this;Object.keys(e).forEach((function(r){var n=Array.isArray(e[r])?e[r]:[e[r]];r in t.mappings?t.mappings[r]=ut(o(o([],i(t.mappings[r]),!1),i(n),!1)):t.mappings[r]=n}))},e.getMappings=function(e){var t=this.normalizeId(e);return t.widget in this.mappings?this.mappings[t.widget]:[]},e.normalizeId=function(e){return"string"==typeof e?{id:"",widget:e}:{id:e.value,widget:e.widget}},e.isMapped=function(e){return this.normalizeId(e).widget in this.mappings},e.cache={},e.callbacks={},e.mappings={},e}(),function(e){e[e.PENGING=1]="PENGING",e[e.READY=2]="READY",e[e.FAILED=3]="FAILED"}(ct||(ct={})),ut=function(e){return e.filter((function(e,t,r){return r.indexOf(e)===t}))},ft=function(r){function i(e,t,n,i,o,a){var s=r.call(this)||this;return s.widgetStore=n,s.localizationServices=i,s.logger=o,s.stream=a,s.skipLoadMessages=!1,s.networkDisabled=!1,s.fullPath=t?"".concat(t,"/").concat(e):e,s.widgetId=s.widgetStore.id,s}return e(i,r),i.create=function(e,t,r,n){return e.createChildLocalization(r.value,t,n&&n.value)},Object.defineProperty(i.prototype,"messagesDictionary",{get:function(){var e=this.localizationServices.cache.getLocaleData(this.widgetId);return e&&e.state===ct.READY?e.locale:{en:{},fr:{}}},enumerable:!1,configurable:!0}),i.prototype.dispatchLocaleChanged=function(){var e=this.getState().locale;this.localizationServices.cache.isLocaleDataReady(this.widgetId,e)?this.widgetStore.dispatch(R()):this.loadMessages(!0,!0)},i.prototype.dispatchMessagesLoaded=function(e){var t,r=this;this.widgetStore.initialized?(t=e?k(e):k(),this.widgetStore.dispatch(t)):setTimeout((function(){return r.dispatchMessagesLoaded(e)}))},i.prototype.createReducer=function(){var e=this;return this.loadMessages(),function(t,r){switch(void 0===t&&(t=e.getState()),r.type){case k.toString():case R.toString():return e.getState();case I.toString():return e.loadMessages(),t;default:return t}}},i.prototype.disableNetwork=function(e){void 0===e&&(e=!0),this.networkDisabled=e},i.prototype.loadMessagesOnce=function(){this.skipLoadMessages=!0},i.prototype.loadMessages=function(e,t){var r,n,i,o=this;void 0===e&&(e=!1),void 0===t&&(t=!1),this.networkDisabled||!e&&this.skipLoadMessages||(r=this.getState().locale,(n=this.localizationServices.cache).loadingFailed(this.widgetId)||(i=function(){if(t&&o.widgetStore.dispatch(R()),n.loadingFailed(o.widgetId)){var e={message:"failed to pre-load locale data",id:o.widgetId.value};o.dispatchMessagesLoaded(new he({error:e,widget:o.widgetId.widget})),o.logger.error(e),o.stream.sendError(D.LOCALIZATION,e)}else o.dispatchMessagesLoaded();return!0},n.isFinished(this.widgetId,r)?i():(n.addCallback(this.widgetId,i),n.isMapped(this.widgetId)||(n.isPending(this.widgetId)?n.tryUseLocaleData(this.widgetId):(n.initLocaleData(this.widgetId.widget),this.localizationServices.loadMessages(this.fullPath).then((function(e){var t=e.data;n.setLocaleData(o.widgetId.widget,t),n.useLocaleData(o.widgetId)})).catch((function(e){var t={message:"failed to load locale data",id:o.widgetId.value,response:e};n.setPermanentlyFailed(o.widgetId),n.removeCallback(o.widgetId),o.dispatchMessagesLoaded(new he(t)),o.logger.error(t),o.stream.sendError(D.LOCALIZATION,t)})))))))},i.prototype.getState=function(){var e=this.localizationServices.locale,t=this.localizationServices.fullLocale,r=this.messagesDictionary[e]||{};return{locale:e,fullLocale:t,messages:r,formats:this.localizationServices.formats[e],loaded:Object.keys(r).length>0}},i.prototype.destroy=function(){this.localizationServices.cache.remove(this.widgetId),this.localizationServices.unsubscribe(this)},t([ee,n("design:type",Function),n("design:paramtypes",[G,H,ge,ve]),n("design:returntype",void 0)],i,"create",null),t([ae(B,{withFactory:i.create}),n("design:paramtypes",[String,String,H,pt,V,M])],i)}(B),lt=new Map,dt=function(){function e(){}return e.load=function(e,t){var r,n,i=this;void 0===t&&(t=function(){}),r=x.instance.getService(Z.Localization),n=[],Object.keys(e).forEach((function(t){var o=e[t];n.push(new Promise((function(e){r.loadMessages(o).then((function(r){var n,a;r.data?(st.setLocaleData(t,r.data),st.runOnReadyCallbacks(t),t in i.widgets&&(i.widgets[t].forEach((function(e){return e.tryDelayedRender()})),delete i.widgets[t]),e(((n={})[t]=r.data,n))):e(j(((a={})["error"+t]={widget:t,path:o},a),r.error))}),(function(r){var n;e(j(((n={})["error"+t]={widget:t,path:o},n),r.error))}))})))})),Promise.all(n).then((function(e){var r=[];t(e.reduce((function(e,t){var n=Object.keys(t)[0];return n.startsWith("error")?r.push(t[n]):e[n]=t[n],e}),{}),r)}))},e.localize=function(e){var t=this,r=e.widgets,n=e.messages;return Object.keys(n).forEach((function(e){st.initLocaleData(e)})),r.forEach((function(e){var r,n=i(o([],i(e),!1),3),a=n[0],c=n[1],u=n[2],f=void 0===u?{}:u;f.localizationConfig=j(j({},f.localizationConfig||{}),{waitBeforeRender:!0}),r=s(a,c,f),a in t.widgets?t.widgets[a]=o(o([],i(t.widgets[a]),!1),[r],!1):t.widgets[a]=[r]})),new Promise((function(e,r){t.load(n,(function(n,i){var o;i.length?(i.forEach((function(e){var t=e.widget;return st.setPermanentlyFailed(t)})),o=function(){return r(i)}):o=function(){return e(n)},Object.keys(t.widgets).forEach((function(e){return t.widgets[e].forEach((function(t){t.tryDelayedRender(100,10,(function(){return st.runOnReadyCallbacks(e)}))}))})),t.widgets={},o()}))}))},e.widgets={},e}();const qt=JSON.parse('{"en":{"date":{"short":{"month":"long","day":"numeric","hour":"numeric","minute":"2-digit","hour12":true,"formatMatcher":"basic"},"long":{"weekday":"long","year":"numeric","month":"long","day":"numeric","formatMatcher":"basic"},"yMMMd":{"month":"short","day":"numeric","year":"numeric","formatMatcher":"basic"},"yMMMMd":{"month":"long","day":"numeric","year":"numeric","formatMatcher":"basic"}},"time":{"hours":{"hour":"numeric","hour12":true},"short":{"hour":"numeric","minute":"2-digit","hour12":true}},"number":{"CAD":{"style":"currency","currency":"CAD","currencyDisplay":"symbol"}}},"fr":{"date":{"short":{"month":"long","day":"numeric","hour":"2-digit","minute":"2-digit","formatMatcher":"basic"},"long":{"weekday":"long","year":"numeric","month":"long","day":"numeric","formatMatcher":"basic"},"yMMMd":{"month":"short","day":"numeric","year":"numeric","formatMatcher":"basic"},"yMMMMd":{"month":"long","day":"numeric","year":"numeric","formatMatcher":"basic"}},"time":{"hours":{"hour":"2-digit","formatMatcher":"basic"},"short":{"hour":"2-digit","minute":"2-digit","formatMatcher":"basic"}},"number":{"CAD":{"style":"currency","currency":"CAD","currencyDisplay":"symbol"}}}}');pt=function(){function e(e,t,r,n,i){this.ajax=e,this.config=t,this.loggerServices=n,this.stream=i,this.localizedWidgets=[],this.disableNetwork=!1,this.brand="",this.channel=null,this.baseUrl="",this.timeout=-1,this.store=r.createStore(this.id)}return Object.defineProperty(e.prototype,"id",{get:function(){return"DefaultLocalizationServices"},enumerable:!1,configurable:!0}),e.prototype.init=function(){var e,t=this;this.disableNetwork=this.config.getConfig(me.DebugDisableNetwork,!1),this.brand=this.config.getConfig(me.Brand,""),this.timeout=this.config.getConfig(me.Timeout,-1),this.channel=this.config.getConfig(me.Channel,null),this.store.init(),this.disableNetwork||(this.baseUrl=this.config.getConfig(me.LocalizationServicesPath,""),this.client=this.ajax.createClient({cache:"default"})),this.store.listenToAll=!0,e=this.config.getConfig(me.DefaultLocale)||"en",this.store.replaceReducer((function(t,r){return void 0===t&&(t={locale:e}),r.type===L.toString()?e=j(j({},t),{locale:r.payload}):t})),this.store.subscribe((function(){var r=t.store.getState();r.locale!==e&&(e=r.locale,t.localizedWidgets.forEach((function(e){return e.dispatchLocaleChanged()})))}))},Object.defineProperty(e.prototype,"locale",{get:function(){return this.store.getState().locale},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"fullLocale",{get:function(){return this.locale+"-CA"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"formats",{get:function(){return qt},enumerable:!1,configurable:!0}),e.prototype.createChildLocalization=function(e,t,r){var n=new ft(e,r,t,this,this.loggerServices.createLogger(Z.Localization,"@widget=".concat(e),"@namespace=".concat(r)),this.stream);return this.localizedWidgets=o(o([],i(this.localizedWidgets),!1),[n],!1),n},e.prototype.unsubscribe=function(e){this.localizedWidgets=this.localizedWidgets.filter((function(t){return t!==e}))},e.prototype.loadMessages=function(e){var t,r,n,i;return this.disableNetwork?Promise.resolve({data:{en:{},fr:{}}}):(t={},r=this.baseUrl,n="",/\{WIDGET\}/.test(r)?r=r.replace(/\{WIDGET\}/g,e):n=(r.endsWith("/")?"":"/")+e,/\{BRAND\}/.test(r)?r=r.replace(/\{BRAND\}/g,this.brand):""!==this.brand&&(t=j(j({},t),{brand:this.brand})),i={url:r,headers:{"Accept-Language":this.locale,Channel:this.channel}},this.timeout>0&&(i.timeout=this.timeout),this.client.get(n,Object.keys(t).length>0?t:null,i))},e.prototype.setLocale=function(e){this.store.dispatch(L(e))},Object.defineProperty(e.prototype,"cache",{get:function(){return st},enumerable:!1,configurable:!0}),e.prototype.getLocalizedString=function(e,t,r){void 0===r&&(r=this.store.getState().locale);var n=this.cache.getLocaleData(e);return n&&n.state===ct.READY&&r in n.locale&&t in n.locale[r]?n.locale[r][t]:""},e.prototype.addLocaleDataMapping=function(e){st.addMapping(e)},e.prototype.preloadLocaleData=function(e,t){return void 0===t&&(t=[]),dt.localize({messages:e,widgets:t})},e.prototype.destroyAll=function(){var e;for(e in this.store.destroy(),this)delete this[e]},t([ae(G),n("design:paramtypes",[N,z,Y,U,M])],e)}(),ht=function(){function e(e,t){this.config=e,this.context=t}return Object.defineProperty(e.prototype,"severityLevel",{get:function(){return this.config.getConfig(this.severityLevelConfigKey)||X.None},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"severityLevelConfigKey",{get:function(){return this.config.createKey(J.SeverityLevel,this.context)},enumerable:!1,configurable:!0}),e}(),yt=function(r){function a(e,t,n){var i=r.call(this)||this;return i.context=e,i.config=t,i.labels=n,i}return e(a,r),a.create=function(e,t,r,n){return e.createLogger(r.value,"@id=".concat(t.value),"@namespace=".concat(n&&n.value))},a.prototype.debug=function(e){var t,r=[];for(t=1;t<arguments.length;t++)r[t-1]=arguments[t];this.hasSeverityLevel(X.Debug)&&this.call.apply(this,o(["debug",e],i(r),!1))},a.prototype.error=function(e){var t,r=[];for(t=1;t<arguments.length;t++)r[t-1]=arguments[t];this.hasSeverityLevel(X.Errors)&&this.call.apply(this,o(["error",e],i(r),!1))},a.prototype.info=function(e){var t,r=[];for(t=1;t<arguments.length;t++)r[t-1]=arguments[t];this.hasSeverityLevel(X.Info)&&this.call.apply(this,o(["info",e],i(r),!1))},a.prototype.log=function(e){var t,r=[];for(t=1;t<arguments.length;t++)r[t-1]=arguments[t];this.hasSeverityLevel(X.Logs)&&this.call.apply(this,o(["log",e],i(r),!1))},a.prototype.warn=function(e){var t,r=[];for(t=1;t<arguments.length;t++)r[t-1]=arguments[t];this.hasSeverityLevel(X.Warnings)&&this.call.apply(this,o(["warn",e],i(r),!1))},a.prototype.clear=function(){O.window.console.clear()},a.prototype.hasSeverityLevel=function(e){return(this.config.severityLevel&e)===e},a.prototype.call=function(e,t){var r,n,a,s=[];for(n=2;n<arguments.length;n++)s[n-2]=arguments[n];a=0===this.labels.length?["[%s]\n",this.context]:["[%s], labels=[%O]\n",this.context,this.labels],(r=O.window.console)[e].apply(r,o([],i(o(o(o([],i(a),!1),[t],!1),i(s),!1)),!1))},t([ee,n("design:type",Function),n("design:paramtypes",[U,ye,ge,ve]),n("design:returntype",void 0)],a,"create",null),t([ae(V,{withFactory:a.create}),n("design:paramtypes",[String,ht,Array])],a)}(V),gt=function(r){function i(e){var t=r.call(this)||this;return t.config=e,t}return e(i,r),i.prototype.createLogger=function(e){var t,r=[];for(t=1;t<arguments.length;t++)r[t-1]=arguments[t];return new yt(e,new ht(this.config,e),r)},t([ae(U),n("design:paramtypes",[z])],i)}(U),vt=function(r){function n(){return null!==r&&r.apply(this,arguments)||this}return e(n,r),n.prototype.createParamsProvider=function(e){return new bt(e)},t([ae(je)],n)}(je),bt=function(t){function r(e){var r=t.call(this)||this;return r.instanceProvider=e,r}return e(r,t),Object.defineProperty(r.prototype,"props",{get:function(){return this.instanceProvider()},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"events",{get:function(){return this.instanceProvider()},enumerable:!1,configurable:!0}),r}(Ee),mt=function(r){function a(e,t){var n=r.call(this)||this;return n.id=e,n.storeServices=t,n.initialized=!1,n.listenToAll=!1,n.unsubscribers=[],n.dispatch=function(e){return n.store.dispatch(e)},n.subscribe=function(e){var t=n.store.subscribe(e);return n.unsubscribers=o(o([],i(n.unsubscribers),!1),[t],!1),function(){n.unsubscribers=n.unsubscribers.filter((function(e){return e!==t})),t()}},n}return e(a,r),a.prototype[Symbol.observable]=function(){throw new Error("Method not implemented.")},a.prototype.init=function(){this.store=this.createStore(),this.unregister=this.storeServices.registerStore(this),this.initialized=!0},a.prototype.createStore=function(){var e=xe.compose;return"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__&&(e=window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__({name:this.id.value})),(0,xe.legacy_createStore)((function(e){return e}),e((0,xe.applyMiddleware)(Ae(this))))},a.prototype.getState=function(){return this.store?this.store.getState():null},a.prototype.replaceReducer=function(e){this.store.replaceReducer(e)},a.prototype.notifyActionListener=function(e){this.storeServices.notifyActionListener(e)},a.prototype.destroy=function(){this.unsubscribers.forEach((function(e){return e()})),this.unsubscribers=[],this.unregister()},t([ae(H),n("design:paramtypes",[ye,Y])],a)}(H),wt=function(r){function i(e){var t=r.call(this)||this;return t.stream=e,t.globalActionListeners=[],t.stores=[],t.state={},t.getState=function(){return t.state},t.dispatch=function(e){var r=j({},e),n=r.meta||{};return r.meta=j(j({},n),{broadcasted:!0,origin:n.source||""}),t.stores.forEach((function(e){return e.dispatch(r)})),e},t}return e(i,r),Object.defineProperty(i.prototype,"id",{get:function(){return"ReduxStoreServices"},enumerable:!1,configurable:!0}),i.prototype.createStore=function(e){return new mt(new ye(e),this)},i.prototype.createGlobalActionListener=function(e,t){var r,n=this;return void 0===t&&(t={}),r=j(j({},{showBroadcasted:!1}),t),this.globalActionListeners.push({listener:e,params:r}),function(){n.globalActionListeners=n.globalActionListeners.filter((function(t){return t.listener!==e}))}},i.prototype.tryInvokeActionListeners=function(e){try{this.globalActionListeners.forEach((function(t){(e.meta.broadcasted&&t.params.showBroadcasted||!e.meta.broadcasted)&&t.listener(e)}))}catch(t){this.stream.sendError(D.REDUX,t)}},i.prototype.registerStore=function(e){var t,r=this;return e.subscribe((function(){return r.state[e.id.value]=e.getState()})),t=new Ot(e,this,this.stream),this.stores.push(t),function(){r.stores=r.stores.filter((function(e){return e!==t})),delete r.state[e.id.value]}},i.prototype.notifyActionListener=function(e){this.tryInvokeActionListeners(e)},i.prototype.replaceReducer=function(e){},i.prototype.destroy=function(){},i.prototype.destroyAll=function(){this.stores.forEach((function(e){e.store.destroy()})),this.stores=[],this.globalActionListeners=[],this.state={}},t([ae(Y),n("design:paramtypes",[M])],i)}(Y),Ot=function(){function e(e,t,r){var n=this;this.store=e,this.originalStoreDispatch=e.dispatch.bind(e),e.dispatch=function(e){var i,o=n.originalStoreDispatch(e);try{(i=j({},e)).meta=j(j({},i.meta||{}),{source:n.store.id.value}),t.dispatch(i)}catch(a){r.sendError(D.REDUX,a)}return o}}return Object.defineProperty(e.prototype,"id",{get:function(){return this.store.id.value},enumerable:!1,configurable:!0}),e.prototype.dispatch=function(e){return this.shouldInvokeOriginalDispatch(e)?this.originalStoreDispatch(e):e},e.prototype.shouldInvokeOriginalDispatch=function(e){return this.store.listenToAll&&e.meta.source!==this.store.id.value||Array.isArray(e.meta.target)&&e.meta.target.indexOf(this.store.id.value)>=0},e}(),Pt=function(){function e(e,t){this.config=e,this.logger=t,this.registry={}}return e.prototype.init=function(){this.registerStaticModules()},e.prototype.reset=function(){this.registry={},this.registerStaticModules()},e.prototype.load=function(e){return Promise.resolve(void 0)},e.prototype.registerStaticModules=function(){var e=this,t=this.config.getConfig(be.StaticWidgetMappings);Object.keys(t).forEach((function(r){e.registerStaticModule(r,t[r])}))},e.prototype.registerStaticModule=function(e,t){this.registry[e]=j(j({},function(e){return{namespace:(t=e,void 0!==t.namespace?e.namespace:void 0),factory:d(e)?e:p(e)?e.factory:void 0,url:h(e)?e:"function"!=typeof e&&e.url?e.url:void 0};var t}(t)),this.registry[e])},t([ae(K),n("design:paramtypes",[z,V])],e)}(),jt=function(){function e(e,t,r,n){this.moduleId=e,this.resolver=t,this.communicator=r,this.registry=n}return e.prototype.load=function(){var e=this;return new Promise((function(t){e.communicator.get(e.moduleId,e.resolvedUrl,t)}))},Object.defineProperty(e.prototype,"resolvedUrl",{get:function(){var e;return void 0!==this.moduleDefinitionUrl?this.moduleDefinitionUrl:(e=this.resolver.module(this.moduleId),this.resolver.url(e))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"moduleDefinitionUrl",{get:function(){return this.registry[this.moduleId]&&this.registry[this.moduleId].url},enumerable:!1,configurable:!0}),e}(),Et=function(){function e(e,t,r){this.moduleId=e,this.registry=t,this.loader=r}return e.prototype.resolve=function(){var e=this;return new Promise((function(t){void 0!==e.factory?Promise.resolve(e.factory.call(null)).then(t):e.isGlobalScript?t(e.globalScript):e.loader.load().then(t)}))},Object.defineProperty(e.prototype,"factory",{get:function(){return this.registry[this.moduleId]&&this.registry[this.moduleId].factory},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isGlobalScript",{get:function(){return void 0!==this.globalScript},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"globalScript",{get:function(){return O.window[this.moduleId]||O.window[this.moduleIdAsGlobalVar]},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"moduleIdAsGlobalVar",{get:function(){return c(663)(this.moduleId)},enumerable:!1,configurable:!0}),e}(),St=function(){function e(e,t){this.config=e,this.moduleId=t}return Object.defineProperty(e.prototype,"widgetBundle",{get:function(){return this.config.getConfig(be.WidgetBundleName)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"versionDelimiter",{get:function(){return this.config.getConfig(be.VersionDelimiter)},enumerable:!1,configurable:!0}),e.prototype.resolve=function(){return this.resolveModulePath()},e.prototype.resolveModulePath=function(){return this.resolveBaseModulePath().concat("/",this.widgetBundle)},e.prototype.resolveBaseModulePath=function(){return this.hasTargetVersion()?this.module().concat("/",this.version()):this.module()},e.prototype.hasTargetVersion=function(){return void 0!==this.version()},e.prototype.version=function(){return this.moduleIdParts[1]},e.prototype.module=function(){return this.moduleIdParts[0]},Object.defineProperty(e.prototype,"moduleIdParts",{get:function(){return this.moduleId.split(this.versionDelimiter)},enumerable:!1,configurable:!0}),e}(),_t=function(i){function o(e,t){var r=i.call(this,e,t)||this;return r.useRequireJs=void 0!==O.window.bwtkRequireJS,r.requireContext="",r}return e(o,i),o.createLogger=function(e){return e.createLogger(Z.Loader)},Object.defineProperty(o.prototype,"inject",{get:function(){return this._inject||(this._inject=Inject.createContext().Inject)},enumerable:!1,configurable:!0}),o.prototype.init=function(){var e=this;i.prototype.init.call(this),this.useRequireJs?this.requireContext=this.config.getConfig(be.RequireContext,""):(this.inject.setModuleRoot(this.config.getConfig(be.RegistryPath)),this.inject.addModuleRule(/.*/,(function(t){return void 0!==(e.registry[t]&&e.registry[t].factory)?t:new St(e.config,t).resolve()})),this.inject.addFetchRule(/.*/,(function(t,r,n,i,o){var a=o.moduleId;new Et(a,e.registry,new jt(a,n,i,e.registry)).resolve().then((function(e){t(null,e)}))}))),this.config.setDefaultConfig(be.VersionDelimiter,"@"),this.config.setDefaultConfig(be.WidgetBundleName,"widget"),this.useRequireJs||this.config.onChange(be.RegistryPath,(function(t,r){return e.inject.setModuleRoot(r)}))},o.prototype.reset=function(){i.prototype.reset.call(this),this.useRequireJs||this._inject.reset()},o.prototype.load=function(e){var t=this,r=this.registry[e]||void 0;return this.useRequireJs&&r&&O.window.define(e,[],(function(){return(r.factory||function(){})()})),new Promise((function(n,i){(t.useRequireJs?t.requireContext.length?O.window.bwtkRequireJS[t.requireContext].require:O.window.bwtkRequireJS.require:t.inject.require)([e],(function(i){var o,a;t.logger.info('INFO - Widget loaded: "'.concat(e,'"')),o=i.default||i,void 0!==(a=r&&r.namespace)&&(o.prototype=j(j({},o.prototype),{namespace:a})),n(o)}))}))},t([ee,n("design:type",Function),n("design:paramtypes",[U]),n("design:returntype",void 0)],o,"createLogger",null),t([ae(K),r(1,ne(o.createLogger)),n("design:paramtypes",[z,V])],o)}(Pt),Ct=function(){function e(){var e=this;this.executeCallback=function(t,r,n){try{r.call(null,t)}catch(i){void 0!==n?n.call(null,i):e.sendError(D.STREAM,i)}},this.subscribeAll=function(t){var r=e.subject.subscribe((function(r){return e.executeCallback(r,t)})),n=e.errorStream.subscribe((function(r){return e.executeCallback(r,t)}));return function(){r.unsubscribe(),n.unsubscribe()}}}return e.prototype.init=function(){this.subject=new Se.Subject,this.errorStream=new Se.ReplaySubject(10)},e.prototype.subscribe=function(e,t){var r,n,i,o=this;return"function"==typeof e?this.subject.subscribe((function(t){return o.executeCallback(t,e)})).unsubscribe:(n="string"==typeof e?((r={})[e]=t||function(){},r):e,i=Object.keys(n).map((function(e){return o.subject.pipe((0,Se.filter)((function(t){return t.type===e}))).subscribe((function(t){return o.executeCallback(t,n[e])}))})),function(){return i.forEach((function(e){return e.unsubscribe()}))})},e.prototype.subscribeError=function(e){var t=this,r=this.errorStream.subscribe((function(r){return t.executeCallback(r,e,(function(e){}))}));return r.unsubscribe.bind(r)},e.prototype.send=function(e,t){this.subject.next({type:e,payload:t})},e.prototype.sendError=function(e,t,r){var n;e instanceof Error?n={type:D.GENERIC,payload:{error:e},error:!0}:(n={type:e,error:!0},void 0!==t&&(n.payload={error:t})),r&&(n.payload=n.payload?j(j({},n.payload),{widget:r}):{widget:r}),this.errorStream.next(n)},Object.defineProperty(e.prototype,"errors",{get:function(){var e=this;return{subscribe:function(t){return e.subscribeError(t)},send:function(t,r){return e.sendError(t,r)}}},enumerable:!1,configurable:!0}),e.prototype.unsubscribe=function(){this.subject.unsubscribe(),this.errorStream.unsubscribe()},e.prototype.destroy=function(){},t([ae(M)],e)}(),Lt=(new tt).appendProviders(new et(Ct).providers).appendProviders(new et(it).providers).appendProviders(new et(at).providers).appendProviders(new et(pt).providers).appendProviders(new et(gt).providers).appendProviders(new et(vt).providers).appendProviders(new et(wt).providers).appendProviders(new et(_t).providers).toArray(),Rt=(new tt).appendProviders(new et(ye).providers).appendProviders(new et(ve).providers).appendProviders(new et(yt).providers).appendProviders(new et(mt).providers).appendProviders(new et(ft).providers).removeLowPriorityProvidersFrom(Lt).toArray(),It=function(){function e(e,t,r){this.globalInjector=e,this.widgetName=t,this.widgetConstructor=r}return e.prototype.createWidgetInstance=function(){return this.instance=this.injector.get(this.widgetConstructor),this.instance},Object.defineProperty(e.prototype,"injector",{get:function(){return this._injector||(this._injector=this.globalInjector.createChild(this.providers))},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"providers",{get:function(){var e=this;return(new tt).appendProviders(Rt).appendProviders([et.provide(ge,(function(){return new ge(e.widgetName)}),!0),et.provide(fe,(function(){return e.widgetConstructor}),!0),et.provide(Ee,new Qe((function(t){return t.createParamsProvider((function(){return e.instance}))}),[je]).factory,!0)]).appendProviders(new et(this.widgetConstructor).providers).removeLowPriorityProvidersFrom(Lt).toArray()},enumerable:!1,configurable:!0}),e}(),kt=function(){function e(e){this.injector=e}return e.prototype.createInstance=function(e,t){return new It(this.injector,e,t).createWidgetInstance()},e}(),Mt=function(t){function r(e){var r=t.call(this)||this;return r.interceptors=new Map,r.baseinjector=r.injector=function(e){var t=new We.Injector((new tt).appendProviders(Lt).appendProviders(new et(e).providers).toArray());return t.createChild(y(t))}(e),r}return e(r,t),Object.defineProperty(r.prototype,"di",{get:function(){return this.injector.get(q)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"loader",{get:function(){return this.injector.get(K)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"store",{get:function(){return this.injector.get(Y)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"stream",{get:function(){return this.injector.get(M)},enumerable:!1,configurable:!0}),r.prototype.getService=function(e){return this.injector.get(e)},r.prototype.addServiceInterceptor=function(e,t,r){var n=this,i=Array.isArray(t)?t:["*"],o=r||t;return i.forEach((function(t){return n.appendInterceptor(e,t,o)})),this.rebuildInjectorWithInterceptors(),function(){i.forEach((function(t){return n.getMembersInterceptors(e).set(t,n.getInterceptors(e,t).filter((function(e){return e!==o})))})),n.rebuildInjectorWithInterceptors()}},r.prototype.appendInterceptor=function(e,t,r){this.interceptors.set(e,this.createMemberInterceptorMap(e,t,r))},r.prototype.createMemberInterceptorMap=function(e,t,r){return this.getMembersInterceptors(e).set(t,o(o([],i(this.getInterceptors(e,t)),!1),[r],!1))},r.prototype.getInterceptors=function(e,t){return this.getMembersInterceptors(e).get(t)||[]},r.prototype.getMembersInterceptors=function(e){return this.interceptors.get(e)||new Map},r.prototype.rebuildInjectorWithInterceptors=function(){var e=this,t=[];this.interceptors.forEach((function(r,n){t=o([],i(e.createProxyBindings(n,r)),!1)})),this.injector=this.injector.createChild(t),this.injector=this.injector.createChild(o([],i(y(this.injector)),!1))},r.prototype.createProxyBindings=function(e,t){var r=this.baseinjector.get(e);return g(e,(function(){return r}))},r}(x),At=O.window,Dt="function"==typeof At.bwtkEventListener?At.bwtkEventListener:function(){},xt=function(){function e(e){this.config=e,this.optionKeys={localization:{webServicesPath:me.LocalizationServicesPath,defaultLocale:me.DefaultLocale,brand:me.Brand,timeout:me.Timeout,disableNetwork:me.DebugDisableNetwork,showKeys:me.DebugShowKeys},loader:{registryPath:be.RegistryPath,staticWidgetMappings:be.StaticWidgetMappings},logger:{severityLevel:J.SeverityLevel}}}return e.prototype.setConfig=function(e,t){var r,n=i(e.split("."),2),o=n[0],a=n[1];r=o&&o in this.optionKeys&&a&&a in this.optionKeys[o]?this.optionKeys[o][a]:e,this.config.setConfig(r,t)},e}(),Ft=function(e,t){var r,n,i,o;void 0===e&&(e={}),Tt(O.window,t),r=j({"loader.registryPath":"","localization.webServicesPath":"","loader.staticWidgetMappings":{}},e),n=new Mt(_t),x.setInstanceProvider((function(){return n})),x.instance.getService(Z.EventStream).init(),(i=x.instance.getService(Z.Config)).init(),"string"==typeof t&&t.length&&i.setDefaultConfig(be.RequireContext,t),o=new xt(i),Object.keys(r).forEach((function(e){return o.setConfig(e,r[e])})),x.instance.getService(Z.Loader).init(),x.instance.getService(Z.Localization).init(),void 0!==O.window.__BWTK_DEVTOOLS_EXTENSION__&&O.window.__BWTK_DEVTOOLS_EXTENSION__.connect(void 0),Dt("INIT_END")},Tt=function(e,t){if(void 0===e.bwtkRequireJS)e.ReactIntl;else{var r=t?e.bwtkRequireJS[t]:e.bwtkRequireJS;if(void 0===r)return;r.require(["react-intl"],(function(t){t&&(e.ReactIntl=t)}))}},Nt=!1,zt=function(){Nt||(Nt=!0,void 0!==O.window.__BWTK_DEVTOOLS_EXTENSION__&&O.window.__BWTK_DEVTOOLS_EXTENSION__.disconnect(),lt.forEach((function(e,t){e.unmount(),t.remove()})),lt.clear(),x.instance.getService(Z.EventStream).unsubscribe(),x.instance.getService(Z.Localization).destroyAll(),x.instance.getService(Z.Store).destroyAll())},b.combineEpics,m.FormattedMessage,w.connect,(Wt=O.window)._Inject&&(Wt._Inject(Wt),Wt._Inject=void 0,Wt.Inject.disableAMD())})(),u})(),"object"==typeof exports&&"object"==typeof module?module.exports=factory(require("redux-observable"),require("react-intl"),require("react-redux"),require("redux-actions"),require("react"),require("react-dom"),require("rxjs"),require("redux")):"function"==typeof define&&define.amd?define("bwtk",["redux-observable","react-intl","react-redux","redux-actions","react","react-dom","rxjs","redux"],factory):"object"==typeof exports?exports.bwtk=factory(require("redux-observable"),require("react-intl"),require("react-redux"),require("redux-actions"),require("react"),require("react-dom"),require("rxjs"),require("redux")):root.bwtk=factory(root.ReduxObservable,root.ReactIntl,root.ReactRedux,root.ReduxActions,root.React,root.ReactDOM,root.rxjs,root.Redux);